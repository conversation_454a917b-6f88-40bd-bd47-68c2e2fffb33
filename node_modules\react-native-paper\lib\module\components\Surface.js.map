{"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "useInternalTheme", "overlay", "isAnimatedValue", "shadow", "forwardRef", "splitStyles", "MD2Surface", "style", "theme", "overrideTheme", "rest", "ref", "elevation", "flatten", "dark", "isDarkTheme", "mode", "colors", "createElement", "View", "_extends", "backgroundColor", "surface", "outerLayerStyleProperties", "shadowColor", "iOSShadowOutputRanges", "shadowOpacity", "height", "shadowRadius", "inputRange", "getStyleForShadowLayer", "layer", "interpolate", "outputRange", "extrapolate", "shadowOffset", "width", "SurfaceIOS", "testID", "children", "container", "props", "outerLayerViewStyles", "innerLayerViewStyles", "useMemo", "flattenedStyles", "filteredStyles", "outerLayerStyles", "borderRadiusStyles", "includes", "startsWith", "endsWith", "process", "env", "NODE_ENV", "overflow", "console", "warn", "bgColor", "isElevated", "flex", "undefined", "Surface", "overridenTheme", "isV3", "_colors$elevation2", "map", "_colors$elevation", "OS", "pointerEvents", "elevationLevel", "getElevationAndroid", "margin", "padding", "transform", "borderRadius", "sharedStyle"], "sourceRoot": "../../../src", "sources": ["components/Surface.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,QAAQ,EAGRC,UAAU,QAGL,cAAc;AAErB,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAOC,OAAO,IAAIC,eAAe,QAAQ,mBAAmB;AAC5D,OAAOC,MAAM,MAAM,kBAAkB;AAErC,SAASC,UAAU,QAAQ,qBAAqB;AAChD,SAASC,WAAW,QAAQ,sBAAsB;AA2ClD,MAAMC,UAAU,GAAGF,UAAU,CAC3B,CAAC;EAAEG,KAAK;EAAEC,KAAK,EAAEC,aAAa;EAAE,GAAGC;AAA+B,CAAC,EAAEC,GAAG,KAAK;EAC3E,MAAM;IAAEC,SAAS,GAAG;EAAE,CAAC,GAAIb,UAAU,CAACc,OAAO,CAACN,KAAK,CAAC,IAAI,CAAC,CAAe;EACxE,MAAM;IAAEO,IAAI,EAAEC,WAAW;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGjB,gBAAgB,CAACS,aAAa,CAAC;EAE3E,oBACEb,KAAA,CAAAsB,aAAA,CAACrB,QAAQ,CAACsB,IAAI,EAAAC,QAAA;IACZT,GAAG,EAAEA;EAAI,GACLD,IAAI;IACRH,KAAK,EAAE,CACL;MACEc,eAAe,EACbN,WAAW,IAAIC,IAAI,KAAK,UAAU,GAC9Bf,OAAO,CAACW,SAAS,EAAEK,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK,OAAO,CAAC,GACnCL,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEK;IAChB,CAAC,EACDV,SAAS,GAAGT,MAAM,CAACS,SAAS,CAAC,GAAG,IAAI,EACpCL,KAAK;EACL,EACH,CAAC;AAEN,CACF,CAAC;AAED,MAAMgB,yBAA8C,GAAG,CACrD,UAAU,EACV,WAAW,EACX,KAAK,EACL,OAAO,EACP,QAAQ,EACR,MAAM,EACN,OAAO,EACP,KAAK,EACL,MAAM,EACN,YAAY,EACZ,UAAU,EACV,OAAO,EACP,QAAQ,EACR,WAAW,EACX,SAAS,CACV;AAED,MAAMC,WAAW,GAAG,MAAM;AAC1B,MAAMC,qBAAqB,GAAG,CAC5B;EACEC,aAAa,EAAE,IAAI;EACnBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE;AACnC,CAAC,EACD;EACEF,aAAa,EAAE,GAAG;EAClBC,MAAM,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC1BC,YAAY,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC;AACjC,CAAC,CACF;AACD,MAAMC,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;AACrC,SAASC,sBAAsBA,CAC7BlB,SAAoB,EACpBmB,KAAY,EACgC;EAC5C,IAAI7B,eAAe,CAACU,SAAS,CAAC,EAAE;IAC9B,OAAO;MACLY,WAAW;MACXE,aAAa,EAAEd,SAAS,CAACoB,WAAW,CAAC;QACnCH,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBI,WAAW,EAAE,CAAC,CAAC,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACL,aAAa,CAAC;QAC5DQ,WAAW,EAAE;MACf,CAAC,CAAC;MACFC,YAAY,EAAE;QACZC,KAAK,EAAE,CAAC;QACRT,MAAM,EAAEf,SAAS,CAACoB,WAAW,CAAC;UAC5BH,UAAU;UACVI,WAAW,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACJ;QAC5C,CAAC;MACH,CAAC;MACDC,YAAY,EAAEhB,SAAS,CAACoB,WAAW,CAAC;QAClCH,UAAU;QACVI,WAAW,EAAER,qBAAqB,CAACM,KAAK,CAAC,CAACH;MAC5C,CAAC;IACH,CAAC;EACH;EAEA,OAAO;IACLJ,WAAW;IACXE,aAAa,EAAEd,SAAS,GAAGa,qBAAqB,CAACM,KAAK,CAAC,CAACL,aAAa,GAAG,CAAC;IACzES,YAAY,EAAE;MACZC,KAAK,EAAE,CAAC;MACRT,MAAM,EAAEF,qBAAqB,CAACM,KAAK,CAAC,CAACJ,MAAM,CAACf,SAAS;IACvD,CAAC;IACDgB,YAAY,EAAEH,qBAAqB,CAACM,KAAK,CAAC,CAACH,YAAY,CAAChB,SAAS;EACnE,CAAC;AACH;AAEA,MAAMyB,UAAU,GAAGjC,UAAU,CAO3B,CACE;EACEQ,SAAS;EACTL,KAAK;EACLc,eAAe;EACfiB,MAAM;EACNC,QAAQ;EACRvB,IAAI,GAAG,UAAU;EACjBwB,SAAS;EACT,GAAGC;AACL,CAAC,EACD9B,GAAG,KACA;EACH,MAAM,CAAC+B,oBAAoB,EAAEC,oBAAoB,CAAC,GAAG/C,KAAK,CAACgD,OAAO,CAAC,MAAM;IACvE,MAAMC,eAAe,GAAI9C,UAAU,CAACc,OAAO,CAACN,KAAK,CAAC,IAAI,CAAC,CAAe;IAEtE,MAAM,CAACuC,cAAc,EAAEC,gBAAgB,EAAEC,kBAAkB,CAAC,GAC1D3C,WAAW,CACTwC,eAAe,EACdtC,KAAK,IACJgB,yBAAyB,CAAC0B,QAAQ,CAAC1C,KAAK,CAAC,IACzCA,KAAK,CAAC2C,UAAU,CAAC,QAAQ,CAAC,EAC3B3C,KAAK,IAAKA,KAAK,CAAC2C,UAAU,CAAC,QAAQ,CAAC,IAAI3C,KAAK,CAAC4C,QAAQ,CAAC,QAAQ,CAClE,CAAC;IAEH,IACEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IACrCR,cAAc,CAACS,QAAQ,KAAK,QAAQ,IACpC3C,SAAS,KAAK,CAAC,EACf;MACA4C,OAAO,CAACC,IAAI,CACV,uKACF,CAAC;IACH;IAEA,MAAMC,OAAO,GAAGb,eAAe,CAACxB,eAAe,IAAIA,eAAe;IAElE,MAAMsC,UAAU,GAAG3C,IAAI,KAAK,UAAU;IAEtC,MAAM0B,oBAAoB,GAAG;MAC3B,IAAIiB,UAAU,IAAI7B,sBAAsB,CAAClB,SAAS,EAAE,CAAC,CAAC,CAAC;MACvD,GAAGmC,gBAAgB;MACnB,GAAGC,kBAAkB;MACrB3B,eAAe,EAAEqC;IACnB,CAAC;IAED,MAAMf,oBAAoB,GAAG;MAC3B,IAAIgB,UAAU,IAAI7B,sBAAsB,CAAClB,SAAS,EAAE,CAAC,CAAC,CAAC;MACvD,GAAGkC,cAAc;MACjB,GAAGE,kBAAkB;MACrBY,IAAI,EACFf,eAAe,CAAClB,MAAM,IAAK,CAACa,SAAS,IAAIK,eAAe,CAACe,IAAK,GAC1D,CAAC,GACDC,SAAS;MACfxC,eAAe,EAAEqC;IACnB,CAAC;IAED,OAAO,CAAChB,oBAAoB,EAAEC,oBAAoB,CAAC;EACrD,CAAC,EAAE,CAACpC,KAAK,EAAEK,SAAS,EAAES,eAAe,EAAEL,IAAI,EAAEwB,SAAS,CAAC,CAAC;EAExD,oBACE5C,KAAA,CAAAsB,aAAA,CAACrB,QAAQ,CAACsB,IAAI;IACZR,GAAG,EAAEA,GAAI;IACTJ,KAAK,EAAEmC,oBAAqB;IAC5BJ,MAAM,EAAE,GAAGA,MAAM;EAAe,gBAEhC1C,KAAA,CAAAsB,aAAA,CAACrB,QAAQ,CAACsB,IAAI,EAAAC,QAAA,KAAKqB,KAAK;IAAElC,KAAK,EAAEoC,oBAAqB;IAACL,MAAM,EAAEA;EAAO,IACnEC,QACY,CACF,CAAC;AAEpB,CACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuB,OAAO,GAAG1D,UAAU,CACxB,CACE;EACEQ,SAAS,GAAG,CAAC;EACb2B,QAAQ;EACR/B,KAAK,EAAEuD,cAAc;EACrBxD,KAAK;EACL+B,MAAM,GAAG,SAAS;EAClBtB,IAAI,GAAG,UAAU;EACjB,GAAGyB;AACE,CAAC,EACR9B,GAAG,KACA;EACH,MAAMH,KAAK,GAAGR,gBAAgB,CAAC+D,cAAc,CAAC;EAE9C,IAAI,CAACvD,KAAK,CAACwD,IAAI,EACb,oBACEpE,KAAA,CAAAsB,aAAA,CAACZ,UAAU,EAAAc,QAAA,KAAKqB,KAAK;IAAEjC,KAAK,EAAEA,KAAM;IAACD,KAAK,EAAEA,KAAM;IAACI,GAAG,EAAEA;EAAI,IACzD4B,QACS,CAAC;EAGjB,MAAM;IAAEtB;EAAO,CAAC,GAAGT,KAAK;EAExB,MAAMqB,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAErC,MAAMR,eAAe,GAAG,CAAC4C,kBAAA,IAAM;IAC7B,IAAI/D,eAAe,CAACU,SAAS,CAAC,EAAE;MAC9B,OAAOA,SAAS,CAACoB,WAAW,CAAC;QAC3BH,UAAU;QACVI,WAAW,EAAEJ,UAAU,CAACqC,GAAG,CAAEtD,SAAS,IAAK;UAAA,IAAAuD,iBAAA;UACzC,QAAAA,iBAAA,GAAOlD,MAAM,CAACL,SAAS,cAAAuD,iBAAA,uBAAhBA,iBAAA,CAAmB,QAAQvD,SAAS,EAAkB,CAAC;QAChE,CAAC;MACH,CAAC,CAAC;IACJ;IAEA,QAAAqD,kBAAA,GAAOhD,MAAM,CAACL,SAAS,cAAAqD,kBAAA,uBAAhBA,kBAAA,CAAmB,QAAQrD,SAAS,EAAE,CAAC;EAChD,CAAC,EAAE,CAAC;EAEJ,MAAM+C,UAAU,GAAG3C,IAAI,KAAK,UAAU;EAEtC,IAAIlB,QAAQ,CAACsE,EAAE,KAAK,KAAK,EAAE;IACzB,MAAM;MAAEC,aAAa,GAAG;IAAO,CAAC,GAAG5B,KAAK;IACxC,oBACE7C,KAAA,CAAAsB,aAAA,CAACrB,QAAQ,CAACsB,IAAI,EAAAC,QAAA,KACRqB,KAAK;MACT4B,aAAa,EAAEA,aAAc;MAC7B1D,GAAG,EAAEA,GAAI;MACT2B,MAAM,EAAEA,MAAO;MACf/B,KAAK,EAAE,CACL;QAAEc;MAAgB,CAAC,EACnBT,SAAS,IAAI+C,UAAU,GAAGxD,MAAM,CAACS,SAAS,EAAEJ,KAAK,CAACwD,IAAI,CAAC,GAAG,IAAI,EAC9DzD,KAAK;IACL,IAEDgC,QACY,CAAC;EAEpB;EAEA,IAAIzC,QAAQ,CAACsE,EAAE,KAAK,SAAS,EAAE;IAC7B,MAAME,cAAc,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;IAE3C,MAAMC,mBAAmB,GAAGA,CAAA,KAAM;MAChC,IAAIrE,eAAe,CAACU,SAAS,CAAC,EAAE;QAC9B,OAAOA,SAAS,CAACoB,WAAW,CAAC;UAC3BH,UAAU;UACVI,WAAW,EAAEqC;QACf,CAAC,CAAC;MACJ;MAEA,OAAOA,cAAc,CAAC1D,SAAS,CAAC;IAClC,CAAC;IAED,MAAM;MAAE4D,MAAM;MAAEC,OAAO;MAAEC,SAAS;MAAEC;IAAa,CAAC,GAAI5E,UAAU,CAACc,OAAO,CACtEN,KACF,CAAC,IAAI,CAAC,CAAe;IAErB,MAAMwC,gBAAgB,GAAG;MAAEyB,MAAM;MAAEC,OAAO;MAAEC,SAAS;MAAEC;IAAa,CAAC;IACrE,MAAMC,WAAW,GAAG,CAAC;MAAEvD;IAAgB,CAAC,EAAEd,KAAK,CAAC;IAEhD,oBACEX,KAAA,CAAAsB,aAAA,CAACrB,QAAQ,CAACsB,IAAI,EAAAC,QAAA,KACRqB,KAAK;MACTH,MAAM,EAAEA,MAAO;MACf3B,GAAG,EAAEA,GAAI;MACTJ,KAAK,EAAE,CACL;QACEc,eAAe;QACfqD;MACF,CAAC,EACD3B,gBAAgB,EAChB6B,WAAW,EACXjB,UAAU,IAAI;QACZ/C,SAAS,EAAE2D,mBAAmB,CAAC;MACjC,CAAC;IACD,IAEDhC,QACY,CAAC;EAEpB;EAEA,oBACE3C,KAAA,CAAAsB,aAAA,CAACmB,UAAU,EAAAjB,QAAA,KACLqB,KAAK;IACT9B,GAAG,EAAEA,GAAI;IACTC,SAAS,EAAEA,SAAU;IACrBS,eAAe,EAAEA,eAAgB;IACjCd,KAAK,EAAEA,KAAM;IACb+B,MAAM,EAAEA,MAAO;IACftB,IAAI,EAAEA;EAAK,IAEVuB,QACS,CAAC;AAEjB,CACF,CAAC;AAED,eAAeuB,OAAO", "ignoreList": []}
{"version": 3, "sources": ["needsToReattach.ts"], "names": ["needsToReattach", "preparedGesture", "newGestures", "length", "attachedGestures", "i", "handler<PERSON>ame", "shouldUseReanimated"], "mappings": "AAGA;AACA;AACA;AACA;AACA,OAAO,SAASA,eAAT,CACLC,eADK,EAELC,WAFK,EAGL;AACA,MAAIA,WAAW,CAACC,MAAZ,KAAuBF,eAAe,CAACG,gBAAhB,CAAiCD,MAA5D,EAAoE;AAClE,WAAO,IAAP;AACD;;AACD,OAAK,IAAIE,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,WAAW,CAACC,MAAhC,EAAwCE,CAAC,EAAzC,EAA6C;AAC3C,QACEH,WAAW,CAACG,CAAD,CAAX,CAAeC,WAAf,KACEL,eAAe,CAACG,gBAAhB,CAAiCC,CAAjC,EAAoCC,WADtC,IAEAJ,WAAW,CAACG,CAAD,CAAX,CAAeE,mBAAf,KACEN,eAAe,CAACG,gBAAhB,CAAiCC,CAAjC,EAAoCE,mBAJxC,EAKE;AACA,aAAO,IAAP;AACD;AACF;;AAED,SAAO,KAAP;AACD", "sourcesContent": ["import { GestureType } from '../gesture';\nimport { AttachedGestureState } from './types';\n\n// Checks whether the gesture should be reattached to the view, this will happen when:\n// - The number of gestures in the preparedGesture is different than the number of gestures in the gesture\n// - The handlerName is different in any of the gestures\n// - At least one of the gestures changed the thread it runs on\nexport function needsToReattach(\n  preparedGesture: AttachedGestureState,\n  newGestures: GestureType[]\n) {\n  if (newGestures.length !== preparedGesture.attachedGestures.length) {\n    return true;\n  }\n  for (let i = 0; i < newGestures.length; i++) {\n    if (\n      newGestures[i].handlerName !==\n        preparedGesture.attachedGestures[i].handlerName ||\n      newGestures[i].shouldUseReanimated !==\n        preparedGesture.attachedGestures[i].shouldUseReanimated\n    ) {\n      return true;\n    }\n  }\n\n  return false;\n}\n"]}
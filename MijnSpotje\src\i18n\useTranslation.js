import React, { createContext, useContext, useState, useEffect } from 'react';
// Expo localization removed for Expo Go compatibility
// import * as Localization from 'expo-localization';
import { translations } from './translations';

// Create i18n context
const I18nContext = createContext();

// Available languages
export const LANGUAGES = {
  nl: 'nl',
  en: 'en', 
  de: 'de',
  es: 'es',
};

// Default language is Dutch since app is "Mijn Spotje"
const DEFAULT_LANGUAGE = LANGUAGES.nl;

// Get device language with fallback (Expo Go compatible)
const getDeviceLanguage = () => {
  // For Expo Go compatibility, we'll default to Dutch
  // In production, this would use Localization.locale
  try {
    // Try to get browser language if available (for web)
    if (typeof navigator !== 'undefined' && navigator.language) {
      const languageCode = navigator.language.split('-')[0];
      if (Object.values(LANGUAGES).includes(languageCode)) {
        return languageCode;
      }
    }
  } catch (error) {
    console.log('Could not detect device language, using default');
  }

  return DEFAULT_LANGUAGE;
};

// I18n Provider Component
export const I18nProvider = ({ children }) => {
  const [currentLanguage, setCurrentLanguage] = useState(DEFAULT_LANGUAGE);
  const [isLoading, setIsLoading] = useState(true);

  // Initialize language on app start
  useEffect(() => {
    const initializeLanguage = async () => {
      try {
        // In production, this would load from AsyncStorage
        // For now, use device language or default
        const deviceLang = getDeviceLanguage();
        setCurrentLanguage(deviceLang);
      } catch (error) {
        console.error('Error initializing language:', error);
        setCurrentLanguage(DEFAULT_LANGUAGE);
      } finally {
        setIsLoading(false);
      }
    };

    initializeLanguage();
  }, []);

  // Change language function
  const changeLanguage = async (newLanguage) => {
    if (Object.values(LANGUAGES).includes(newLanguage)) {
      setCurrentLanguage(newLanguage);
      // In production, this would save to AsyncStorage
      console.log('Language changed to:', newLanguage);
    }
  };

  // Get translation function
  const t = (key, params = {}) => {
    const keys = key.split('.');
    let translation = translations[currentLanguage];

    // Navigate through nested keys
    for (const k of keys) {
      if (translation && typeof translation === 'object' && k in translation) {
        translation = translation[k];
      } else {
        // Fallback to English if key not found
        translation = translations[LANGUAGES.en];
        for (const fallbackKey of keys) {
          if (translation && typeof translation === 'object' && fallbackKey in translation) {
            translation = translation[fallbackKey];
          } else {
            // Final fallback to key itself
            console.warn(`Translation key not found: ${key}`);
            return key;
          }
        }
        break;
      }
    }

    // Handle string interpolation if needed
    if (typeof translation === 'string' && Object.keys(params).length > 0) {
      return translation.replace(/\{\{(\w+)\}\}/g, (match, paramKey) => {
        return params[paramKey] || match;
      });
    }

    return translation || key;
  };

  // Get current language info
  const getCurrentLanguageInfo = () => {
    return {
      code: currentLanguage,
      name: translations[currentLanguage]?.languages?.[currentLanguage] || currentLanguage,
      isRTL: false, // None of our supported languages are RTL
    };
  };

  // Get available languages
  const getAvailableLanguages = () => {
    return Object.keys(LANGUAGES).map(code => ({
      code,
      name: translations[code]?.languages?.[code] || code,
      nativeName: translations[code]?.languages?.[code] || code,
    }));
  };

  const value = {
    t,
    currentLanguage,
    changeLanguage,
    getCurrentLanguageInfo,
    getAvailableLanguages,
    isLoading,
    LANGUAGES,
  };

  return (
    <I18nContext.Provider value={value}>
      {children}
    </I18nContext.Provider>
  );
};

// Custom hook to use translations
export const useTranslation = () => {
  const context = useContext(I18nContext);
  
  if (!context) {
    throw new Error('useTranslation must be used within an I18nProvider');
  }
  
  return context;
};

// Helper function to get translation without hook (for static usage)
export const getTranslation = (key, language = DEFAULT_LANGUAGE) => {
  const keys = key.split('.');
  let translation = translations[language];

  for (const k of keys) {
    if (translation && typeof translation === 'object' && k in translation) {
      translation = translation[k];
    } else {
      return key;
    }
  }

  return translation || key;
};

export default useTranslation;

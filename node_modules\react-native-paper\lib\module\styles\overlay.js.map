{"version": 3, "names": ["Animated", "color", "MD2DarkTheme", "isAnimatedValue", "it", "Value", "overlay", "elevation", "surfaceColor", "_MD2DarkTheme$colors", "colors", "surface", "inputRange", "interpolate", "outputRange", "map", "calculateColor", "overlayTransparency", "elevationOverlayTransparency", "mix", "hex"], "sourceRoot": "../../../src", "sources": ["styles/overlay.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAEvC,OAAOC,KAAK,MAAM,OAAO;AAEzB,SAASC,YAAY,QAAQ,uBAAuB;AAEpD,OAAO,MAAMC,eAAe,GAC1BC,EAAqE,IAC5CA,EAAE,YAAYJ,QAAQ,CAACK,KAAK;AAEvD,eAAe,SAASC,OAAOA,CAC7BC,SAAY,EACZC,YAAoB,IAAAC,oBAAA,KAAAA,oBAAA,GAAGP,YAAY,CAACQ,MAAM,cAAAD,oBAAA,uBAAnBA,oBAAA,CAAqBE,OAAO,KAC0B;EAC7E,IAAIR,eAAe,CAACI,SAAS,CAAC,EAAE;IAC9B,MAAMK,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;IAEtC;IACA,OAAOL,SAAS,CAACM,WAAW,CAAC;MAC3BD,UAAU;MACVE,WAAW,EAAEF,UAAU,CAACG,GAAG,CAAER,SAAS,IAAK;QACzC,OAAOS,cAAc,CAACR,YAAY,EAAED,SAAS,CAAC;MAChD,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOS,cAAc,CAACR,YAAY,EAAED,SAAS,CAAC;AAChD;AAEA,SAASS,cAAcA,CAACR,YAAoB,EAAED,SAAiB,GAAG,CAAC,EAAE;EACnE,IAAIU,mBAA2B;EAC/B,IAAIV,SAAS,IAAI,CAAC,IAAIA,SAAS,IAAI,EAAE,EAAE;IACrCU,mBAAmB,GAAGC,4BAA4B,CAACX,SAAS,CAAC;EAC/D,CAAC,MAAM,IAAIA,SAAS,GAAG,EAAE,EAAE;IACzBU,mBAAmB,GAAGC,4BAA4B,CAAC,EAAE,CAAC;EACxD,CAAC,MAAM;IACLD,mBAAmB,GAAGC,4BAA4B,CAAC,CAAC,CAAC;EACvD;EACA,OAAOjB,KAAK,CAACO,YAAY,CAAC,CACvBW,GAAG,CAAClB,KAAK,CAAC,OAAO,CAAC,EAAEgB,mBAAmB,GAAG,IAAI,CAAC,CAC/CG,GAAG,CAAC,CAAC;AACV;AAEA,MAAMF,4BAAoD,GAAG;EAC3D,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,IAAI;EACP,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,IAAI;EACP,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE;AACN,CAAC", "ignoreList": []}
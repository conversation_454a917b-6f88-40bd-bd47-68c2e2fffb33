{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_utils", "_theming", "_AnimatedText", "_interopRequireDefault", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "HelperText", "style", "type", "visible", "theme", "themeOverrides", "onLayout", "padding", "disabled", "rest", "useInternalTheme", "current", "shown", "useRef", "Animated", "Value", "textHeight", "scale", "animation", "maxFontSizeMultiplier", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "handleTextLayout", "nativeEvent", "layout", "height", "textColor", "getTextColor", "createElement", "styles", "text", "color", "opacity", "transform", "translateY", "interpolate", "inputRange", "outputRange", "children", "StyleSheet", "create", "fontSize", "paddingVertical", "paddingHorizontal", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/HelperText/HelperText.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAQA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAEA,IAAAI,aAAA,GAAAC,sBAAA,CAAAL,OAAA;AAAsD,SAAAK,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAO,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAqCtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,UAAU,GAAGA,CAAC;EAClBC,KAAK;EACLC,IAAI,GAAG,MAAM;EACbC,OAAO,GAAG,IAAI;EACdC,KAAK,EAAEC,cAAc;EACrBC,QAAQ;EACRC,OAAO,GAAG,QAAQ;EAClBC,QAAQ;EACR,GAAGC;AACE,CAAC,KAAK;EACX,MAAML,KAAK,GAAG,IAAAM,yBAAgB,EAACL,cAAc,CAAC;EAC9C,MAAM;IAAEM,OAAO,EAAEC;EAAM,CAAC,GAAG7C,KAAK,CAAC8C,MAAM,CACrC,IAAIC,qBAAQ,CAACC,KAAK,CAACZ,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;EAED,IAAI;IAAEQ,OAAO,EAAEK;EAAW,CAAC,GAAGjD,KAAK,CAAC8C,MAAM,CAAS,CAAC,CAAC;EAErD,MAAM;IAAEI;EAAM,CAAC,GAAGb,KAAK,CAACc,SAAS;EAEjC,MAAM;IAAEC,qBAAqB,GAAG;EAAI,CAAC,GAAGV,IAAI;EAE5C1C,KAAK,CAACqD,SAAS,CAAC,MAAM;IACpB,IAAIjB,OAAO,EAAE;MACX;MACAW,qBAAQ,CAACO,MAAM,CAACT,KAAK,EAAE;QACrBU,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACAX,qBAAQ,CAACO,MAAM,CAACT,KAAK,EAAE;QACrBU,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,eAAe,EAAE;MACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACtB,OAAO,EAAEc,KAAK,EAAEL,KAAK,CAAC,CAAC;EAE3B,MAAMc,gBAAgB,GAAInD,CAAoB,IAAK;IACjD+B,QAAQ,aAARA,QAAQ,eAARA,QAAQ,CAAG/B,CAAC,CAAC;IACbyC,UAAU,GAAGzC,CAAC,CAACoD,WAAW,CAACC,MAAM,CAACC,MAAM;EAC1C,CAAC;EAED,MAAMC,SAAS,GAAG,IAAAC,mBAAY,EAAC;IAAE3B,KAAK;IAAEI,QAAQ;IAAEN;EAAK,CAAC,CAAC;EAEzD,oBACEnC,KAAA,CAAAiE,aAAA,CAAC3D,aAAA,CAAAI,OAAY,EAAAiB,QAAA;IACXY,QAAQ,EAAEoB,gBAAiB;IAC3BzB,KAAK,EAAE,CACLgC,MAAM,CAACC,IAAI,EACX3B,OAAO,KAAK,MAAM,GAAG0B,MAAM,CAAC1B,OAAO,GAAG,CAAC,CAAC,EACxC;MACE4B,KAAK,EAAEL,SAAS;MAChBM,OAAO,EAAExB,KAAK;MACdyB,SAAS,EACPlC,OAAO,IAAID,IAAI,KAAK,OAAO,GACvB,CACE;QACEoC,UAAU,EAAE1B,KAAK,CAAC2B,WAAW,CAAC;UAC5BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,CAACzB,UAAU,GAAG,CAAC,EAAE,CAAC;QAClC,CAAC;MACH,CAAC,CACF,GACD;IACR,CAAC,EACDf,KAAK,CACL;IACFkB,qBAAqB,EAAEA;EAAsB,GACzCV,IAAI,GAEPA,IAAI,CAACiC,QACM,CAAC;AAEnB,CAAC;AAED,MAAMT,MAAM,GAAGU,uBAAU,CAACC,MAAM,CAAC;EAC/BV,IAAI,EAAE;IACJW,QAAQ,EAAE,EAAE;IACZC,eAAe,EAAE;EACnB,CAAC;EACDvC,OAAO,EAAE;IACPwC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAxE,OAAA,GAEYuB,UAAU", "ignoreList": []}
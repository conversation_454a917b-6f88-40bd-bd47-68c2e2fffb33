{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_color", "_interopRequireDefault", "_theming", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "INDETERMINATE_DURATION", "INDETERMINATE_MAX_WIDTH", "isRTL", "I18nManager", "ProgressBar", "color", "indeterminate", "progress", "visible", "theme", "themeOverrides", "animatedValue", "style", "fillStyle", "testID", "rest", "_theme$colors", "isWeb", "Platform", "OS", "useInternalTheme", "current", "timer", "useRef", "Animated", "Value", "fade", "passedAnimatedValue", "width", "<PERSON><PERSON><PERSON><PERSON>", "useState", "prevWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indeterminateAnimation", "scale", "animation", "useEffect", "startAnimation", "useCallback", "timing", "duration", "toValue", "useNativeDriver", "isInteraction", "start", "externalAnimation", "setValue", "loop", "stopAnimation", "stop", "onLayout", "event", "nativeEvent", "layout", "tintColor", "colors", "primary", "trackTintColor", "isV3", "surfaceVariant", "setColor", "alpha", "rgb", "string", "createElement", "View", "accessible", "accessibilityRole", "accessibilityState", "busy", "accessibilityValue", "min", "max", "now", "Math", "round", "styles", "webContainer", "container", "backgroundColor", "opacity", "progressBar", "transform", "translateX", "interpolate", "inputRange", "outputRange", "scaleX", "StyleSheet", "create", "height", "overflow", "flex", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/ProgressBar.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAWA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAEA,IAAAI,QAAA,GAAAJ,OAAA;AAAmD,SAAAG,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAN,wBAAAM,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAV,uBAAA,YAAAA,CAAAM,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAyCnD,MAAMG,sBAAsB,GAAG,IAAI;AACnC,MAAMC,uBAAuB,GAAG,GAAG;AACnC,MAAM;EAAEC;AAAM,CAAC,GAAGC,wBAAW;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAC;EACnBC,KAAK;EACLC,aAAa;EACbC,QAAQ,GAAG,CAAC;EACZC,OAAO,GAAG,IAAI;EACdC,KAAK,EAAEC,cAAc;EACrBC,aAAa;EACbC,KAAK;EACLC,SAAS;EACTC,MAAM,GAAG,cAAc;EACvB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMC,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;EACnC,MAAMV,KAAK,GAAG,IAAAW,yBAAgB,EAACV,cAAc,CAAC;EAC9C,MAAM;IAAEW,OAAO,EAAEC;EAAM,CAAC,GAAGtD,KAAK,CAACuD,MAAM,CACrC,IAAIC,qBAAQ,CAACC,KAAK,CAAC,CAAC,CACtB,CAAC;EACD,MAAM;IAAEJ,OAAO,EAAEK;EAAK,CAAC,GAAG1D,KAAK,CAACuD,MAAM,CAAiB,IAAIC,qBAAQ,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7E,MAAME,mBAAmB,GACvB3D,KAAK,CAACuD,MAAM,CAAyBZ,aAAa,CAAC;EACrD,MAAM,CAACiB,KAAK,EAAEC,QAAQ,CAAC,GAAG7D,KAAK,CAAC8D,QAAQ,CAAS,CAAC,CAAC;EACnD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGhE,KAAK,CAAC8D,QAAQ,CAAS,CAAC,CAAC;EAE3D,MAAMG,sBAAsB,GAC1BjE,KAAK,CAACuD,MAAM,CAAqC,IAAI,CAAC;EAExD,MAAM;IAAEW;EAAM,CAAC,GAAGzB,KAAK,CAAC0B,SAAS;EAEjCnE,KAAK,CAACoE,SAAS,CAAC,MAAM;IACpBT,mBAAmB,CAACN,OAAO,GAAGV,aAAa;EAC7C,CAAC,CAAC;EAEF,MAAM0B,cAAc,GAAGrE,KAAK,CAACsE,WAAW,CAAC,MAAM;IAC7C;IACAd,qBAAQ,CAACe,MAAM,CAACb,IAAI,EAAE;MACpBc,QAAQ,EAAE,GAAG,GAAGN,KAAK;MACrBO,OAAO,EAAE,CAAC;MACVC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;;IAEV;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMC,iBAAiB,GACrB,OAAOlB,mBAAmB,CAACN,OAAO,KAAK,WAAW,IAClDM,mBAAmB,CAACN,OAAO,IAAI,CAAC;IAElC,IAAIwB,iBAAiB,EAAE;MACrB;IACF;;IAEA;IACA,IAAIvC,aAAa,EAAE;MACjB,IAAI,CAAC2B,sBAAsB,CAACZ,OAAO,EAAE;QACnCY,sBAAsB,CAACZ,OAAO,GAAGG,qBAAQ,CAACe,MAAM,CAACjB,KAAK,EAAE;UACtDkB,QAAQ,EAAExC,sBAAsB;UAChCyC,OAAO,EAAE,CAAC;UACV;UACAC,eAAe,EAAE,CAACzB,KAAK;UACvB0B,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ;;MAEA;MACArB,KAAK,CAACwB,QAAQ,CAAC,CAAC,CAAC;MAEjBtB,qBAAQ,CAACuB,IAAI,CAACd,sBAAsB,CAACZ,OAAO,CAAC,CAACuB,KAAK,CAAC,CAAC;IACvD,CAAC,MAAM;MACLpB,qBAAQ,CAACe,MAAM,CAACjB,KAAK,EAAE;QACrBkB,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,OAAO,EAAElC,QAAQ,GAAGA,QAAQ,GAAG,CAAC;QAChCmC,eAAe,EAAE,IAAI;QACrBC,aAAa,EAAE;MACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAClB,IAAI,EAAEQ,KAAK,EAAE5B,aAAa,EAAEgB,KAAK,EAAEf,QAAQ,EAAEU,KAAK,CAAC,CAAC;EAExD,MAAM+B,aAAa,GAAGhF,KAAK,CAACsE,WAAW,CAAC,MAAM;IAC5C;IACA,IAAIL,sBAAsB,CAACZ,OAAO,EAAE;MAClCY,sBAAsB,CAACZ,OAAO,CAAC4B,IAAI,CAAC,CAAC;IACvC;IAEAzB,qBAAQ,CAACe,MAAM,CAACb,IAAI,EAAE;MACpBc,QAAQ,EAAE,GAAG,GAAGN,KAAK;MACrBO,OAAO,EAAE,CAAC;MACVC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAClB,IAAI,EAAEQ,KAAK,CAAC,CAAC;EAEjBlE,KAAK,CAACoE,SAAS,CAAC,MAAM;IACpB,IAAI5B,OAAO,EAAE6B,cAAc,CAAC,CAAC,CAAC,KACzBW,aAAa,CAAC,CAAC;EACtB,CAAC,EAAE,CAACxC,OAAO,EAAE6B,cAAc,EAAEW,aAAa,CAAC,CAAC;EAE5ChF,KAAK,CAACoE,SAAS,CAAC,MAAM;IACpB,IAAIzB,aAAa,IAAIA,aAAa,IAAI,CAAC,EAAE;MACvCW,KAAK,CAACwB,QAAQ,CAACnC,aAAa,CAAC;IAC/B;EACF,CAAC,EAAE,CAACA,aAAa,EAAEW,KAAK,CAAC,CAAC;EAE1BtD,KAAK,CAACoE,SAAS,CAAC,MAAM;IACpB;IACA,IAAI5B,OAAO,IAAIuB,SAAS,KAAK,CAAC,EAAE;MAC9BM,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACN,SAAS,EAAEM,cAAc,EAAE7B,OAAO,CAAC,CAAC;EAExC,MAAM0C,QAAQ,GAAIC,KAAwB,IAAK;IAC7CnB,YAAY,CAACJ,KAAK,CAAC;IACnBC,QAAQ,CAACsB,KAAK,CAACC,WAAW,CAACC,MAAM,CAACzB,KAAK,CAAC;EAC1C,CAAC;EAED,MAAM0B,SAAS,GAAGjD,KAAK,MAAAW,aAAA,GAAIP,KAAK,CAAC8C,MAAM,cAAAvC,aAAA,uBAAZA,aAAA,CAAcwC,OAAO;EAChD,MAAMC,cAAc,GAAGhD,KAAK,CAACiD,IAAI,GAC7BjD,KAAK,CAAC8C,MAAM,CAACI,cAAc,GAC3B,IAAAC,cAAQ,EAACN,SAAS,CAAC,CAACO,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAElD,oBACE/F,KAAA,CAAAgG,aAAA,CAAC7F,YAAA,CAAA8F,IAAI,EAAAvE,QAAA;IACHwD,QAAQ,EAAEA;EAAS,GACfnC,IAAI;IACRmD,UAAU;IACVC,iBAAiB,EAAC,aAAa;IAC/BC,kBAAkB,EAAE;MAAEC,IAAI,EAAE7D;IAAQ,CAAE;IACtC8D,kBAAkB,EAChBhE,aAAa,GACT,CAAC,CAAC,GACF;MAAEiE,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE,GAAG;MAAEC,GAAG,EAAEC,IAAI,CAACC,KAAK,CAACpE,QAAQ,GAAG,GAAG;IAAE,CACzD;IACDK,KAAK,EAAEK,KAAK,IAAI2D,MAAM,CAACC,YAAa;IACpC/D,MAAM,EAAEA;EAAO,iBAEf9C,KAAA,CAAAgG,aAAA,CAAC7F,YAAA,CAAAqD,QAAQ,CAACyC,IAAI;IACZrD,KAAK,EAAE,CACLgE,MAAM,CAACE,SAAS,EAChB;MAAEC,eAAe,EAAEtB,cAAc;MAAEuB,OAAO,EAAEtD;IAAK,CAAC,EAClDd,KAAK;EACL,GAEDgB,KAAK,gBACJ5D,KAAA,CAAAgG,aAAA,CAAC7F,YAAA,CAAAqD,QAAQ,CAACyC,IAAI;IACZnD,MAAM,EAAE,GAAGA,MAAM,OAAQ;IACzBF,KAAK,EAAE,CACLgE,MAAM,CAACK,WAAW,EAClB;MACErD,KAAK;MACLmD,eAAe,EAAEzB,SAAS;MAC1B4B,SAAS,EAAE,CACT;QACEC,UAAU,EAAE7D,KAAK,CAAC8D,WAAW,CAC3B9E,aAAa,GACT;UACE+E,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAE,CACX,CAACpF,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG0B,KAAK,EAC9B,CAAC1B,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IACb,GAAG,GACHD,uBAAuB,GACvB2B,KAAK,EACP,CAAC1B,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,GAAG0B,KAAK;QAElC,CAAC,GACD;UACEyD,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,CAACpF,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,GAAG0B,KAAK,EAAE,CAAC;QACjD,CACN;MACF,CAAC,EACD;QACE;QACA2D,MAAM,EAAEjE,KAAK,CAAC8D,WAAW,CACvB9E,aAAa,GACT;UACE+E,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAE,CACX,MAAM,EACNrF,uBAAuB,EACvB,MAAM;QAEV,CAAC,GACD;UACEoF,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC;QACzB,CACN;MACF,CAAC;IAEL,CAAC,EACDzE,SAAS;EACT,CACH,CAAC,GACA,IACS,CACX,CAAC;AAEX,CAAC;AAED,MAAM+D,MAAM,GAAGY,uBAAU,CAACC,MAAM,CAAC;EAC/BX,SAAS,EAAE;IACTY,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EACDd,YAAY,EAAE;IACZjD,KAAK,EAAE,MAAM;IACb8D,MAAM,EAAE;EACV,CAAC;EACDT,WAAW,EAAE;IACXW,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAArH,OAAA,GAEY2B,WAAW", "ignoreList": []}
import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../contexts/AuthContext';
import { theme } from '../config/theme';

// Auth Screens
import LoginScreen from '../screens/auth/LoginScreen';
import RegisterScreen from '../screens/auth/RegisterScreen';
import ForgotPasswordScreen from '../screens/auth/ForgotPasswordScreen';

// Main Screens
import MapScreen from '../screens/main/MapScreen';
import SpotsScreen from '../screens/main/SpotsScreen';
import GroupsScreen from '../screens/main/GroupsScreen';
import ProfileScreen from '../screens/main/ProfileScreen';

// Detail Screens
import SpotDetailScreen from '../screens/details/SpotDetailScreen';
import CreateSpotScreen from '../screens/create/CreateSpotScreen';
import GroupDetailScreen from '../screens/details/GroupDetailScreen';
import CreateGroupScreen from '../screens/create/CreateGroupScreen';
import JoinGroupScreen from '../screens/groups/JoinGroupScreen';
import EditProfileScreen from '../screens/profile/EditProfileScreen';
import FavoritesScreen from '../screens/profile/FavoritesScreen';
import AchievementsScreen from '../screens/profile/AchievementsScreen';

const Stack = createStackNavigator();
const Tab = createBottomTabNavigator();

// Auth Stack
const AuthStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: theme.colors.primary,
      },
      headerTintColor: '#fff',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    }}
  >
    <Stack.Screen 
      name="Login" 
      component={LoginScreen} 
      options={{ headerShown: false }}
    />
    <Stack.Screen 
      name="Register" 
      component={RegisterScreen} 
      options={{ title: 'Account Aanmaken' }}
    />
    <Stack.Screen 
      name="ForgotPassword" 
      component={ForgotPasswordScreen} 
      options={{ title: 'Wachtwoord Vergeten' }}
    />
  </Stack.Navigator>
);

// Main Tab Navigator
const MainTabs = () => (
  <Tab.Navigator
    screenOptions={({ route }) => ({
      tabBarIcon: ({ focused, color, size }) => {
        let iconName;

        if (route.name === 'Map') {
          iconName = focused ? 'map' : 'map-outline';
        } else if (route.name === 'Spots') {
          iconName = focused ? 'location' : 'location-outline';
        } else if (route.name === 'Groups') {
          iconName = focused ? 'people' : 'people-outline';
        } else if (route.name === 'Profile') {
          iconName = focused ? 'person' : 'person-outline';
        }

        return <Ionicons name={iconName} size={size} color={color} />;
      },
      tabBarActiveTintColor: theme.colors.primary,
      tabBarInactiveTintColor: theme.colors.textSecondary,
      tabBarStyle: {
        backgroundColor: theme.colors.background,
        borderTopColor: theme.colors.border,
      },
      headerStyle: {
        backgroundColor: theme.colors.primary,
      },
      headerTintColor: '#fff',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    })}
  >
    <Tab.Screen 
      name="Map" 
      component={MapScreen} 
      options={{ 
        title: 'Kaart',
        headerShown: false,
      }}
    />
    <Tab.Screen 
      name="Spots" 
      component={SpotsScreen} 
      options={{ title: 'Spots' }}
    />
    <Tab.Screen 
      name="Groups" 
      component={GroupsScreen} 
      options={{ title: 'Groepen' }}
    />
    <Tab.Screen 
      name="Profile" 
      component={ProfileScreen} 
      options={{ title: 'Profiel' }}
    />
  </Tab.Navigator>
);

// Main App Stack
const AppStack = () => (
  <Stack.Navigator
    screenOptions={{
      headerStyle: {
        backgroundColor: theme.colors.primary,
      },
      headerTintColor: '#fff',
      headerTitleStyle: {
        fontWeight: 'bold',
      },
    }}
  >
    <Stack.Screen 
      name="MainTabs" 
      component={MainTabs} 
      options={{ headerShown: false }}
    />
    <Stack.Screen 
      name="SpotDetail" 
      component={SpotDetailScreen} 
      options={{ title: 'Spot Details' }}
    />
    <Stack.Screen 
      name="CreateSpot" 
      component={CreateSpotScreen} 
      options={{ 
        title: 'Nieuwe Spot',
        presentation: 'modal',
      }}
    />
    <Stack.Screen 
      name="GroupDetail" 
      component={GroupDetailScreen} 
      options={{ title: 'Groep Details' }}
    />
    <Stack.Screen 
      name="CreateGroup" 
      component={CreateGroupScreen} 
      options={{ 
        title: 'Nieuwe Groep',
        presentation: 'modal',
      }}
    />
    <Stack.Screen 
      name="JoinGroup" 
      component={JoinGroupScreen} 
      options={{ 
        title: 'Groep Joinen',
        presentation: 'modal',
      }}
    />
    <Stack.Screen 
      name="EditProfile" 
      component={EditProfileScreen} 
      options={{ title: 'Profiel Bewerken' }}
    />
    <Stack.Screen 
      name="Favorites" 
      component={FavoritesScreen} 
      options={{ title: 'Favorieten' }}
    />
    <Stack.Screen 
      name="Achievements" 
      component={AchievementsScreen} 
      options={{ title: 'Prestaties' }}
    />
  </Stack.Navigator>
);

// Main App Navigator
const AppNavigator = () => {
  const { user, loading } = useAuth();

  if (loading) {
    return null; // You can add a loading screen here
  }

  return (
    <NavigationContainer>
      {user ? <AppStack /> : <AuthStack />}
    </NavigationContainer>
  );
};

export default AppNavigator;

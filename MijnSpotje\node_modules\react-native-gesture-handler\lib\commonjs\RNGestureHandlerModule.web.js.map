{"version": 3, "sources": ["RNGestureHandlerModule.web.ts"], "names": ["shouldPreventDrop", "handleSetJSResponder", "tag", "blockNativeResponder", "console", "warn", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "Gestures", "Error", "GestureClass", "NodeManager", "GestureHandlerWebDelegate", "InteractionManager", "instance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "HammerGestures", "HammerNodeManager", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "Element", "React", "Component", "handler", "constructor", "name", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "mappings": ";;;;;;;AAAA;;AAGA;;AACA;;AAEA;;AACA;;AACA;;AACA;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA,IAAIA,iBAAiB,GAAG,KAAxB;eAEe;AACbC,EAAAA,oBAAoB,CAACC,GAAD,EAAcC,oBAAd,EAA6C;AAC/DC,IAAAA,OAAO,CAACC,IAAR,CAAa,wBAAb,EAAuCH,GAAvC,EAA4CC,oBAA5C;AACD,GAHY;;AAIbG,EAAAA,sBAAsB,GAAG;AACvBF,IAAAA,OAAO,CAACC,IAAR,CAAa,0BAAb;AACD,GANY;;AAObE,EAAAA,oBAAoB,CAClBC,WADkB,EAElBC,UAFkB,EAGlBC,MAHkB,EAIlB;AACA,QAAI,gEAAJ,EAAqC;AACnC,UAAI,EAAEF,WAAW,IAAIG,kBAAjB,CAAJ,EAAgC;AAC9B,cAAM,IAAIC,KAAJ,CACH,iCAAgCJ,WAAY,2BADzC,CAAN;AAGD;;AAED,YAAMK,YAAY,GAAGF,mBAASH,WAAT,CAArB;;AACAM,2BAAYP,oBAAZ,CACEE,UADF,EAEE,IAAII,YAAJ,CAAiB,IAAIE,oDAAJ,EAAjB,CAFF;;AAIAC,kCAAmBC,QAAnB,CAA4BC,qBAA5B,CACEJ,qBAAYK,UAAZ,CAAuBV,UAAvB,CADF,EAEEC,MAFF;AAID,KAhBD,MAgBO;AACL,UAAI,EAAEF,WAAW,IAAIY,wBAAjB,CAAJ,EAAsC;AACpC,cAAM,IAAIR,KAAJ,CACH,iCAAgCJ,WAAY,2BADzC,CAAN;AAGD,OALI,CAOL;AACA;;;AACA,YAAMK,YAAY,GAAGO,yBAAeZ,WAAf,CAArB,CATK,CAUL;;AACAa,MAAAA,iBAAiB,CAACd,oBAAlB,CAAuCE,UAAvC,EAAmD,IAAII,YAAJ,EAAnD;AACD;;AAED,SAAKS,oBAAL,CAA0Bb,UAA1B,EAAsCC,MAAtC;AACD,GA3CY;;AA4Cba,EAAAA,oBAAoB,CAClBd,UADkB,EAElB;AACAe,EAAAA,OAHkB,EAIlBC,WAJkB,EAKlBC,QALkB,EAMlB;AACA,QAAI,EAAEF,OAAO,YAAYG,OAAnB,IAA8BH,OAAO,YAAYI,eAAMC,SAAzD,CAAJ,EAAyE;AACvE7B,MAAAA,iBAAiB,GAAG,IAApB;AAEA,YAAM8B,OAAO,GAAG,mEACZhB,qBAAYK,UAAZ,CAAuBV,UAAvB,CADY,GAEZY,iBAAiB,CAACF,UAAlB,CAA6BV,UAA7B,CAFJ;AAIA,YAAMD,WAAW,GAAGsB,OAAO,CAACC,WAAR,CAAoBC,IAAxC;AAEA,YAAM,IAAIpB,KAAJ,CACH,GAAEJ,WAAY,aAAYC,UAAW,iDADlC,CAAN;AAGD;;AAED,QAAI,gEAAJ,EAAqC;AACnC;AACAK,2BAAYK,UAAZ,CAAuBV,UAAvB,EAAmCwB,IAAnC,CAAwCT,OAAxC,EAAiDE,QAAjD;AACD,KAHD,MAGO;AACL;AACAL,MAAAA,iBAAiB,CAACF,UAAlB,CAA6BV,UAA7B,EAAyCyB,OAAzC,CAAiDV,OAAjD,EAA0DE,QAA1D;AACD;AACF,GAxEY;;AAyEbJ,EAAAA,oBAAoB,CAACb,UAAD,EAAqB0B,SAArB,EAAwC;AAC1D,QAAI,gEAAJ,EAAqC;AACnCrB,2BAAYK,UAAZ,CAAuBV,UAAvB,EAAmC2B,mBAAnC,CAAuDD,SAAvD;;AAEAnB,kCAAmBC,QAAnB,CAA4BC,qBAA5B,CACEJ,qBAAYK,UAAZ,CAAuBV,UAAvB,CADF,EAEE0B,SAFF;AAID,KAPD,MAOO;AACLd,MAAAA,iBAAiB,CAACF,UAAlB,CAA6BV,UAA7B,EAAyC2B,mBAAzC,CAA6DD,SAA7D;AACD;AACF,GApFY;;AAqFbE,EAAAA,qBAAqB,CAAC5B,UAAD,EAAqB;AACxC,QAAI,gEAAJ,EAAqC;AACnC,aAAOK,qBAAYK,UAAZ,CAAuBV,UAAvB,CAAP;AACD,KAFD,MAEO;AACL,aAAOY,iBAAiB,CAACF,UAAlB,CAA6BV,UAA7B,CAAP;AACD;AACF,GA3FY;;AA4Fb6B,EAAAA,kBAAkB,CAAC7B,UAAD,EAAqB;AACrC,QAAIT,iBAAJ,EAAuB;AACrB;AACD;;AAED,QAAI,gEAAJ,EAAqC;AACnCc,2BAAYwB,kBAAZ,CAA+B7B,UAA/B;AACD,KAFD,MAEO;AACLY,MAAAA,iBAAiB,CAACiB,kBAAlB,CAAqC7B,UAArC;AACD;AACF,GAtGY;;AAuGb;AACA8B,EAAAA,eAAe,GAAG,CAAE;;AAxGP,C", "sourcesContent": ["import React from 'react';\n\nimport type { ActionType } from './ActionType';\nimport { isNewWebImplementationEnabled } from './EnableNewWebImplementation';\nimport { Gestures, HammerGestures } from './web/Gestures';\nimport type { Config } from './web/interfaces';\nimport InteractionManager from './web/tools/InteractionManager';\nimport NodeManager from './web/tools/NodeManager';\nimport * as HammerNodeManager from './web_hammer/NodeManager';\nimport { GestureHandlerWebDelegate } from './web/tools/GestureHandlerWebDelegate';\n\n// init method is called inside attachGestureHandler function. However, this function may\n// fail when received view is not valid HTML element. On the other hand, dropGestureHandler\n// will be called even if attach failed, which will result in crash.\n//\n// We use this flag to check whether or not dropGestureHandler should be called.\nlet shouldPreventDrop = false;\n\nexport default {\n  handleSetJSResponder(tag: number, blockNativeResponder: boolean) {\n    console.warn('handleSetJSResponder: ', tag, blockNativeResponder);\n  },\n  handleClearJSResponder() {\n    console.warn('handleClearJSResponder: ');\n  },\n  createGestureHandler<T>(\n    handlerName: keyof typeof Gestures,\n    handlerTag: number,\n    config: T\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      if (!(handlerName in Gestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      const GestureClass = Gestures[handlerName];\n      NodeManager.createGestureHandler(\n        handlerTag,\n        new GestureClass(new GestureHandlerWebDelegate())\n      );\n      InteractionManager.instance.configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        config as unknown as Config\n      );\n    } else {\n      if (!(handlerName in HammerGestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      // @ts-ignore If it doesn't exist, the error is thrown\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      const GestureClass = HammerGestures[handlerName];\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n      HammerNodeManager.createGestureHandler(handlerTag, new GestureClass());\n    }\n\n    this.updateGestureHandler(handlerTag, config as unknown as Config);\n  },\n  attachGestureHandler(\n    handlerTag: number,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    newView: any,\n    _actionType: ActionType,\n    propsRef: React.RefObject<unknown>\n  ) {\n    if (!(newView instanceof Element || newView instanceof React.Component)) {\n      shouldPreventDrop = true;\n\n      const handler = isNewWebImplementationEnabled()\n        ? NodeManager.getHandler(handlerTag)\n        : HammerNodeManager.getHandler(handlerTag);\n\n      const handlerName = handler.constructor.name;\n\n      throw new Error(\n        `${handlerName} with tag ${handlerTag} received child that is not valid HTML element.`\n      );\n    }\n\n    if (isNewWebImplementationEnabled()) {\n      // @ts-ignore Types should be HTMLElement or React.Component\n      NodeManager.getHandler(handlerTag).init(newView, propsRef);\n    } else {\n      // @ts-ignore Types should be HTMLElement or React.Component\n      HammerNodeManager.getHandler(handlerTag).setView(newView, propsRef);\n    }\n  },\n  updateGestureHandler(handlerTag: number, newConfig: Config) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n\n      InteractionManager.instance.configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        newConfig\n      );\n    } else {\n      HammerNodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n    }\n  },\n  getGestureHandlerNode(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      return NodeManager.getHandler(handlerTag);\n    } else {\n      return HammerNodeManager.getHandler(handlerTag);\n    }\n  },\n  dropGestureHandler(handlerTag: number) {\n    if (shouldPreventDrop) {\n      return;\n    }\n\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.dropGestureHandler(handlerTag);\n    } else {\n      HammerNodeManager.dropGestureHandler(handlerTag);\n    }\n  },\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  flushOperations() {},\n};\n"]}
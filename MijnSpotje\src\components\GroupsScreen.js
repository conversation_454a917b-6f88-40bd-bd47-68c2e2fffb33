import React, { useState, useEffect, useCallback, memo, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  FlatList,
  Alert,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import GroupsService from '../services/GroupsService';
import ImageManager from './ImageManager';
import PerformanceMonitor from '../utils/PerformanceMonitor';

const GroupsScreen = memo(({ isDark = false, onGroupPress, t }) => {
  const [groups, setGroups] = useState([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showJoinModal, setShowJoinModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Safety check for required props
  if (!onGroupPress || typeof onGroupPress !== 'function') {
    console.warn('⚠️ GroupsScreen: onGroupPress prop is required');
    return null;
  }

  // Load groups on mount
  useEffect(() => {
    loadGroups();
  }, []);

  const loadGroups = useCallback(async () => {
    try {
      setIsLoading(true);
      PerformanceMonitor.startTiming('GroupsScreen.loadGroups');

      const userGroups = await GroupsService.getUserGroups();
      setGroups(Array.isArray(userGroups) ? userGroups : []);

      PerformanceMonitor.endTiming('GroupsScreen.loadGroups');
      console.log(`👥 Loaded ${userGroups?.length || 0} groups`);
    } catch (error) {
      console.error('❌ Error loading groups:', error);
      setGroups([]); // Fallback to empty array
      PerformanceMonitor.endTiming('GroupsScreen.loadGroups');
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleCreateGroup = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const handleJoinGroup = useCallback(() => {
    setShowJoinModal(true);
  }, []);

  const renderGroupCard = useCallback(({ item: group }) => (
    <GroupCard
      group={group}
      isDark={isDark}
      onPress={() => onGroupPress(group)}
    />
  ), [isDark, onGroupPress]);

  const keyExtractor = useCallback((item) => item.id, []);

  // Optimize FlatList with getItemLayout
  const getItemLayout = useCallback((data, index) => ({
    length: 88, // Approximate height of group card
    offset: 88 * index,
    index,
  }), []);

  // Track component renders
  useEffect(() => {
    PerformanceMonitor.trackRender('GroupsScreen');
  });

  if (isLoading) {
    return (
      <View style={[styles.container, isDark && styles.containerDark, styles.loadingContainer]}>
        <Text style={[styles.loadingText, isDark && styles.textDark]}>👥</Text>
        <Text style={[styles.loadingSubtext, isDark && styles.textSecondaryDark]}>
          Groepen laden...
        </Text>
      </View>
    );
  }

  if (groups.length === 0) {
    return (
      <View style={[styles.container, isDark && styles.containerDark]}>
        <View style={styles.emptyState}>
          <Text style={styles.emptyIcon}>👥</Text>
          <Text style={[styles.emptyTitle, isDark && styles.textDark]}>Geen Groepen</Text>
          <Text style={[styles.emptySubtext, isDark && styles.textSecondaryDark]}>
            Maak een nieuwe groep aan of sluit je aan bij een bestaande groep
          </Text>
          
          <View style={styles.emptyActions}>
            <TouchableOpacity
              style={[styles.primaryButton, isDark && styles.primaryButtonDark]}
              onPress={handleCreateGroup}
            >
              <Text style={styles.primaryButtonText}>➕ Nieuwe Groep</Text>
            </TouchableOpacity>
            
            <TouchableOpacity
              style={[styles.secondaryButton, isDark && styles.secondaryButtonDark]}
              onPress={handleJoinGroup}
            >
              <Text style={[styles.secondaryButtonText, isDark && styles.textDark]}>🔗 Groep Joinen</Text>
            </TouchableOpacity>
          </View>
        </View>

        <CreateGroupModal
          visible={showCreateModal}
          onClose={() => setShowCreateModal(false)}
          onSuccess={loadGroups}
          isDark={isDark}
        />

        <JoinGroupModal
          visible={showJoinModal}
          onClose={() => setShowJoinModal(false)}
          onSuccess={loadGroups}
          isDark={isDark}
        />
      </View>
    );
  }

  return (
    <View style={[styles.container, isDark && styles.containerDark]}>
      <View style={[styles.header, isDark && styles.headerDark]}>
        <Text style={[styles.headerTitle, isDark && styles.textDark]}>Mijn Groepen</Text>
        <View style={styles.headerActions}>
          <TouchableOpacity
            style={[styles.headerButton, isDark && styles.headerButtonDark]}
            onPress={handleJoinGroup}
          >
            <Text style={styles.headerButtonText}>🔗</Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.headerButton, isDark && styles.headerButtonDark]}
            onPress={handleCreateGroup}
          >
            <Text style={styles.headerButtonText}>➕</Text>
          </TouchableOpacity>
        </View>
      </View>

      <FlatList
        data={groups}
        renderItem={renderGroupCard}
        keyExtractor={keyExtractor}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContent}
        removeClippedSubviews={true}
        maxToRenderPerBatch={5}
        updateCellsBatchingPeriod={100}
        initialNumToRender={5}
        windowSize={5}
        getItemLayout={getItemLayout}
        onScrollBeginDrag={() => PerformanceMonitor.startTiming('GroupsScreen.scroll')}
        onScrollEndDrag={() => PerformanceMonitor.endTiming('GroupsScreen.scroll')}
      />

      <CreateGroupModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSuccess={loadGroups}
        isDark={isDark}
      />

      <JoinGroupModal
        visible={showJoinModal}
        onClose={() => setShowJoinModal(false)}
        onSuccess={loadGroups}
        isDark={isDark}
      />
    </View>
  );
});

// Group Card Component
const GroupCard = memo(({ group, isDark, onPress }) => {
  const handlePress = useCallback(() => {
    onPress(group);
  }, [group, onPress]);

  return (
    <TouchableOpacity
      style={[styles.groupCard, isDark && styles.groupCardDark]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.groupHeader}>
        <View style={[styles.groupAvatar, isDark && styles.groupAvatarDark]}>
          <Text style={styles.groupAvatarText}>
            {group.avatar || group.name.charAt(0).toUpperCase()}
          </Text>
        </View>
        
        <View style={styles.groupInfo}>
          <Text style={[styles.groupName, isDark && styles.textDark]}>{group.name}</Text>
          <Text style={[styles.groupDescription, isDark && styles.textSecondaryDark]} numberOfLines={1}>
            {group.description || 'Geen beschrijving'}
          </Text>
          <View style={styles.groupStats}>
            <Text style={[styles.groupStat, isDark && styles.textSecondaryDark]}>
              👥 {group.stats.totalMembers} leden
            </Text>
            <Text style={[styles.groupStat, isDark && styles.textSecondaryDark]}>
              📍 {group.stats.totalSpots} spots
            </Text>
          </View>
        </View>
        
        <Text style={[styles.groupArrow, isDark && styles.textSecondaryDark]}>›</Text>
      </View>
    </TouchableOpacity>
  );
});

// Create Group Modal Component
const CreateGroupModal = memo(({ visible, onClose, onSuccess, isDark }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    avatar: null,
  });
  const [isLoading, setIsLoading] = useState(false);

  const handleSave = useCallback(async () => {
    if (!formData.name.trim()) {
      Alert.alert('Fout', 'Groepsnaam is verplicht', [{ text: 'OK' }]);
      return;
    }

    try {
      setIsLoading(true);
      await GroupsService.createGroup(formData);
      
      setFormData({ name: '', description: '', avatar: null });
      onClose();
      onSuccess();
      
      Alert.alert('Succes', 'Groep succesvol aangemaakt!', [{ text: 'OK' }]);
    } catch (error) {
      console.error('❌ Error creating group:', error);
      Alert.alert('Fout', 'Kon groep niet aanmaken', [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  }, [formData, onClose, onSuccess]);

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
        <View style={[styles.modalHeader, isDark && styles.modalHeaderDark]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.modalButton}>Annuleren</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, isDark && styles.textDark]}>Nieuwe Groep</Text>
          <TouchableOpacity onPress={handleSave} disabled={isLoading}>
            <Text style={[styles.modalButton, styles.saveButton, isLoading && styles.disabledButton]}>
              {isLoading ? 'Opslaan...' : 'Opslaan'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent}>
          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Groepsnaam *</Text>
            <TextInput
              style={[styles.input, isDark && styles.inputDark]}
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              placeholder="Bijv. Familie, Vrienden, Werk..."
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              maxLength={50}
            />
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Beschrijving</Text>
            <TextInput
              style={[styles.textArea, isDark && styles.inputDark]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Optionele beschrijving van de groep..."
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              multiline
              numberOfLines={3}
              maxLength={200}
            />
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
});

// Join Group Modal Component
const JoinGroupModal = memo(({ visible, onClose, onSuccess, isDark }) => {
  const [inviteCode, setInviteCode] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const handleJoin = useCallback(async () => {
    if (!inviteCode.trim()) {
      Alert.alert('Fout', 'Uitnodigingscode is verplicht', [{ text: 'OK' }]);
      return;
    }

    try {
      setIsLoading(true);
      const group = await GroupsService.joinGroupByCode(inviteCode.toUpperCase());
      
      setInviteCode('');
      onClose();
      onSuccess();
      
      Alert.alert('Succes', `Je bent toegevoegd aan "${group.name}"!`, [{ text: 'OK' }]);
    } catch (error) {
      console.error('❌ Error joining group:', error);
      Alert.alert('Fout', error.message || 'Kon niet deelnemen aan groep', [{ text: 'OK' }]);
    } finally {
      setIsLoading(false);
    }
  }, [inviteCode, onClose, onSuccess]);

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
        <View style={[styles.modalHeader, isDark && styles.modalHeaderDark]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={styles.modalButton}>Annuleren</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, isDark && styles.textDark]}>Groep Joinen</Text>
          <TouchableOpacity onPress={handleJoin} disabled={isLoading}>
            <Text style={[styles.modalButton, styles.saveButton, isLoading && styles.disabledButton]}>
              {isLoading ? 'Joinen...' : 'Joinen'}
            </Text>
          </TouchableOpacity>
        </View>

        <View style={styles.modalContent}>
          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Uitnodigingscode</Text>
            <TextInput
              style={[styles.input, isDark && styles.inputDark, styles.codeInput]}
              value={inviteCode}
              onChangeText={setInviteCode}
              placeholder="Bijv. ABC123"
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              autoCapitalize="characters"
              maxLength={8}
            />
            <Text style={[styles.helpText, isDark && styles.textSecondaryDark]}>
              Voer de 6-cijferige code in die je van de groepsbeheerder hebt ontvangen
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
});

GroupsScreen.displayName = 'GroupsScreen';
GroupCard.displayName = 'GroupCard';
CreateGroupModal.displayName = 'CreateGroupModal';
JoinGroupModal.displayName = 'JoinGroupModal';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 60,
    marginBottom: 16,
  },
  loadingSubtext: {
    fontSize: 16,
    color: '#666666',
  },
  textDark: {
    color: '#ffffff',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
    gap: 8,
  },
  headerButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#f0f0f0',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerButtonDark: {
    backgroundColor: '#2c2c2e',
  },
  headerButtonText: {
    fontSize: 16,
  },
  listContent: {
    padding: 16,
  },
  groupCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  groupCardDark: {
    backgroundColor: '#1c1c1e',
  },
  groupHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  groupAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  groupAvatarDark: {
    backgroundColor: '#0A84FF',
  },
  groupAvatarText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  groupInfo: {
    flex: 1,
  },
  groupName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  groupDescription: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  groupStats: {
    flexDirection: 'row',
    gap: 16,
  },
  groupStat: {
    fontSize: 12,
    color: '#666666',
  },
  groupArrow: {
    fontSize: 20,
    color: '#666666',
  },
  emptyState: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyIcon: {
    fontSize: 80,
    marginBottom: 20,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 12,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 32,
    lineHeight: 24,
  },
  emptyActions: {
    gap: 12,
    width: '100%',
    maxWidth: 280,
  },
  primaryButton: {
    backgroundColor: '#007AFF',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  primaryButtonDark: {
    backgroundColor: '#0A84FF',
  },
  primaryButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  secondaryButton: {
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
  },
  secondaryButtonDark: {
    backgroundColor: '#2c2c2e',
  },
  secondaryButtonText: {
    color: '#000000',
    fontSize: 16,
    fontWeight: '600',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalContainerDark: {
    backgroundColor: '#000000',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalHeaderDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
  },
  modalButton: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  saveButton: {
    color: '#007AFF',
  },
  disabledButton: {
    color: '#8e8e93',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  labelDark: {
    color: '#ffffff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
  },
  inputDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
    color: '#ffffff',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
    height: 80,
    textAlignVertical: 'top',
  },
  codeInput: {
    textAlign: 'center',
    fontSize: 18,
    fontWeight: 'bold',
    letterSpacing: 2,
  },
  helpText: {
    fontSize: 12,
    color: '#666666',
    marginTop: 8,
    lineHeight: 16,
  },
});

export default GroupsScreen;

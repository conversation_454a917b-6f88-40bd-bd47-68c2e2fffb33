name: React Native CI

on:
  push:
    branches:
      - '*'
  pull_request:
    branches: [ master ]

jobs:
  build:

    runs-on: ${{ matrix.os }}

    strategy:
      matrix:
        os: [macOS-latest]
        node-version: [14.x]

    steps:
    - uses: actions/checkout@v2
    - name: Use Node.js ${{ matrix.node-version }} on ${{ matrix.os }}
      uses: actions/setup-node@v1
      with:
        node-version: ${{ matrix.node-version }}
        registry-url: 'https://registry.npmjs.org'
    - run: yarn install --frozen-lockfile
    - run: yarn build
    - run: yarn lint-all

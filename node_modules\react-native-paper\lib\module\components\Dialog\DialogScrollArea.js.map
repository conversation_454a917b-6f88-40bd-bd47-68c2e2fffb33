{"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "DialogScrollArea", "props", "theme", "borderStyles", "borderColor", "isV3", "colors", "surfaceVariant", "borderTopWidth", "hairlineWidth", "borderBottomWidth", "createElement", "_extends", "style", "styles", "container", "v3Container", "children", "displayName", "create", "paddingHorizontal", "flexGrow", "flexShrink", "marginBottom"], "sourceRoot": "../../../../src", "sources": ["components/Dialog/DialogScrollArea.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAoBC,UAAU,EAAEC,IAAI,QAAmB,cAAc;AAIrE,SAASC,gBAAgB,QAAQ,oBAAoB;AAcrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAIC,KAAY,IAAK;EACzC,MAAMC,KAAK,GAAGH,gBAAgB,CAACE,KAAK,CAACC,KAAK,CAAC;EAC3C,MAAMC,YAAY,GAAG;IACnBC,WAAW,EAAEF,KAAK,CAACG,IAAI,GACnBH,KAAK,CAACI,MAAM,CAACC,cAAc,GAC3B,oBAAoB;IACxBC,cAAc,EAAEN,KAAK,CAACG,IAAI,GAAG,CAAC,GAAGR,UAAU,CAACY,aAAa;IACzDC,iBAAiB,EAAER,KAAK,CAACG,IAAI,GAAG,CAAC,GAAGR,UAAU,CAACY;EACjD,CAAC;EACD,oBACEb,KAAA,CAAAe,aAAA,CAACb,IAAI,EAAAc,QAAA,KACCX,KAAK;IACTY,KAAK,EAAE,CACLC,MAAM,CAACC,SAAS,EAChBZ,YAAY,EACZD,KAAK,CAACG,IAAI,IAAIS,MAAM,CAACE,WAAW,EAChCf,KAAK,CAACY,KAAK;EACX,IAEDZ,KAAK,CAACgB,QACH,CAAC;AAEX,CAAC;AAEDjB,gBAAgB,CAACkB,WAAW,GAAG,mBAAmB;AAElD,MAAMJ,MAAM,GAAGjB,UAAU,CAACsB,MAAM,CAAC;EAC/BJ,SAAS,EAAE;IACTK,iBAAiB,EAAE,EAAE;IACrBC,QAAQ,EAAE,CAAC;IACXC,UAAU,EAAE;EACd,CAAC;EACDN,WAAW,EAAE;IACXO,YAAY,EAAE;EAChB;AACF,CAAC,CAAC;AAEF,eAAevB,gBAAgB", "ignoreList": []}
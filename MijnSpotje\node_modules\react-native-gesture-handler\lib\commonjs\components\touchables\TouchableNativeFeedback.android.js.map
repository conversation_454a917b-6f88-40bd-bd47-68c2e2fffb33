{"version": 3, "sources": ["TouchableNativeFeedback.android.tsx"], "names": ["TouchableNativeFeedback", "Component", "getExtraButtonProps", "extraProps", "background", "props", "type", "borderless", "color", "attribute", "rippleRadius", "useForeground", "render", "style", "rest", "GenericTouchable", "defaultProps", "extraButtonProps", "rippleColor", "Platform", "OS", "Version"], "mappings": ";;;;;;;AAAA;;AACA;;AAEA;;;;;;;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMA,uBAAN,SAAsCC,eAAtC,CAA8E;AAU3F;AA0BAC,EAAAA,mBAAmB,GAAG;AACpB,UAAMC,UAA6C,GAAG,EAAtD;AACA,UAAM;AAAEC,MAAAA;AAAF,QAAiB,KAAKC,KAA5B;;AACA,QAAID,UAAJ,EAAgB;AACd;AACA;AACA,UAAIA,UAAU,CAACE,IAAX,KAAoB,eAAxB,EAAyC;AACvCH,QAAAA,UAAU,CAAC,YAAD,CAAV,GAA2BC,UAAU,CAACG,UAAtC;AACAJ,QAAAA,UAAU,CAAC,aAAD,CAAV,GAA4BC,UAAU,CAACI,KAAvC;AACD,OAHD,MAGO,IAAIJ,UAAU,CAACE,IAAX,KAAoB,kBAAxB,EAA4C;AACjDH,QAAAA,UAAU,CAAC,YAAD,CAAV,GACEC,UAAU,CAACK,SAAX,KAAyB,oCAD3B;AAED,OATa,CAUd;;;AACAN,MAAAA,UAAU,CAAC,cAAD,CAAV,GAA6BC,UAAU,CAACM,YAAxC;AACD;;AACDP,IAAAA,UAAU,CAAC,YAAD,CAAV,GAA2B,KAAKE,KAAL,CAAWM,aAAtC;AACA,WAAOR,UAAP;AACD;;AACDS,EAAAA,MAAM,GAAG;AACP,UAAM;AAAEC,MAAAA,KAAK,GAAG,EAAV;AAAc,SAAGC;AAAjB,QAA0B,KAAKT,KAArC;AACA,wBACE,oBAAC,yBAAD,eACMS,IADN;AAEE,MAAA,KAAK,EAAED,KAFT;AAGE,MAAA,gBAAgB,EAAE,KAAKX,mBAAL;AAHpB,OADF;AAOD;;AAhE0F;;;;gBAAxEF,uB,kBACG,EACpB,GAAGe,0BAAiBC,YADA;AAEpBL,EAAAA,aAAa,EAAE,IAFK;AAGpBM,EAAAA,gBAAgB,EAAE;AAChB;AACAC,IAAAA,WAAW,EAAE;AAFG;AAHE,C;;gBADHlB,uB,0BAWYU,YAAD,KAA4B;AACxDJ,EAAAA,IAAI,EAAE,kBADkD;AAExD;AACAG,EAAAA,SAAS,EAAE,0BAH6C;AAIxDC,EAAAA;AAJwD,CAA5B,C;;gBAXXV,uB,oCAiBsBU,YAAD,KAA4B;AAClEJ,EAAAA,IAAI,EAAE,kBAD4D;AAElEG,EAAAA,SAAS,EAAE,oCAFuD;AAGlEC,EAAAA;AAHkE,CAA5B,C;;gBAjBrBV,uB,YAsBH,CACdQ,KADc,EAEdD,UAFc,EAGdG,YAHc,MAIV;AACJJ,EAAAA,IAAI,EAAE,eADF;AAEJE,EAAAA,KAFI;AAGJD,EAAAA,UAHI;AAIJG,EAAAA;AAJI,CAJU,C;;gBAtBGV,uB,4BAiCa,MAC9BmB,sBAASC,EAAT,KAAgB,SAAhB,IAA6BD,sBAASE,OAAT,IAAoB,E", "sourcesContent": ["import { Platform, ColorValue } from 'react-native';\nimport * as React from 'react';\nimport { Component } from 'react';\nimport GenericTouchable from './GenericTouchable';\nimport {\n  TouchableNativeFeedbackProps,\n  TouchableNativeFeedbackExtraProps,\n} from './TouchableNativeFeedbackProps';\n\n/**\n * @deprecated TouchableNativeFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.\n *\n * TouchableNativeFeedback behaves slightly different than RN's TouchableNativeFeedback.\n * There's small difference with handling long press ripple since RN's implementation calls\n * ripple animation via bridge. This solution leaves all animations' handling for native components so\n * it follows native behaviours.\n */\nexport default class TouchableNativeFeedback extends Component<TouchableNativeFeedbackProps> {\n  static defaultProps = {\n    ...GenericTouchable.defaultProps,\n    useForeground: true,\n    extraButtonProps: {\n      // Disable hiding ripple on Android\n      rippleColor: null,\n    },\n  };\n\n  // Could be taken as RNTouchableNativeFeedback.SelectableBackground etc. but the API may change\n  static SelectableBackground = (rippleRadius?: number) => ({\n    type: 'ThemeAttrAndroid',\n    // I added `attribute` prop to clone the implementation of RN and be able to use only 2 types\n    attribute: 'selectableItemBackground',\n    rippleRadius,\n  });\n  static SelectableBackgroundBorderless = (rippleRadius?: number) => ({\n    type: 'ThemeAttrAndroid',\n    attribute: 'selectableItemBackgroundBorderless',\n    rippleRadius,\n  });\n  static Ripple = (\n    color: ColorValue,\n    borderless: boolean,\n    rippleRadius?: number\n  ) => ({\n    type: 'RippleAndroid',\n    color,\n    borderless,\n    rippleRadius,\n  });\n\n  static canUseNativeForeground = () =>\n    Platform.OS === 'android' && Platform.Version >= 23;\n\n  getExtraButtonProps() {\n    const extraProps: TouchableNativeFeedbackExtraProps = {};\n    const { background } = this.props;\n    if (background) {\n      // I changed type values to match those used in RN\n      // TODO(TS): check if it works the same as previous implementation - looks like it works the same as RN component, so it should be ok\n      if (background.type === 'RippleAndroid') {\n        extraProps['borderless'] = background.borderless;\n        extraProps['rippleColor'] = background.color;\n      } else if (background.type === 'ThemeAttrAndroid') {\n        extraProps['borderless'] =\n          background.attribute === 'selectableItemBackgroundBorderless';\n      }\n      // I moved it from above since it should be available in all options\n      extraProps['rippleRadius'] = background.rippleRadius;\n    }\n    extraProps['foreground'] = this.props.useForeground;\n    return extraProps;\n  }\n  render() {\n    const { style = {}, ...rest } = this.props;\n    return (\n      <GenericTouchable\n        {...rest}\n        style={style}\n        extraButtonProps={this.getExtraButtonProps()}\n      />\n    );\n  }\n}\n"]}
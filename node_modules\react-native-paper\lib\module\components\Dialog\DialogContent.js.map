{"version": 3, "names": ["React", "View", "StyleSheet", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "createElement", "_extends", "style", "styles", "container", "children", "displayName", "create", "paddingBottom", "paddingHorizontal"], "sourceRoot": "../../../../src", "sources": ["components/Dialog/DialogContent.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,EAAaC,UAAU,QAAmB,cAAc;AAUrE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAIC,KAAY,iBACjCJ,KAAA,CAAAK,aAAA,CAACJ,IAAI,EAAAK,QAAA,KAAKF,KAAK;EAAEG,KAAK,EAAE,CAACC,MAAM,CAACC,SAAS,EAAEL,KAAK,CAACG,KAAK;AAAE,IACrDH,KAAK,CAACM,QACH,CACP;AAEDP,aAAa,CAACQ,WAAW,GAAG,gBAAgB;AAE5C,MAAMH,MAAM,GAAGN,UAAU,CAACU,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,aAAa,EAAE,EAAE;IACjBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAEF,eAAeX,aAAa", "ignoreList": []}
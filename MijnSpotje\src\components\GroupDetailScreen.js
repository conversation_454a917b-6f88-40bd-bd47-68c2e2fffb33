import React, { useState, useEffect, useCallback, memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  FlatList,
  Alert,
  Modal,
  TextInput,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import GroupsService from '../services/GroupsService';
import ShareUtils from '../utils/ShareUtils';
import DeepLinkService from '../services/DeepLinkService';

const GroupDetailScreen = memo(({ group, isDark = false, onBack, t }) => {
  const [groupSpots, setGroupSpots] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [showSettingsModal, setShowSettingsModal] = useState(false);

  // Safety checks for required props
  if (!group || !group.id) {
    console.warn('⚠️ GroupDetailScreen: group prop is required');
    return null;
  }

  if (!onBack || typeof onBack !== 'function') {
    console.warn('⚠️ GroupDetailScreen: onBack prop is required');
    return null;
  }

  // Load group spots on mount
  useEffect(() => {
    loadGroupSpots();
  }, [group.id]);

  const loadGroupSpots = useCallback(async () => {
    try {
      setIsLoading(true);
      const spots = await GroupsService.getGroupSpots(group.id);
      setGroupSpots(Array.isArray(spots) ? spots : []);
      console.log(`📍 Loaded ${spots?.length || 0} spots for group: ${group.name}`);
    } catch (error) {
      console.error('❌ Error loading group spots:', error);
      setGroupSpots([]); // Fallback to empty array
    } finally {
      setIsLoading(false);
    }
  }, [group.id, group.name]);

  const handleShareInvite = useCallback(async () => {
    try {
      const linkData = DeepLinkService.generateInviteLink(group.inviteCode);
      
      const shareText = `🎉 Je bent uitgenodigd voor de groep "${group.name}"!

${group.description ? `📝 ${group.description}` : ''}

👥 ${group.stats.totalMembers} leden
📍 ${group.stats.totalSpots} gedeelde spots

🔗 Klik hier om deel te nemen: ${linkData.deepLink}

Of voer deze code in: ${group.inviteCode}

Gedeeld via Mijn Spotje 📱`;

      await ShareUtils.shareApp(); // Use custom share for invites
      
      // For now, use the formatted text
      Alert.alert(
        'Uitnodiging Delen',
        'Uitnodigingslink gekopieerd! Deel deze met je vrienden.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('❌ Error sharing invite:', error);
      Alert.alert('Fout', 'Kon uitnodiging niet delen', [{ text: 'OK' }]);
    }
  }, [group]);

  const handleRegenerateCode = useCallback(async () => {
    Alert.alert(
      'Code Vernieuwen',
      'Weet je zeker dat je een nieuwe uitnodigingscode wilt genereren? De oude code werkt dan niet meer.',
      [
        { text: 'Annuleren', style: 'cancel' },
        {
          text: 'Vernieuwen',
          onPress: async () => {
            try {
              await GroupsService.regenerateInviteCode(group.id);
              Alert.alert('Succes', 'Nieuwe uitnodigingscode gegenereerd!');
              // Refresh group data would be needed here
            } catch (error) {
              Alert.alert('Fout', 'Kon code niet vernieuwen');
            }
          }
        }
      ]
    );
  }, [group.id]);

  const handleLeaveGroup = useCallback(async () => {
    Alert.alert(
      'Groep Verlaten',
      `Weet je zeker dat je "${group.name}" wilt verlaten?`,
      [
        { text: 'Annuleren', style: 'cancel' },
        {
          text: 'Verlaten',
          style: 'destructive',
          onPress: async () => {
            try {
              const result = await GroupsService.leaveGroup(group.id);
              if (result.deleted) {
                Alert.alert('Groep Verwijderd', 'De groep is verwijderd omdat er geen leden meer waren.');
              } else {
                Alert.alert('Succes', 'Je hebt de groep verlaten.');
              }
              onBack();
            } catch (error) {
              Alert.alert('Fout', error.message || 'Kon groep niet verlaten');
            }
          }
        }
      ]
    );
  }, [group, onBack]);

  const renderSpotCard = useCallback(({ item: spot }) => (
    <GroupSpotCard
      spot={spot}
      isDark={isDark}
      onPress={() => {
        // Handle spot press - could open spot details
        console.log('Group spot pressed:', spot.title);
      }}
    />
  ), [isDark]);

  const keyExtractor = useCallback((item) => item.groupSpotId, []);

  const isAdmin = group && GroupsService.isGroupAdmin(group);

  return (
    <View style={[styles.container, isDark && styles.containerDark]}>
      <StatusBar style={isDark ? "light" : "dark"} />
      
      {/* Header */}
      <View style={[styles.header, isDark && styles.headerDark]}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, isDark && styles.textDark]}>{group.name}</Text>
        <TouchableOpacity onPress={() => setShowSettingsModal(true)}>
          <Text style={styles.settingsIcon}>⚙️</Text>
        </TouchableOpacity>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Group Info */}
        <View style={[styles.groupInfo, isDark && styles.groupInfoDark]}>
          <View style={[styles.groupAvatar, isDark && styles.groupAvatarDark]}>
            <Text style={styles.groupAvatarText}>
              {group.avatar || group.name.charAt(0).toUpperCase()}
            </Text>
          </View>
          
          <Text style={[styles.groupName, isDark && styles.textDark]}>{group.name}</Text>
          {group.description && (
            <Text style={[styles.groupDescription, isDark && styles.textSecondaryDark]}>
              {group.description}
            </Text>
          )}
          
          <View style={styles.groupStats}>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, isDark && styles.textDark]}>{group.stats.totalMembers}</Text>
              <Text style={[styles.statLabel, isDark && styles.textSecondaryDark]}>Leden</Text>
            </View>
            <View style={styles.statItem}>
              <Text style={[styles.statNumber, isDark && styles.textDark]}>{group.stats.totalSpots}</Text>
              <Text style={[styles.statLabel, isDark && styles.textSecondaryDark]}>Spots</Text>
            </View>
          </View>
        </View>

        {/* Action Buttons */}
        <View style={styles.actions}>
          <TouchableOpacity
            style={[styles.actionButton, styles.inviteButton]}
            onPress={() => setShowInviteModal(true)}
          >
            <Text style={styles.actionButtonText}>👥 Uitnodigen</Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.actionButton, styles.membersButton]}
            onPress={() => setShowMembersModal(true)}
          >
            <Text style={styles.actionButtonText}>👤 Leden</Text>
          </TouchableOpacity>
        </View>

        {/* Group Spots */}
        <View style={styles.spotsSection}>
          <Text style={[styles.sectionTitle, isDark && styles.textDark]}>Gedeelde Spots</Text>
          
          {isLoading ? (
            <View style={styles.loadingContainer}>
              <Text style={[styles.loadingText, isDark && styles.textSecondaryDark]}>
                Spots laden...
              </Text>
            </View>
          ) : groupSpots.length === 0 ? (
            <View style={styles.emptySpots}>
              <Text style={styles.emptyIcon}>📍</Text>
              <Text style={[styles.emptyText, isDark && styles.textDark]}>Nog geen spots</Text>
              <Text style={[styles.emptySubtext, isDark && styles.textSecondaryDark]}>
                Deel je eerste spot met de groep!
              </Text>
            </View>
          ) : (
            <FlatList
              data={groupSpots}
              renderItem={renderSpotCard}
              keyExtractor={keyExtractor}
              showsVerticalScrollIndicator={false}
              scrollEnabled={false}
            />
          )}
        </View>
      </ScrollView>

      {/* Invite Modal */}
      <InviteModal
        visible={showInviteModal}
        onClose={() => setShowInviteModal(false)}
        group={group}
        isDark={isDark}
        isAdmin={isAdmin}
        onShareInvite={handleShareInvite}
        onRegenerateCode={handleRegenerateCode}
      />

      {/* Members Modal */}
      <MembersModal
        visible={showMembersModal}
        onClose={() => setShowMembersModal(false)}
        group={group}
        isDark={isDark}
      />

      {/* Settings Modal */}
      <SettingsModal
        visible={showSettingsModal}
        onClose={() => setShowSettingsModal(false)}
        group={group}
        isDark={isDark}
        isAdmin={isAdmin}
        onLeaveGroup={handleLeaveGroup}
      />
    </View>
  );
});

// Group Spot Card Component
const GroupSpotCard = memo(({ spot, isDark, onPress }) => {
  const handlePress = useCallback(() => {
    onPress(spot);
  }, [spot, onPress]);

  return (
    <TouchableOpacity
      style={[styles.spotCard, isDark && styles.spotCardDark]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.spotHeader}>
        <Text style={[styles.spotTitle, isDark && styles.textDark]}>{spot.title}</Text>
        <View style={styles.spotRating}>
          <Text style={styles.star}>⭐</Text>
          <Text style={[styles.ratingText, isDark && styles.textDark]}>{spot.rating}</Text>
        </View>
      </View>
      <Text style={[styles.spotCategory, isDark && styles.categoryDark]}>{spot.category}</Text>
      <Text style={[styles.spotSharedBy, isDark && styles.textSecondaryDark]}>
        Gedeeld door {spot.sharedBy} • {new Date(spot.sharedAt).toLocaleDateString()}
      </Text>
    </TouchableOpacity>
  );
});

// Invite Modal Component
const InviteModal = memo(({ visible, onClose, group, isDark, isAdmin, onShareInvite, onRegenerateCode }) => (
  <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
    <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
      <View style={[styles.modalHeader, isDark && styles.modalHeaderDark]}>
        <TouchableOpacity onPress={onClose}>
          <Text style={styles.modalButton}>Sluiten</Text>
        </TouchableOpacity>
        <Text style={[styles.modalTitle, isDark && styles.textDark]}>Uitnodigen</Text>
        <View style={{ width: 60 }} />
      </View>

      <View style={styles.modalContent}>
        <View style={styles.inviteCodeContainer}>
          <Text style={[styles.inviteLabel, isDark && styles.textDark]}>Uitnodigingscode:</Text>
          <View style={[styles.codeDisplay, isDark && styles.codeDisplayDark]}>
            <Text style={[styles.codeText, isDark && styles.textDark]}>{group.inviteCode}</Text>
          </View>
          <Text style={[styles.codeExpiry, isDark && styles.textSecondaryDark]}>
            Verloopt op: {new Date(group.inviteExpiry).toLocaleDateString()}
          </Text>
        </View>

        <View style={styles.inviteActions}>
          <TouchableOpacity
            style={[styles.inviteActionButton, styles.shareInviteButton]}
            onPress={onShareInvite}
          >
            <Text style={styles.inviteActionText}>📤 Uitnodiging Delen</Text>
          </TouchableOpacity>

          {isAdmin && (
            <TouchableOpacity
              style={[styles.inviteActionButton, styles.regenerateButton]}
              onPress={onRegenerateCode}
            >
              <Text style={styles.inviteActionText}>🔄 Code Vernieuwen</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>
    </View>
  </Modal>
));

// Members Modal Component
const MembersModal = memo(({ visible, onClose, group, isDark }) => (
  <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
    <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
      <View style={[styles.modalHeader, isDark && styles.modalHeaderDark]}>
        <TouchableOpacity onPress={onClose}>
          <Text style={styles.modalButton}>Sluiten</Text>
        </TouchableOpacity>
        <Text style={[styles.modalTitle, isDark && styles.textDark]}>Leden ({group.members.length})</Text>
        <View style={{ width: 60 }} />
      </View>

      <ScrollView style={styles.modalContent}>
        {group.members.map((member) => (
          <View key={member.id} style={[styles.memberItem, isDark && styles.memberItemDark]}>
            <View style={[styles.memberAvatar, isDark && styles.memberAvatarDark]}>
              <Text style={styles.memberAvatarText}>{member.name.charAt(0).toUpperCase()}</Text>
            </View>
            <View style={styles.memberInfo}>
              <Text style={[styles.memberName, isDark && styles.textDark]}>{member.name}</Text>
              <Text style={[styles.memberRole, isDark && styles.textSecondaryDark]}>
                {member.role === 'admin' ? '👑 Beheerder' : '👤 Lid'}
              </Text>
            </View>
            <Text style={[styles.memberJoined, isDark && styles.textSecondaryDark]}>
              {new Date(member.joinedAt).toLocaleDateString()}
            </Text>
          </View>
        ))}
      </ScrollView>
    </View>
  </Modal>
));

// Settings Modal Component
const SettingsModal = memo(({ visible, onClose, group, isDark, isAdmin, onLeaveGroup }) => (
  <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
    <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
      <View style={[styles.modalHeader, isDark && styles.modalHeaderDark]}>
        <TouchableOpacity onPress={onClose}>
          <Text style={styles.modalButton}>Sluiten</Text>
        </TouchableOpacity>
        <Text style={[styles.modalTitle, isDark && styles.textDark]}>Groep Instellingen</Text>
        <View style={{ width: 60 }} />
      </View>

      <ScrollView style={styles.modalContent}>
        <View style={styles.settingsSection}>
          <Text style={[styles.sectionTitle, isDark && styles.textDark]}>Groep Informatie</Text>
          <Text style={[styles.settingItem, isDark && styles.textSecondaryDark]}>
            Aangemaakt: {new Date(group.createdAt).toLocaleDateString()}
          </Text>
          <Text style={[styles.settingItem, isDark && styles.textSecondaryDark]}>
            Groep ID: {group.id}
          </Text>
        </View>

        <View style={styles.dangerZone}>
          <TouchableOpacity
            style={[styles.dangerButton, isDark && styles.dangerButtonDark]}
            onPress={onLeaveGroup}
          >
            <Text style={styles.dangerButtonText}>🚪 Groep Verlaten</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  </Modal>
));

GroupDetailScreen.displayName = 'GroupDetailScreen';
GroupSpotCard.displayName = 'GroupSpotCard';
InviteModal.displayName = 'InviteModal';
MembersModal.displayName = 'MembersModal';
SettingsModal.displayName = 'SettingsModal';

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  backButton: {
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#007AFF',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
  },
  settingsIcon: {
    fontSize: 20,
    padding: 8,
  },
  textDark: {
    color: '#ffffff',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  content: {
    flex: 1,
  },
  groupInfo: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#f8f9fa',
    margin: 16,
    borderRadius: 12,
  },
  groupInfoDark: {
    backgroundColor: '#1c1c1e',
  },
  groupAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  groupAvatarDark: {
    backgroundColor: '#0A84FF',
  },
  groupAvatarText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  groupName: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
    textAlign: 'center',
  },
  groupDescription: {
    fontSize: 16,
    color: '#666666',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 22,
  },
  groupStats: {
    flexDirection: 'row',
    gap: 32,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
  },
  actions: {
    flexDirection: 'row',
    paddingHorizontal: 16,
    gap: 12,
    marginBottom: 24,
  },
  actionButton: {
    flex: 1,
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  inviteButton: {
    backgroundColor: '#007AFF',
  },
  membersButton: {
    backgroundColor: '#34C759',
  },
  actionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  spotsSection: {
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 16,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: '#666666',
  },
  emptySpots: {
    padding: 40,
    alignItems: 'center',
  },
  emptyIcon: {
    fontSize: 60,
    marginBottom: 16,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  spotCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  spotCardDark: {
    backgroundColor: '#1c1c1e',
  },
  spotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  spotTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  spotRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  star: {
    fontSize: 14,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  spotCategory: {
    fontSize: 12,
    color: '#007AFF',
    fontWeight: '500',
    marginBottom: 8,
    textTransform: 'capitalize',
  },
  categoryDark: {
    color: '#0A84FF',
  },
  spotSharedBy: {
    fontSize: 12,
    color: '#666666',
  },
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalContainerDark: {
    backgroundColor: '#000000',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalHeaderDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
  },
  modalButton: {
    fontSize: 16,
    color: '#007AFF',
    fontWeight: '600',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  inviteCodeContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  inviteLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 12,
  },
  codeDisplay: {
    backgroundColor: '#f0f0f0',
    borderRadius: 12,
    paddingVertical: 16,
    paddingHorizontal: 24,
    marginBottom: 8,
  },
  codeDisplayDark: {
    backgroundColor: '#2c2c2e',
  },
  codeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    letterSpacing: 4,
  },
  codeExpiry: {
    fontSize: 12,
    color: '#666666',
  },
  inviteActions: {
    gap: 12,
  },
  inviteActionButton: {
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  shareInviteButton: {
    backgroundColor: '#007AFF',
  },
  regenerateButton: {
    backgroundColor: '#FF9500',
  },
  inviteActionText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  memberItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    marginBottom: 8,
  },
  memberItemDark: {
    backgroundColor: '#1c1c1e',
  },
  memberAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#007AFF',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  memberAvatarDark: {
    backgroundColor: '#0A84FF',
  },
  memberAvatarText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#ffffff',
  },
  memberInfo: {
    flex: 1,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  memberRole: {
    fontSize: 12,
    color: '#666666',
  },
  memberJoined: {
    fontSize: 12,
    color: '#666666',
  },
  settingsSection: {
    marginBottom: 32,
  },
  settingItem: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 8,
  },
  dangerZone: {
    marginTop: 32,
  },
  dangerButton: {
    backgroundColor: '#ff3b30',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  dangerButtonDark: {
    backgroundColor: '#ff453a',
  },
  dangerButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default GroupDetailScreen;

import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, useColorScheme } from 'react-native';
import { StatusBar } from 'expo-status-bar';

// Memoized components for better performance
const WelcomeScreen = React.memo(({ onStart, isDark }) => (
  <View style={[styles.container, isDark && styles.containerDark]}>
    <Text style={[styles.text, isDark && styles.textDark]}>🗺️ Mijn Spotje</Text>
    <Text style={[styles.subtext, isDark && styles.subtextDark]}>
      Social Location Sharing
    </Text>
    <Text style={[styles.description, isDark && styles.descriptionDark]}>
      Ontdek en deel jouw favoriete plekken
    </Text>

    <TouchableOpacity
      style={[styles.button, isDark && styles.buttonDark]}
      onPress={onStart}
      activeOpacity={0.8}
    >
      <Text style={[styles.buttonText, isDark && styles.buttonTextDark]}>
        Start App ⚡
      </Text>
    </TouchableOpacity>

    <View style={styles.features}>
      <Text style={[styles.feature, isDark && styles.featureDark]}>✅ Expo Go Ready</Text>
      <Text style={[styles.feature, isDark && styles.featureDark]}>📱 Instant Loading</Text>
      <Text style={[styles.feature, isDark && styles.featureDark]}>🎯 Full Featured</Text>
    </View>
  </View>
));

// Lazy load the main app for better performance
const FastApp = React.lazy(() => import('./src/FastApp'));

const LoadingFallback = React.memo(({ isDark }) => (
  <View style={[styles.container, isDark && styles.containerDark]}>
    <Text style={[styles.text, isDark && styles.textDark]}>⚡ Loading...</Text>
    <View style={styles.loadingDots}>
      <View style={[styles.dot, isDark && styles.dotDark]} />
      <View style={[styles.dot, isDark && styles.dotDark]} />
      <View style={[styles.dot, isDark && styles.dotDark]} />
    </View>
  </View>
));

export default function App() {
  const [showApp, setShowApp] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const handleStart = useCallback(() => setShowApp(true), []);

  if (showApp) {
    return (
      <React.Suspense fallback={<LoadingFallback isDark={isDark} />}>
        <FastApp />
        <StatusBar style={isDark ? "light" : "dark"} />
      </React.Suspense>
    );
  }

  return (
    <>
      <WelcomeScreen onStart={handleStart} isDark={isDark} />
      <StatusBar style={isDark ? "light" : "dark"} />
    </>
  );
}

const styles = StyleSheet.create({
  // Light mode styles
  container: {
    flex: 1,
    backgroundColor: '#4A90E2',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  text: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#fff',
    textAlign: 'center',
  },
  subtext: {
    fontSize: 16,
    color: '#E8F4FD',
    marginBottom: 16,
    textAlign: 'center',
  },
  description: {
    fontSize: 14,
    color: '#E8F4FD',
    marginBottom: 32,
    textAlign: 'center',
    lineHeight: 20,
  },
  button: {
    backgroundColor: '#fff',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 20,
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  buttonText: {
    color: '#4A90E2',
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
  features: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    gap: 12,
  },
  feature: {
    fontSize: 12,
    color: '#E8F4FD',
    backgroundColor: 'rgba(255,255,255,0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.15)',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.3)',
  },
  backButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '500',
  },
  loadingDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 24,
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#fff',
    opacity: 0.7,
  },

  // Dark mode styles
  containerDark: {
    backgroundColor: '#1a1a1a',
  },
  textDark: {
    color: '#ffffff',
  },
  subtextDark: {
    color: '#b0b0b0',
  },
  descriptionDark: {
    color: '#888888',
  },
  buttonDark: {
    backgroundColor: '#333333',
    shadowColor: '#000',
    shadowOpacity: 0.3,
  },
  buttonTextDark: {
    color: '#ffffff',
  },
  featureDark: {
    color: '#b0b0b0',
    backgroundColor: 'rgba(255,255,255,0.05)',
  },
  backButtonDark: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderColor: 'rgba(255,255,255,0.2)',
  },
  backButtonTextDark: {
    color: '#ffffff',
  },
  dotDark: {
    backgroundColor: '#ffffff',
  },
});

import React, { useState, useCallback, Suspense } from 'react';
import { View, Text, StyleSheet, useColorScheme } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { I18nProvider } from './src/i18n/useTranslation';

// Lazy load components for better performance
const WelcomeScreen = React.lazy(() => import('./src/components/WelcomeScreen'));
const FastApp = React.lazy(() => import('./src/FastApp'));

// Optimized loading fallback
const LoadingFallback = React.memo(({ isDark }) => (
  <View style={[styles.container, isDark && styles.containerDark]}>
    <Text style={styles.loadingIcon}>⚡</Text>
    <Text style={[styles.text, isDark && styles.textDark]}>Mijn Spotje</Text>
    <Text style={[styles.subtext, isDark && styles.textSecondaryDark]}>Loading...</Text>
  </View>
));

function AppContent() {
  const [showApp, setShowApp] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const handleStart = useCallback(() => setShowApp(true), []);

  if (showApp) {
    return (
      <Suspense fallback={<LoadingFallback isDark={isDark} />}>
        <FastApp />
        <StatusBar style={isDark ? "light" : "dark"} />
      </Suspense>
    );
  }

  return (
    <Suspense fallback={<LoadingFallback isDark={isDark} />}>
      <WelcomeScreen onStart={handleStart} isDark={isDark} />
      <StatusBar style={isDark ? "light" : "dark"} />
    </Suspense>
  );
}

export default function App() {
  return (
    <I18nProvider>
      <AppContent />
    </I18nProvider>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  loadingIcon: {
    fontSize: 60,
    marginBottom: 16,
  },
  text: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  textDark: {
    color: '#ffffff',
  },
  subtext: {
    fontSize: 16,
    color: '#666666',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
});

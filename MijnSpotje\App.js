import React, { useState, useCallback } from 'react';
import { View, Text, StyleSheet, useColorScheme } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import WelcomeScreen from './src/components/WelcomeScreen';

// Lazy load the main app for better performance
const FastApp = React.lazy(() => import('./src/FastApp'));

const LoadingFallback = React.memo(({ isDark }) => (
  <View style={[styles.container, isDark && styles.containerDark]}>
    <Text style={[styles.text, isDark && styles.textDark]}>⚡ Loading...</Text>
    <View style={styles.loadingDots}>
      <View style={[styles.dot, isDark && styles.dotDark]} />
      <View style={[styles.dot, isDark && styles.dotDark]} />
      <View style={[styles.dot, isDark && styles.dotDark]} />
    </View>
  </View>
));

export default function App() {
  const [showApp, setShowApp] = useState(false);
  const colorScheme = useColorScheme();
  const isDark = colorScheme === 'dark';

  const handleStart = useCallback(() => setShowApp(true), []);

  if (showApp) {
    return (
      <React.Suspense fallback={<LoadingFallback isDark={isDark} />}>
        <FastApp />
        <StatusBar style={isDark ? "light" : "dark"} />
      </React.Suspense>
    );
  }

  return (
    <>
      <WelcomeScreen onStart={handleStart} isDark={isDark} />
      <StatusBar style={isDark ? "light" : "dark"} />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  text: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  textDark: {
    color: '#ffffff',
  },
  loadingDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginVertical: 24,
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007AFF',
    opacity: 0.7,
  },
  dotDark: {
    backgroundColor: '#ffffff',
  },
});

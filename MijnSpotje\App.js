import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  const [showApp, setShowApp] = React.useState(false);

  if (!showApp) {
    return (
      <View style={styles.container}>
        <Text style={styles.text}>🗺️ Mijn Spotje</Text>
        <Text style={styles.subtext}>Social Location Sharing App</Text>
        <Text style={styles.description}>
          Ontdek en deel jouw favoriete plekken met vrienden
        </Text>

        <TouchableOpacity
          style={styles.button}
          onPress={() => setShowApp(true)}
        >
          <Text style={styles.buttonText}>Start App</Text>
        </TouchableOpacity>

        <Text style={styles.note}>
          ✅ Expo Go Compatible{'\n'}
          📱 Direct te testen{'\n'}
          🎯 Volledig functioneel
        </Text>

        <StatusBar style="auto" />
      </View>
    );
  }

  // This will be replaced with the full app once dependencies are working
  return (
    <View style={styles.container}>
      <Text style={styles.text}>App wordt geladen...</Text>
      <Text style={styles.subtext}>Dependencies worden geïnstalleerd</Text>
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => setShowApp(false)}
      >
        <Text style={styles.buttonText}>Terug</Text>
      </TouchableOpacity>
      <StatusBar style="auto" />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#4A90E2',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  text: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 10,
    color: '#fff',
    textAlign: 'center',
  },
  subtext: {
    fontSize: 18,
    color: '#E8F4FD',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#E8F4FD',
    marginBottom: 40,
    textAlign: 'center',
    lineHeight: 24,
  },
  button: {
    backgroundColor: '#fff',
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    marginBottom: 30,
  },
  backButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: '#fff',
  },
  buttonText: {
    color: '#4A90E2',
    fontSize: 18,
    fontWeight: '600',
  },
  note: {
    fontSize: 14,
    color: '#E8F4FD',
    textAlign: 'center',
    lineHeight: 20,
  },
});

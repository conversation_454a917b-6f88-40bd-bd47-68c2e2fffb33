{"version": 3, "names": ["React", "NavigationFocusedRouteStateContext", "useStateForPath", "state", "useContext"], "sourceRoot": "../../src", "sources": ["useStateForPath.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,kCAAkC,QAAQ,yCAAsC;;AAEzF;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAMC,KAAK,GAAGH,KAAK,CAACI,UAAU,CAACH,kCAAkC,CAAC;EAElE,OAAOE,KAAK;AACd", "ignoreList": []}
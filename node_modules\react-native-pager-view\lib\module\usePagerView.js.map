{"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "Animated", "useCallback", "useMemo", "useRef", "useState", "AnimatedPagerView", "createAnimatedComponent", "usePagerView", "pagesAmount", "ref", "pages", "setPages", "Array", "fill", "map", "_v", "index", "activePage", "setActivePage", "isAnimated", "setIsAnimated", "overdragEnabled", "setOverdragEnabled", "scrollEnabled", "setScrollEnabled", "scrollState", "setScrollState", "progress", "setProgress", "position", "offset", "onPageScrollOffset", "Value", "current", "onPageScrollPosition", "onPageSelectedPosition", "setPage", "page", "_ref$current", "_ref$current2", "setPageWithoutAnimation", "addPage", "prevPages", "length", "removePage", "slice", "toggleAnimation", "animated", "toggleScroll", "enabled", "toggle<PERSON>verdrag", "onPageScroll", "event", "nativeEvent", "listener", "useNativeDriver", "onPageSelected", "onPageScrollStateChanged", "e", "pageScrollState"], "sources": ["usePagerView.ts"], "sourcesContent": ["import type * as ReactNative from 'react-native';\nimport type {\n  OnPageScrollEventData as PagerViewOnPageScrollEventData,\n  OnPageSelectedEventData as PagerViewOnPageSelectedEventData,\n  OnPageScrollStateChangedEventData as PageScrollStateChangedNativeEventData,\n} from './PagerViewNativeComponent';\n\ntype PageScrollStateChangedNativeEvent =\n  ReactNative.NativeSyntheticEvent<PageScrollStateChangedNativeEventData>;\n\nimport { PagerView } from './PagerView';\n\nimport { Animated } from 'react-native';\nimport { useCallback, useMemo, useRef, useState } from 'react';\n\nexport type UsePagerViewProps = ReturnType<typeof usePagerView>;\n\nconst AnimatedPagerView = Animated.createAnimatedComponent(PagerView);\n\ntype UsePagerViewParams = {\n  pagesAmount: number;\n};\n\nexport function usePagerView(\n  { pagesAmount }: UsePagerViewParams = { pagesAmount: 0 }\n) {\n  const ref = useRef<PagerView>(null);\n  const [pages, setPages] = useState<number[]>(\n    new Array(pagesAmount).fill('').map((_v, index) => index)\n  );\n  const [activePage, setActivePage] = useState(0);\n  const [isAnimated, setIsAnimated] = useState(true);\n  const [overdragEnabled, setOverdragEnabled] = useState(false);\n  const [scrollEnabled, setScrollEnabled] = useState(true);\n  const [scrollState, setScrollState] = useState('idle');\n  const [progress, setProgress] = useState({ position: 0, offset: 0 });\n  const onPageScrollOffset = useRef(new Animated.Value(0)).current;\n  const onPageScrollPosition = useRef(new Animated.Value(0)).current;\n  const onPageSelectedPosition = useRef(new Animated.Value(0)).current;\n\n  const setPage = useCallback(\n    (page: number) =>\n      isAnimated\n        ? ref.current?.setPage(page)\n        : ref.current?.setPageWithoutAnimation(page),\n    [isAnimated]\n  );\n\n  const addPage = useCallback(() => {\n    setPages((prevPages) => {\n      return [...prevPages, prevPages.length];\n    });\n  }, []);\n\n  const removePage = useCallback(() => {\n    setPages((prevPages) => {\n      if (prevPages.length === 1) {\n        return prevPages;\n      }\n      return prevPages.slice(0, prevPages.length - 1);\n    });\n  }, []);\n\n  const toggleAnimation = useCallback(\n    () => setIsAnimated((animated) => !animated),\n    []\n  );\n\n  const toggleScroll = useCallback(\n    () => setScrollEnabled((enabled) => !enabled),\n    []\n  );\n\n  const toggleOverdrag = useCallback(\n    () => setOverdragEnabled((enabled) => !enabled),\n    []\n  );\n\n  const onPageScroll = useMemo(\n    () =>\n      Animated.event<PagerViewOnPageScrollEventData>(\n        [\n          {\n            nativeEvent: {\n              offset: onPageScrollOffset,\n              position: onPageScrollPosition,\n            },\n          },\n        ],\n        {\n          listener: ({ nativeEvent: { offset, position } }) => {\n            setProgress({\n              position,\n              offset,\n            });\n          },\n          useNativeDriver: true,\n        }\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    []\n  );\n\n  const onPageSelected = useMemo(\n    () =>\n      Animated.event<PagerViewOnPageSelectedEventData>(\n        [{ nativeEvent: { position: onPageSelectedPosition } }],\n        {\n          listener: ({ nativeEvent: { position } }) => {\n            setActivePage(position);\n          },\n          useNativeDriver: true,\n        }\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    []\n  );\n\n  const onPageScrollStateChanged = useCallback(\n    (e: PageScrollStateChangedNativeEvent) => {\n      setScrollState(e.nativeEvent.pageScrollState);\n    },\n    []\n  );\n\n  return {\n    ref,\n    activePage,\n    isAnimated,\n    pages,\n    scrollState,\n    scrollEnabled,\n    progress,\n    overdragEnabled,\n    setPage,\n    addPage,\n    removePage,\n    toggleScroll,\n    toggleAnimation,\n    setProgress,\n    onPageScroll,\n    onPageSelected,\n    onPageScrollStateChanged,\n    toggleOverdrag,\n    AnimatedPagerView,\n    PagerView,\n  };\n}\n"], "mappings": "AAUA,SAASA,SAAS,QAAQ,aAAa;AAEvC,SAASC,QAAQ,QAAQ,cAAc;AACvC,SAASC,WAAW,EAAEC,OAAO,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,OAAO;AAI9D,MAAMC,iBAAiB,GAAGL,QAAQ,CAACM,uBAAuB,CAACP,SAAS,CAAC;AAMrE,OAAO,SAASQ,YAAYA,CAC1B;EAAEC;AAAgC,CAAC,GAAG;EAAEA,WAAW,EAAE;AAAE,CAAC,EACxD;EACA,MAAMC,GAAG,GAAGN,MAAM,CAAY,IAAI,CAAC;EACnC,MAAM,CAACO,KAAK,EAAEC,QAAQ,CAAC,GAAGP,QAAQ,CAChC,IAAIQ,KAAK,CAACJ,WAAW,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAKA,KAAK,CAC1D,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGd,QAAQ,CAAC,CAAC,CAAC;EAC/C,MAAM,CAACe,UAAU,EAAEC,aAAa,CAAC,GAAGhB,QAAQ,CAAC,IAAI,CAAC;EAClD,MAAM,CAACiB,eAAe,EAAEC,kBAAkB,CAAC,GAAGlB,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACmB,aAAa,EAAEC,gBAAgB,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EACxD,MAAM,CAACqB,WAAW,EAAEC,cAAc,CAAC,GAAGtB,QAAQ,CAAC,MAAM,CAAC;EACtD,MAAM,CAACuB,QAAQ,EAAEC,WAAW,CAAC,GAAGxB,QAAQ,CAAC;IAAEyB,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EACpE,MAAMC,kBAAkB,GAAG5B,MAAM,CAAC,IAAIH,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAChE,MAAMC,oBAAoB,GAAG/B,MAAM,CAAC,IAAIH,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAClE,MAAME,sBAAsB,GAAGhC,MAAM,CAAC,IAAIH,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAEpE,MAAMG,OAAO,GAAGnC,WAAW,CACxBoC,IAAY;IAAA,IAAAC,YAAA,EAAAC,aAAA;IAAA,OACXpB,UAAU,IAAAmB,YAAA,GACN7B,GAAG,CAACwB,OAAO,cAAAK,YAAA,uBAAXA,YAAA,CAAaF,OAAO,CAACC,IAAI,CAAC,IAAAE,aAAA,GAC1B9B,GAAG,CAACwB,OAAO,cAAAM,aAAA,uBAAXA,aAAA,CAAaC,uBAAuB,CAACH,IAAI,CAAC;EAAA,GAChD,CAAClB,UAAU,CACb,CAAC;EAED,MAAMsB,OAAO,GAAGxC,WAAW,CAAC,MAAM;IAChCU,QAAQ,CAAE+B,SAAS,IAAK;MACtB,OAAO,CAAC,GAAGA,SAAS,EAAEA,SAAS,CAACC,MAAM,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,UAAU,GAAG3C,WAAW,CAAC,MAAM;IACnCU,QAAQ,CAAE+B,SAAS,IAAK;MACtB,IAAIA,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAOD,SAAS;MAClB;MACA,OAAOA,SAAS,CAACG,KAAK,CAAC,CAAC,EAAEH,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,eAAe,GAAG7C,WAAW,CACjC,MAAMmB,aAAa,CAAE2B,QAAQ,IAAK,CAACA,QAAQ,CAAC,EAC5C,EACF,CAAC;EAED,MAAMC,YAAY,GAAG/C,WAAW,CAC9B,MAAMuB,gBAAgB,CAAEyB,OAAO,IAAK,CAACA,OAAO,CAAC,EAC7C,EACF,CAAC;EAED,MAAMC,cAAc,GAAGjD,WAAW,CAChC,MAAMqB,kBAAkB,CAAE2B,OAAO,IAAK,CAACA,OAAO,CAAC,EAC/C,EACF,CAAC;EAED,MAAME,YAAY,GAAGjD,OAAO,CAC1B,MACEF,QAAQ,CAACoD,KAAK,CACZ,CACE;IACEC,WAAW,EAAE;MACXvB,MAAM,EAAEC,kBAAkB;MAC1BF,QAAQ,EAAEK;IACZ;EACF,CAAC,CACF,EACD;IACEoB,QAAQ,EAAEA,CAAC;MAAED,WAAW,EAAE;QAAEvB,MAAM;QAAED;MAAS;IAAE,CAAC,KAAK;MACnDD,WAAW,CAAC;QACVC,QAAQ;QACRC;MACF,CAAC,CAAC;IACJ,CAAC;IACDyB,eAAe,EAAE;EACnB,CACF,CAAC;EACH;EACA,EACF,CAAC;EAED,MAAMC,cAAc,GAAGtD,OAAO,CAC5B,MACEF,QAAQ,CAACoD,KAAK,CACZ,CAAC;IAAEC,WAAW,EAAE;MAAExB,QAAQ,EAAEM;IAAuB;EAAE,CAAC,CAAC,EACvD;IACEmB,QAAQ,EAAEA,CAAC;MAAED,WAAW,EAAE;QAAExB;MAAS;IAAE,CAAC,KAAK;MAC3CX,aAAa,CAACW,QAAQ,CAAC;IACzB,CAAC;IACD0B,eAAe,EAAE;EACnB,CACF,CAAC;EACH;EACA,EACF,CAAC;EAED,MAAME,wBAAwB,GAAGxD,WAAW,CACzCyD,CAAoC,IAAK;IACxChC,cAAc,CAACgC,CAAC,CAACL,WAAW,CAACM,eAAe,CAAC;EAC/C,CAAC,EACD,EACF,CAAC;EAED,OAAO;IACLlD,GAAG;IACHQ,UAAU;IACVE,UAAU;IACVT,KAAK;IACLe,WAAW;IACXF,aAAa;IACbI,QAAQ;IACRN,eAAe;IACfe,OAAO;IACPK,OAAO;IACPG,UAAU;IACVI,YAAY;IACZF,eAAe;IACflB,WAAW;IACXuB,YAAY;IACZK,cAAc;IACdC,wBAAwB;IACxBP,cAAc;IACd7C,iBAAiB;IACjBN;EACF,CAAC;AACH", "ignoreList": []}
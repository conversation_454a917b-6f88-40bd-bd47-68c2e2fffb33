{"name": "react-native-uuid", "version": "2.0.3", "description": "react-native-uuid is a zero-dependency TypeScript implementation of RFC4122.", "main": "dist/index.js", "types": "dist/index.d.ts", "engines": {"node": ">=10.0.0", "npm": ">=6.0.0"}, "scripts": {"build": "tsc", "prepublishOnly": "npm run build", "test": "ts-jest", "docs": "typedoc ./src --entryDocument .", "prettier:write": "npx prettier --write **/*.{js,jsx,ts,tsx,json} && npx prettier --write *.{js,jsx,ts,tsx,json}", "lint": "tsc --noEmit && eslint --ext .js,.jsx,.ts,.tsx ./"}, "repository": {"type": "git", "url": "git+https://github.com/eugenehp/react-native-uuid.git"}, "keywords": ["uuid", "rfc4122", "react-native", "reactivelions"], "author": "<PERSON>", "license": "MIT", "bugs": {"url": "https://github.com/eugenehp/react-native-uuid/issues"}, "homepage": "https://github.com/eugenehp/react-native-uuid#readme", "devDependencies": {"@babel/core": "^7.12.9", "@babel/runtime": "^7.12.5", "@react-native-community/eslint-config": "^2.0.0", "@types/jest": "^26.0.20", "babel-jest": "^29.7.0", "eslint": "^7.14.0", "jest": "^29.7.0", "metro-react-native-babel-preset": "^0.64.0", "react-test-renderer": "17.0.1", "tslint": "^6.1.3", "typedoc": "^0.20.35", "typedoc-plugin-markdown": "^3.6.1", "typescript": "^3.8.3"}, "jest": {"preset": "react-native", "moduleFileExtensions": ["ts", "tsx", "js", "jsx", "json", "node"]}}
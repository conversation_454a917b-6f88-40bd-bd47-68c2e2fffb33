{"version": 3, "names": ["color", "black", "pinkA400", "white", "configure<PERSON>onts", "MD2LightTheme", "dark", "roundness", "version", "isV3", "colors", "primary", "accent", "background", "surface", "error", "text", "onSurface", "disabled", "alpha", "rgb", "string", "placeholder", "backdrop", "notification", "tooltip", "fonts", "animation", "scale"], "sourceRoot": "../../../../../src", "sources": ["styles/themes/v2/LightTheme.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,UAAU;AAEjD,OAAOC,cAAc,MAAM,aAAa;AAExC,OAAO,MAAMC,aAAuB,GAAG;EACrCC,IAAI,EAAE,KAAK;EACXC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE,CAAC;EACVC,IAAI,EAAE,KAAK;EACXC,MAAM,EAAE;IACNC,OAAO,EAAE,SAAS;IAClBC,MAAM,EAAE,SAAS;IACjBC,UAAU,EAAE,SAAS;IACrBC,OAAO,EAAEX,KAAK;IACdY,KAAK,EAAE,SAAS;IAChBC,IAAI,EAAEf,KAAK;IACXgB,SAAS,EAAE,SAAS;IACpBC,QAAQ,EAAElB,KAAK,CAACC,KAAK,CAAC,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjDC,WAAW,EAAEtB,KAAK,CAACC,KAAK,CAAC,CAACkB,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACpDE,QAAQ,EAAEvB,KAAK,CAACC,KAAK,CAAC,CAACkB,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAChDG,YAAY,EAAEtB,QAAQ;IACtBuB,OAAO,EAAE;EACX,CAAC;EACDC,KAAK,EAAEtB,cAAc,CAAC;IAAEK,IAAI,EAAE;EAAM,CAAC,CAAU;EAC/CkB,SAAS,EAAE;IACTC,KAAK,EAAE;EACT;AACF,CAAC", "ignoreList": []}
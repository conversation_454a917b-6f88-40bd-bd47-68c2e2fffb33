{"version": 3, "sources": ["EnableNewWebImplementation.ts"], "names": ["useNewWebImplementation", "getWasCalled", "enableExperimentalWebImplementation", "_shouldEnable", "console", "warn", "enableLegacyWebImplementation", "shouldUseLegacyImplementation", "Platform", "OS", "error", "isNewWebImplementationEnabled"], "mappings": ";;;;;;;;;AAAA;;AACA;;AAEA,IAAIA,uBAAuB,GAAG,IAA9B;AACA,IAAIC,YAAY,GAAG,KAAnB;AAEA;AACA;AACA;;AACO,SAASC,mCAAT,CACLC,aAAa,GAAG,IADX,EAEC;AACN;AACAC,EAAAA,OAAO,CAACC,IAAR,CACE,uBACE,mGADF,CADF;AAKD;AAED;AACA;AACA;;;AACO,SAASC,6BAAT,CACLC,6BAA6B,GAAG,IAD3B,EAEC;AACNH,EAAAA,OAAO,CAACC,IAAR,CACE,uBACE,8FADF,CADF;;AAMA,MACEG,sBAASC,EAAT,KAAgB,KAAhB,IACAT,uBAAuB,KAAK,CAACO,6BAF/B,EAGE;AACA;AACD;;AAED,MAAIN,YAAJ,EAAkB;AAChBG,IAAAA,OAAO,CAACM,KAAR,CACE,mLADF;AAGA;AACD;;AAEDV,EAAAA,uBAAuB,GAAG,CAACO,6BAA3B;AACD;;AAEM,SAASI,6BAAT,GAAkD;AACvDV,EAAAA,YAAY,GAAG,IAAf;AACA,SAAOD,uBAAP;AACD", "sourcesContent": ["import { Platform } from 'react-native';\nimport { tagMessage } from './utils';\n\nlet useNewWebImplementation = true;\nlet getWasCalled = false;\n\n/**\n * @deprecated new web implementation is enabled by default. This function will be removed in Gesture Handler 3\n */\nexport function enableExperimentalWebImplementation(\n  _shouldEnable = true\n): void {\n  // NO-OP since the new implementation is now the default\n  console.warn(\n    tagMessage(\n      'New web implementation is enabled by default. This function will be removed in Gesture Handler 3.'\n    )\n  );\n}\n\n/**\n * @deprecated legacy implementation is no longer supported. This function will be removed in Gesture Handler 3\n */\nexport function enableLegacyWebImplementation(\n  shouldUseLegacyImplementation = true\n): void {\n  console.warn(\n    tagMessage(\n      'Legacy web implementation is deprecated. This function will be removed in Gesture Handler 3.'\n    )\n  );\n\n  if (\n    Platform.OS !== 'web' ||\n    useNewWebImplementation === !shouldUseLegacyImplementation\n  ) {\n    return;\n  }\n\n  if (getWasCalled) {\n    console.error(\n      'Some parts of this application have already started using the new gesture handler implementation. No changes will be applied. You can try enabling legacy implementation earlier.'\n    );\n    return;\n  }\n\n  useNewWebImplementation = !shouldUseLegacyImplementation;\n}\n\nexport function isNewWebImplementationEnabled(): boolean {\n  getWasCalled = true;\n  return useNewWebImplementation;\n}\n"]}
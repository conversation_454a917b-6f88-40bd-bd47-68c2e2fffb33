{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_AnimatedText", "_interopRequireDefault", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "LabelBackground", "labeled", "labelLayoutWidth", "labelLayoutHeight", "placeholder<PERSON><PERSON><PERSON>", "baseLabelTranslateX", "topPosition", "backgroundColor", "roundness", "labelStyle", "maxFontSizeMultiplier", "testID", "opacity", "interpolate", "inputRange", "outputRange", "labelTranslationX", "translateX", "labelTextScaleY", "scaleY", "labelTextTransform", "transform", "isRounded", "roundedEdgeCover", "createElement", "Animated", "View", "key", "pointerEvents", "style", "StyleSheet", "absoluteFill", "styles", "view", "maxHeight", "Math", "max", "bottom", "<PERSON><PERSON><PERSON><PERSON>", "top", "width", "paddingHorizontal", "height", "numberOfLines", "_default", "exports", "create", "position", "left", "color"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Label/LabelBackground.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,aAAA,GAAAC,sBAAA,CAAAH,OAAA;AAAyD,SAAAG,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAK,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAGzD,MAAMgB,eAAe,GAAGA,CAAC;EACvBC,OAAO;EACPC,gBAAgB;EAChBC,iBAAiB;EACjBC,gBAAgB;EAChBC,mBAAmB;EACnBC,WAAW;EACXC,eAAe;EACfC,SAAS;EACTC,UAAU;EACVC,qBAAqB;EACrBC;AACoB,CAAC,KAAK;EAC1B,MAAMC,OAAO,GAAGX,OAAO,CAACY,WAAW,CAAC;IAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAG;IACxBC,UAAU,EAAEhB,OAAO,CAACY,WAAW,CAAC;MAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAACV,mBAAmB,EAAE,CAAC;IACvC,CAAC;EACH,CAAC;EAED,MAAMa,eAAe,GAAG;IACtBC,MAAM,EAAElB,OAAO,CAACY,WAAW,CAAC;MAC1BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;EACH,CAAC;EAED,MAAMK,kBAAkB,GAAG,CAAC,GAAGX,UAAU,CAACY,SAAS,EAAEH,eAAe,CAAC;EAErE,MAAMI,SAAS,GAAGd,SAAS,GAAG,CAAC;EAC/B,MAAMe,gBAAgB,GAAGD,SAAS,gBAChC/C,KAAA,CAAAiD,aAAA,CAAC9C,YAAA,CAAA+C,QAAQ,CAACC,IAAI;IACZC,GAAG,EAAC,sBAAsB;IAC1BC,aAAa,EAAC,MAAM;IACpBC,KAAK,EAAE,CACLC,uBAAU,CAACC,YAAY,EACvBC,MAAM,CAACC,IAAI,EACX;MACE1B,eAAe;MACf2B,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC5B,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC;MACrC6B,MAAM,EAAEF,IAAI,CAACC,GAAG,CAAC5B,SAAS,EAAE,CAAC,CAAC;MAC9Ba,SAAS,EAAE,CAACL,iBAAiB,CAAC;MAC9BJ;IACF,CAAC;EACD,CACH,CAAC,GACA,IAAI;EAER,OAAO,CACLW,gBAAgB,eAChBhD,KAAA,CAAAiD,aAAA,CAAC7C,aAAA,CAAAI,OAAY;IACX4C,GAAG,EAAC,sBAAsB;IAC1BhB,MAAM,EAAE,GAAGA,MAAM,mBAAoB;IACrCkB,KAAK,EAAE,CACLzB,gBAAgB,EAChBK,UAAU,EACVuB,MAAM,CAACM,aAAa,EACpB;MACEC,GAAG,EAAEjC,WAAW,GAAG,CAAC;MACpBkC,KAAK,EAAEtC,gBAAgB,GAAGE,gBAAgB,CAACqC,iBAAiB;MAC5DC,MAAM,EAAEvC,iBAAiB;MACzBI,eAAe;MACfK,OAAO;MACPS,SAAS,EAAED;IACb,CAAC,CACD;IACFuB,aAAa,EAAE,CAAE;IACjBjC,qBAAqB,EAAEA;EAAsB,CAC9C,CAAC,CACH;AACH,CAAC;AAAC,IAAAkC,QAAA,GAAAC,OAAA,CAAA9D,OAAA,GAEaiB,eAAe;AAE9B,MAAMgC,MAAM,GAAGF,uBAAU,CAACgB,MAAM,CAAC;EAC/Bb,IAAI,EAAE;IACJc,QAAQ,EAAE,UAAU;IACpBR,GAAG,EAAE,CAAC;IACNS,IAAI,EAAE,EAAE;IACRR,KAAK,EAAE;EACT,CAAC;EACD;EACAF,aAAa,EAAE;IACbS,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPP,iBAAiB,EAAE,CAAC;IACpBQ,KAAK,EAAE;EACT;AACF,CAAC,CAAC", "ignoreList": []}
{"version": 3, "names": ["React", "Animated", "I18nManager", "Platform", "StyleSheet", "View", "setColor", "useInternalTheme", "INDETERMINATE_DURATION", "INDETERMINATE_MAX_WIDTH", "isRTL", "ProgressBar", "color", "indeterminate", "progress", "visible", "theme", "themeOverrides", "animatedValue", "style", "fillStyle", "testID", "rest", "_theme$colors", "isWeb", "OS", "current", "timer", "useRef", "Value", "fade", "passedAnimatedValue", "width", "<PERSON><PERSON><PERSON><PERSON>", "useState", "prevWidth", "set<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indeterminateAnimation", "scale", "animation", "useEffect", "startAnimation", "useCallback", "timing", "duration", "toValue", "useNativeDriver", "isInteraction", "start", "externalAnimation", "setValue", "loop", "stopAnimation", "stop", "onLayout", "event", "nativeEvent", "layout", "tintColor", "colors", "primary", "trackTintColor", "isV3", "surfaceVariant", "alpha", "rgb", "string", "createElement", "_extends", "accessible", "accessibilityRole", "accessibilityState", "busy", "accessibilityValue", "min", "max", "now", "Math", "round", "styles", "webContainer", "container", "backgroundColor", "opacity", "progressBar", "transform", "translateX", "interpolate", "inputRange", "outputRange", "scaleX", "create", "height", "overflow", "flex"], "sourceRoot": "../../../src", "sources": ["components/ProgressBar.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,WAAW,EAEXC,QAAQ,EAERC,UAAU,EACVC,IAAI,QAEC,cAAc;AAErB,OAAOC,QAAQ,MAAM,OAAO;AAE5B,SAASC,gBAAgB,QAAQ,iBAAiB;AAyClD,MAAMC,sBAAsB,GAAG,IAAI;AACnC,MAAMC,uBAAuB,GAAG,GAAG;AACnC,MAAM;EAAEC;AAAM,CAAC,GAAGR,WAAW;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMS,WAAW,GAAGA,CAAC;EACnBC,KAAK;EACLC,aAAa;EACbC,QAAQ,GAAG,CAAC;EACZC,OAAO,GAAG,IAAI;EACdC,KAAK,EAAEC,cAAc;EACrBC,aAAa;EACbC,KAAK;EACLC,SAAS;EACTC,MAAM,GAAG,cAAc;EACvB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMC,KAAK,GAAGrB,QAAQ,CAACsB,EAAE,KAAK,KAAK;EACnC,MAAMT,KAAK,GAAGT,gBAAgB,CAACU,cAAc,CAAC;EAC9C,MAAM;IAAES,OAAO,EAAEC;EAAM,CAAC,GAAG3B,KAAK,CAAC4B,MAAM,CACrC,IAAI3B,QAAQ,CAAC4B,KAAK,CAAC,CAAC,CACtB,CAAC;EACD,MAAM;IAAEH,OAAO,EAAEI;EAAK,CAAC,GAAG9B,KAAK,CAAC4B,MAAM,CAAiB,IAAI3B,QAAQ,CAAC4B,KAAK,CAAC,CAAC,CAAC,CAAC;EAC7E,MAAME,mBAAmB,GACvB/B,KAAK,CAAC4B,MAAM,CAAyBV,aAAa,CAAC;EACrD,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGjC,KAAK,CAACkC,QAAQ,CAAS,CAAC,CAAC;EACnD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGpC,KAAK,CAACkC,QAAQ,CAAS,CAAC,CAAC;EAE3D,MAAMG,sBAAsB,GAC1BrC,KAAK,CAAC4B,MAAM,CAAqC,IAAI,CAAC;EAExD,MAAM;IAAEU;EAAM,CAAC,GAAGtB,KAAK,CAACuB,SAAS;EAEjCvC,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpBT,mBAAmB,CAACL,OAAO,GAAGR,aAAa;EAC7C,CAAC,CAAC;EAEF,MAAMuB,cAAc,GAAGzC,KAAK,CAAC0C,WAAW,CAAC,MAAM;IAC7C;IACAzC,QAAQ,CAAC0C,MAAM,CAACb,IAAI,EAAE;MACpBc,QAAQ,EAAE,GAAG,GAAGN,KAAK;MACrBO,OAAO,EAAE,CAAC;MACVC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;;IAEV;AACJ;AACA;AACA;AACA;AACA;IACI,MAAMC,iBAAiB,GACrB,OAAOlB,mBAAmB,CAACL,OAAO,KAAK,WAAW,IAClDK,mBAAmB,CAACL,OAAO,IAAI,CAAC;IAElC,IAAIuB,iBAAiB,EAAE;MACrB;IACF;;IAEA;IACA,IAAIpC,aAAa,EAAE;MACjB,IAAI,CAACwB,sBAAsB,CAACX,OAAO,EAAE;QACnCW,sBAAsB,CAACX,OAAO,GAAGzB,QAAQ,CAAC0C,MAAM,CAAChB,KAAK,EAAE;UACtDiB,QAAQ,EAAEpC,sBAAsB;UAChCqC,OAAO,EAAE,CAAC;UACV;UACAC,eAAe,EAAE,CAACtB,KAAK;UACvBuB,aAAa,EAAE;QACjB,CAAC,CAAC;MACJ;;MAEA;MACApB,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAAC;MAEjBjD,QAAQ,CAACkD,IAAI,CAACd,sBAAsB,CAACX,OAAO,CAAC,CAACsB,KAAK,CAAC,CAAC;IACvD,CAAC,MAAM;MACL/C,QAAQ,CAAC0C,MAAM,CAAChB,KAAK,EAAE;QACrBiB,QAAQ,EAAE,GAAG,GAAGN,KAAK;QACrBO,OAAO,EAAE/B,QAAQ,GAAGA,QAAQ,GAAG,CAAC;QAChCgC,eAAe,EAAE,IAAI;QACrBC,aAAa,EAAE;MACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAAClB,IAAI,EAAEQ,KAAK,EAAEzB,aAAa,EAAEc,KAAK,EAAEb,QAAQ,EAAEU,KAAK,CAAC,CAAC;EAExD,MAAM4B,aAAa,GAAGpD,KAAK,CAAC0C,WAAW,CAAC,MAAM;IAC5C;IACA,IAAIL,sBAAsB,CAACX,OAAO,EAAE;MAClCW,sBAAsB,CAACX,OAAO,CAAC2B,IAAI,CAAC,CAAC;IACvC;IAEApD,QAAQ,CAAC0C,MAAM,CAACb,IAAI,EAAE;MACpBc,QAAQ,EAAE,GAAG,GAAGN,KAAK;MACrBO,OAAO,EAAE,CAAC;MACVC,eAAe,EAAE,IAAI;MACrBC,aAAa,EAAE;IACjB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAClB,IAAI,EAAEQ,KAAK,CAAC,CAAC;EAEjBtC,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB,IAAIzB,OAAO,EAAE0B,cAAc,CAAC,CAAC,CAAC,KACzBW,aAAa,CAAC,CAAC;EACtB,CAAC,EAAE,CAACrC,OAAO,EAAE0B,cAAc,EAAEW,aAAa,CAAC,CAAC;EAE5CpD,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB,IAAItB,aAAa,IAAIA,aAAa,IAAI,CAAC,EAAE;MACvCS,KAAK,CAACuB,QAAQ,CAAChC,aAAa,CAAC;IAC/B;EACF,CAAC,EAAE,CAACA,aAAa,EAAES,KAAK,CAAC,CAAC;EAE1B3B,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB;IACA,IAAIzB,OAAO,IAAIoB,SAAS,KAAK,CAAC,EAAE;MAC9BM,cAAc,CAAC,CAAC;IAClB;EACF,CAAC,EAAE,CAACN,SAAS,EAAEM,cAAc,EAAE1B,OAAO,CAAC,CAAC;EAExC,MAAMuC,QAAQ,GAAIC,KAAwB,IAAK;IAC7CnB,YAAY,CAACJ,KAAK,CAAC;IACnBC,QAAQ,CAACsB,KAAK,CAACC,WAAW,CAACC,MAAM,CAACzB,KAAK,CAAC;EAC1C,CAAC;EAED,MAAM0B,SAAS,GAAG9C,KAAK,MAAAW,aAAA,GAAIP,KAAK,CAAC2C,MAAM,cAAApC,aAAA,uBAAZA,aAAA,CAAcqC,OAAO;EAChD,MAAMC,cAAc,GAAG7C,KAAK,CAAC8C,IAAI,GAC7B9C,KAAK,CAAC2C,MAAM,CAACI,cAAc,GAC3BzD,QAAQ,CAACoD,SAAS,CAAC,CAACM,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAElD,oBACElE,KAAA,CAAAmE,aAAA,CAAC9D,IAAI,EAAA+D,QAAA;IACHd,QAAQ,EAAEA;EAAS,GACfhC,IAAI;IACR+C,UAAU;IACVC,iBAAiB,EAAC,aAAa;IAC/BC,kBAAkB,EAAE;MAAEC,IAAI,EAAEzD;IAAQ,CAAE;IACtC0D,kBAAkB,EAChB5D,aAAa,GACT,CAAC,CAAC,GACF;MAAE6D,GAAG,EAAE,CAAC;MAAEC,GAAG,EAAE,GAAG;MAAEC,GAAG,EAAEC,IAAI,CAACC,KAAK,CAAChE,QAAQ,GAAG,GAAG;IAAE,CACzD;IACDK,KAAK,EAAEK,KAAK,IAAIuD,MAAM,CAACC,YAAa;IACpC3D,MAAM,EAAEA;EAAO,iBAEfrB,KAAA,CAAAmE,aAAA,CAAClE,QAAQ,CAACI,IAAI;IACZc,KAAK,EAAE,CACL4D,MAAM,CAACE,SAAS,EAChB;MAAEC,eAAe,EAAErB,cAAc;MAAEsB,OAAO,EAAErD;IAAK,CAAC,EAClDX,KAAK;EACL,GAEDa,KAAK,gBACJhC,KAAA,CAAAmE,aAAA,CAAClE,QAAQ,CAACI,IAAI;IACZgB,MAAM,EAAE,GAAGA,MAAM,OAAQ;IACzBF,KAAK,EAAE,CACL4D,MAAM,CAACK,WAAW,EAClB;MACEpD,KAAK;MACLkD,eAAe,EAAExB,SAAS;MAC1B2B,SAAS,EAAE,CACT;QACEC,UAAU,EAAE3D,KAAK,CAAC4D,WAAW,CAC3B1E,aAAa,GACT;UACE2E,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAE,CACX,CAAC/E,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,GAAGsB,KAAK,EAC9B,CAACtB,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IACb,GAAG,GACHD,uBAAuB,GACvBuB,KAAK,EACP,CAACtB,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,GAAGsB,KAAK;QAElC,CAAC,GACD;UACEwD,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,CAAC/E,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,IAAI,GAAG,GAAGsB,KAAK,EAAE,CAAC;QACjD,CACN;MACF,CAAC,EACD;QACE;QACA0D,MAAM,EAAE/D,KAAK,CAAC4D,WAAW,CACvB1E,aAAa,GACT;UACE2E,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;UACvBC,WAAW,EAAE,CACX,MAAM,EACNhF,uBAAuB,EACvB,MAAM;QAEV,CAAC,GACD;UACE+E,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,MAAM,EAAE,CAAC;QACzB,CACN;MACF,CAAC;IAEL,CAAC,EACDrE,SAAS;EACT,CACH,CAAC,GACA,IACS,CACX,CAAC;AAEX,CAAC;AAED,MAAM2D,MAAM,GAAG3E,UAAU,CAACuF,MAAM,CAAC;EAC/BV,SAAS,EAAE;IACTW,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE;EACZ,CAAC;EACDb,YAAY,EAAE;IACZhD,KAAK,EAAE,MAAM;IACb4D,MAAM,EAAE;EACV,CAAC;EACDR,WAAW,EAAE;IACXU,IAAI,EAAE;EACR;AACF,CAAC,CAAC;AAEF,eAAenF,WAAW", "ignoreList": []}
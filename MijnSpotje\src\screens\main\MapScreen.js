import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Text,
  Alert,
  Dimensions,
  ScrollView,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import * as Location from 'expo-location';
import { useAuth } from '../../contexts/AuthContext';
import { spotService } from '../../services/spotService';
import { theme } from '../../config/theme';

const { width, height } = Dimensions.get('window');

const MapScreen = ({ navigation }) => {
  const [location, setLocation] = useState(null);
  const [spots, setSpots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedSpot, setSelectedSpot] = useState(null);
  const { user } = useAuth();

  useEffect(() => {
    getCurrentLocation();
    loadSpots();
  }, []);

  const getCurrentLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        // For Expo Go compatibility, we'll just use a default location
        setLocation({
          latitude: 52.3676,
          longitude: 4.9041,
          name: 'Amsterdam, Nederland'
        });
        return;
      }

      const currentLocation = await Location.getCurrentPositionAsync({});
      setLocation({
        latitude: currentLocation.coords.latitude,
        longitude: currentLocation.coords.longitude,
        name: 'Huidige locatie'
      });
    } catch (error) {
      console.error('Error getting location:', error);
      // Default to Amsterdam if location fails
      setLocation({
        latitude: 52.3676,
        longitude: 4.9041,
        name: 'Amsterdam, Nederland'
      });
    }
  };

  const loadSpots = async () => {
    try {
      // For demo purposes, we'll use mock data since Supabase isn't configured yet
      const mockSpots = [
        {
          id: 1,
          title: 'Vondelpark',
          description: 'Prachtig park in het centrum van Amsterdam',
          latitude: 52.3579,
          longitude: 4.8686,
          averageRating: 4.5,
          ratingCount: 23,
          users: { username: 'amsterdam_lover' },
          category: 'park'
        },
        {
          id: 2,
          title: 'Anne Frank Huis',
          description: 'Historisch museum en monument',
          latitude: 52.3752,
          longitude: 4.8840,
          averageRating: 4.8,
          ratingCount: 156,
          users: { username: 'history_buff' },
          category: 'museum'
        },
        {
          id: 3,
          title: 'Café de Reiger',
          description: 'Gezellig bruin café in de Jordaan',
          latitude: 52.3738,
          longitude: 4.8852,
          averageRating: 4.2,
          ratingCount: 45,
          users: { username: 'cafe_hopper' },
          category: 'café'
        }
      ];
      setSpots(mockSpots);
    } catch (error) {
      console.error('Error loading spots:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleSpotPress = (spot) => {
    setSelectedSpot(spot);
  };

  const handleSpotDetailPress = () => {
    if (selectedSpot) {
      navigation.navigate('SpotDetail', { spotId: selectedSpot.id });
      setSelectedSpot(null);
    }
  };

  const handleCreateSpot = () => {
    navigation.navigate('CreateSpot');
  };

  const getMarkerColor = (spot) => {
    if (spot.averageRating >= 4) return '#27AE60'; // Green for high rated
    if (spot.averageRating >= 3) return '#F39C12'; // Orange for medium rated
    if (spot.averageRating >= 2) return '#E74C3C'; // Red for low rated
    return '#95A5A6'; // Gray for no rating
  };

  if (loading) {
    return (
      <View style={styles.loadingContainer}>
        <Ionicons name="map-outline" size={60} color={theme.colors.primary} />
        <Text style={styles.loadingText}>Kaart laden...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Map Placeholder */}
      <View style={styles.mapPlaceholder}>
        <View style={styles.mapHeader}>
          <Ionicons name="map-outline" size={40} color={theme.colors.primary} />
          <Text style={styles.mapTitle}>Kaart Weergave</Text>
          <Text style={styles.mapSubtitle}>
            {location ? `📍 ${location.name}` : 'Locatie niet beschikbaar'}
          </Text>
        </View>

        <Text style={styles.mapNote}>
          🗺️ Interactieve kaart wordt binnenkort toegevoegd
        </Text>

        {/* Spots List in Map View */}
        <ScrollView style={styles.spotsInMap} showsVerticalScrollIndicator={false}>
          <Text style={styles.spotsHeader}>Spots in de buurt ({spots.length})</Text>
          {spots.map((spot) => (
            <TouchableOpacity
              key={spot.id}
              style={[styles.spotMarker, { borderLeftColor: getMarkerColor(spot) }]}
              onPress={() => handleSpotPress(spot)}
            >
              <View style={styles.spotMarkerContent}>
                <View style={styles.spotMarkerHeader}>
                  <Ionicons
                    name="location"
                    size={20}
                    color={getMarkerColor(spot)}
                  />
                  <Text style={styles.spotMarkerTitle}>{spot.title}</Text>
                  <View style={styles.spotMarkerRating}>
                    <Ionicons name="star" size={14} color="#F39C12" />
                    <Text style={styles.spotMarkerRatingText}>
                      {spot.averageRating.toFixed(1)}
                    </Text>
                  </View>
                </View>
                <Text style={styles.spotMarkerDescription} numberOfLines={1}>
                  {spot.description}
                </Text>
                <Text style={styles.spotMarkerLocation}>
                  📍 {spot.latitude.toFixed(4)}, {spot.longitude.toFixed(4)}
                </Text>
              </View>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      {/* Selected Spot Card */}
      {selectedSpot && (
        <View style={styles.spotCard}>
          <View style={styles.spotCardContent}>
            <Text style={styles.spotTitle}>{selectedSpot.title}</Text>
            <Text style={styles.spotDescription} numberOfLines={2}>
              {selectedSpot.description}
            </Text>
            <View style={styles.spotMeta}>
              <View style={styles.ratingContainer}>
                <Ionicons name="star" size={16} color="#F39C12" />
                <Text style={styles.ratingValue}>
                  {selectedSpot.averageRating > 0
                    ? selectedSpot.averageRating.toFixed(1)
                    : 'Geen rating'
                  }
                </Text>
                <Text style={styles.ratingCount}>
                  ({selectedSpot.ratingCount} reviews)
                </Text>
              </View>
              <Text style={styles.spotAuthor}>
                door {selectedSpot.users?.username || 'Onbekend'}
              </Text>
            </View>
          </View>
          <TouchableOpacity
            style={styles.spotCardButton}
            onPress={handleSpotDetailPress}
          >
            <Ionicons name="chevron-forward" size={20} color={theme.colors.primary} />
          </TouchableOpacity>
        </View>
      )}

      {/* Floating Action Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={handleCreateSpot}
      >
        <Ionicons name="add" size={28} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: theme.colors.background,
  },
  loadingText: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.md,
  },
  mapPlaceholder: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  mapHeader: {
    alignItems: 'center',
    padding: theme.spacing.xl,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  mapTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: theme.spacing.sm,
  },
  mapSubtitle: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginTop: theme.spacing.xs,
  },
  mapNote: {
    textAlign: 'center',
    fontSize: 14,
    color: theme.colors.textSecondary,
    padding: theme.spacing.md,
    fontStyle: 'italic',
  },
  spotsInMap: {
    flex: 1,
    padding: theme.spacing.md,
  },
  spotsHeader: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  spotMarker: {
    backgroundColor: '#fff',
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
    borderLeftWidth: 4,
    ...theme.shadows.small,
  },
  spotMarkerContent: {
    padding: theme.spacing.md,
  },
  spotMarkerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.xs,
  },
  spotMarkerTitle: {
    flex: 1,
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: theme.spacing.sm,
  },
  spotMarkerRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  spotMarkerRatingText: {
    fontSize: 12,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: theme.spacing.xs,
  },
  spotMarkerDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.xs,
  },
  spotMarkerLocation: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    fontFamily: 'monospace',
  },
  spotCard: {
    position: 'absolute',
    bottom: 100,
    left: theme.spacing.md,
    right: theme.spacing.md,
    backgroundColor: '#fff',
    borderRadius: theme.borderRadius.lg,
    padding: theme.spacing.md,
    flexDirection: 'row',
    alignItems: 'center',
    ...theme.shadows.large,
  },
  spotCardContent: {
    flex: 1,
  },
  spotTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.xs,
  },
  spotDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.sm,
  },
  spotMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingValue: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: theme.spacing.xs,
  },
  ratingCount: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  spotAuthor: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  spotCardButton: {
    padding: theme.spacing.sm,
  },
  fab: {
    position: 'absolute',
    bottom: theme.spacing.lg,
    right: theme.spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.large,
  },
});

export default MapScreen;

// MD3 types
export let MD3TypescaleKey = /*#__PURE__*/function (MD3TypescaleKey) {
  MD3TypescaleKey["displayLarge"] = "displayLarge";
  MD3TypescaleKey["displayMedium"] = "displayMedium";
  MD3TypescaleKey["displaySmall"] = "displaySmall";
  MD3TypescaleKey["headlineLarge"] = "headlineLarge";
  MD3TypescaleKey["headlineMedium"] = "headlineMedium";
  MD3TypescaleKey["headlineSmall"] = "headlineSmall";
  MD3TypescaleKey["titleLarge"] = "titleLarge";
  MD3TypescaleKey["titleMedium"] = "titleMedium";
  MD3TypescaleKey["titleSmall"] = "titleSmall";
  MD3TypescaleKey["labelLarge"] = "labelLarge";
  MD3TypescaleKey["labelMedium"] = "labelMedium";
  MD3TypescaleKey["labelSmall"] = "labelSmall";
  MD3TypescaleKey["bodyLarge"] = "bodyLarge";
  MD3TypescaleKey["bodyMedium"] = "bodyMedium";
  MD3TypescaleKey["bodySmall"] = "bodySmall";
  return MD3TypescaleKey;
}({});
export let ElevationLevels = /*#__PURE__*/function (ElevationLevels) {
  ElevationLevels[ElevationLevels["level0"] = 0] = "level0";
  ElevationLevels[ElevationLevels["level1"] = 1] = "level1";
  ElevationLevels[ElevationLevels["level2"] = 2] = "level2";
  ElevationLevels[ElevationLevels["level3"] = 3] = "level3";
  ElevationLevels[ElevationLevels["level4"] = 4] = "level4";
  ElevationLevels[ElevationLevels["level5"] = 5] = "level5";
  return ElevationLevels;
}({});
//# sourceMappingURL=types.js.map
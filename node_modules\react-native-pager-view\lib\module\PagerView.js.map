{"version": 3, "names": ["React", "Platform", "Keyboard", "I18nManager", "childrenWithOverriddenStyle", "PagerViewNativeComponent", "Commands", "PagerViewNativeCommands", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Component", "constructor", "args", "_defineProperty", "e", "props", "onPageScroll", "OS", "keyboardDismissMode", "dismiss", "onPageScrollStateChanged", "isScrolling", "nativeEvent", "pageScrollState", "onPageSelected", "selectedPage", "pager<PERSON>ie<PERSON>", "setPage", "setPageWithoutAnimation", "scrollEnabled", "setScrollEnabledImperatively", "deducedLayoutDirection", "layoutDirection", "isRTL", "render", "createElement", "_extends", "ref", "style", "_onPageScroll", "_onPageScrollStateChanged", "_onPageSelected", "onMoveShouldSetResponderCapture", "_onMoveShouldSetResponderCapture", "children"], "sources": ["PagerView.tsx"], "sourcesContent": ["import React from 'react';\nimport { Platform, Keyboard } from 'react-native';\nimport { I18nManager } from 'react-native';\nimport type * as ReactNative from 'react-native';\n\nimport {\n  childrenWithOverriddenStyle,\n} from './utils';\n\nimport PagerViewNativeComponent, {\n  Commands as PagerViewNativeCommands,\n  OnPageScrollEventData,\n  OnPageScrollStateChangedEventData,\n  OnPageSelectedEventData,\n  NativeProps,\n} from './PagerViewNativeComponent';\n\n\n/**\n * Container that allows to flip left and right between child views. Each\n * child view of the `PagerView` will be treated as a separate page\n * and will be stretched to fill the `PagerView`.\n *\n * It is important all children are `<View>`s and not composite components.\n * You can set style properties like `padding` or `backgroundColor` for each\n * child. It is also important that each child have a `key` prop.\n *\n * Example:\n *\n * ```\n * render: function() {\n *   return (\n *     <PagerView\n *       style={styles.PagerView}\n *       initialPage={0}>\n *       <View style={styles.pageStyle} key=\"1\">\n *         <Text>First page</Text>\n *       </View>\n *       <View style={styles.pageStyle} key=\"2\">\n *         <Text>Second page</Text>\n *       </View>\n *     </PagerView>\n *   );\n * }\n *\n * ...\n *\n * var styles = {\n *   ...\n *   PagerView: {\n *     flex: 1\n *   },\n *   pageStyle: {\n *     alignItems: 'center',\n *     padding: 20,\n *   }\n * }\n * ```\n */\n\nexport class PagerView extends React.Component<NativeProps> {\n  private isScrolling = false;\n  pagerView: React.ElementRef<typeof PagerViewNativeComponent> | null = null;\n\n\n  private get deducedLayoutDirection() {\n    if (\n      !this.props.layoutDirection ||\n      //@ts-ignore fix it\n      this.props.layoutDirection === 'locale'\n    ) {\n      return I18nManager.isRTL ? 'rtl' : 'ltr';\n    } else {\n      return this.props.layoutDirection;\n    }\n  }\n\n  private _onPageScroll = (\n    e: ReactNative.NativeSyntheticEvent<OnPageScrollEventData>\n  ) => {\n    if (this.props.onPageScroll) {\n      this.props.onPageScroll(e);\n    }\n\n    // Not implemented on iOS yet\n    if (Platform.OS === 'android') {\n      if (this.props.keyboardDismissMode === 'on-drag') {\n        Keyboard.dismiss();\n      }\n    }\n  };\n\n  private _onPageScrollStateChanged = (\n    e: ReactNative.NativeSyntheticEvent<OnPageScrollStateChangedEventData>\n  ) => {\n    if (this.props.onPageScrollStateChanged) {\n      this.props.onPageScrollStateChanged(e);\n    }\n    this.isScrolling = e.nativeEvent.pageScrollState === 'dragging';\n  };\n\n  private _onPageSelected = (\n    e: ReactNative.NativeSyntheticEvent<OnPageSelectedEventData>\n  ) => {\n    if (this.props.onPageSelected) {\n      this.props.onPageSelected(e);\n    }\n  };\n\n  private _onMoveShouldSetResponderCapture = () => {\n    return this.isScrolling;\n  };\n\n  /**\n   * A helper function to scroll to a specific page in the PagerView.\n   * The transition between pages will be animated.\n   */\n  public setPage = (selectedPage: number) => {\n    if (this.pagerView) {\n      PagerViewNativeCommands.setPage(this.pagerView, selectedPage);\n    }\n  };\n\n  /**\n   * A helper function to scroll to a specific page in the PagerView.\n   * The transition between pages will *not* be animated.\n   */\n  public setPageWithoutAnimation = (selectedPage: number) => {\n    if (this.pagerView) {\n      PagerViewNativeCommands.setPageWithoutAnimation(\n        this.pagerView,\n        selectedPage\n      );\n    }\n  };\n\n  /**\n   * A helper function to enable/disable scroll imperatively\n   * The recommended way is using the scrollEnabled prop, however, there might be a case where a\n   * imperative solution is more useful (e.g. for not blocking an animation)\n   */\n  public setScrollEnabled = (scrollEnabled: boolean) => {\n    if (this.pagerView) {\n      PagerViewNativeCommands.setScrollEnabledImperatively(\n        this.pagerView,\n        scrollEnabled\n      );\n    }\n  };\n\n  render() {\n      return (\n        <PagerViewNativeComponent\n          {...this.props}\n          ref={(ref) => {\n            this.pagerView = ref;\n          }}\n          style={this.props.style}\n          layoutDirection={this.deducedLayoutDirection}\n          onPageScroll={this._onPageScroll}\n          onPageScrollStateChanged={this._onPageScrollStateChanged}\n          onPageSelected={this._onPageSelected}\n          onMoveShouldSetResponderCapture={\n            this._onMoveShouldSetResponderCapture\n          }\n          children={childrenWithOverriddenStyle(this.props.children)}\n        />\n      );\n  }\n}\n"], "mappings": ";;;;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAEC,QAAQ,QAAQ,cAAc;AACjD,SAASC,WAAW,QAAQ,cAAc;AAG1C,SACEC,2BAA2B,QACtB,SAAS;AAEhB,OAAOC,wBAAwB,IAC7BC,QAAQ,IAAIC,uBAAuB,QAK9B,4BAA4B;;AAGnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,OAAO,MAAMC,SAAS,SAASR,KAAK,CAACS,SAAS,CAAc;EAAAC,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAC,eAAA,sBACpC,KAAK;IAAAA,eAAA,oBAC2C,IAAI;IAAAA,eAAA,wBAgBxEC,CAA0D,IACvD;MACH,IAAI,IAAI,CAACC,KAAK,CAACC,YAAY,EAAE;QAC3B,IAAI,CAACD,KAAK,CAACC,YAAY,CAACF,CAAC,CAAC;MAC5B;;MAEA;MACA,IAAIZ,QAAQ,CAACe,EAAE,KAAK,SAAS,EAAE;QAC7B,IAAI,IAAI,CAACF,KAAK,CAACG,mBAAmB,KAAK,SAAS,EAAE;UAChDf,QAAQ,CAACgB,OAAO,CAAC,CAAC;QACpB;MACF;IACF,CAAC;IAAAN,eAAA,oCAGCC,CAAsE,IACnE;MACH,IAAI,IAAI,CAACC,KAAK,CAACK,wBAAwB,EAAE;QACvC,IAAI,CAACL,KAAK,CAACK,wBAAwB,CAACN,CAAC,CAAC;MACxC;MACA,IAAI,CAACO,WAAW,GAAGP,CAAC,CAACQ,WAAW,CAACC,eAAe,KAAK,UAAU;IACjE,CAAC;IAAAV,eAAA,0BAGCC,CAA4D,IACzD;MACH,IAAI,IAAI,CAACC,KAAK,CAACS,cAAc,EAAE;QAC7B,IAAI,CAACT,KAAK,CAACS,cAAc,CAACV,CAAC,CAAC;MAC9B;IACF,CAAC;IAAAD,eAAA,2CAE0C,MAAM;MAC/C,OAAO,IAAI,CAACQ,WAAW;IACzB,CAAC;IAED;AACF;AACA;AACA;IAHER,eAAA,kBAIkBY,YAAoB,IAAK;MACzC,IAAI,IAAI,CAACC,SAAS,EAAE;QAClBlB,uBAAuB,CAACmB,OAAO,CAAC,IAAI,CAACD,SAAS,EAAED,YAAY,CAAC;MAC/D;IACF,CAAC;IAED;AACF;AACA;AACA;IAHEZ,eAAA,kCAIkCY,YAAoB,IAAK;MACzD,IAAI,IAAI,CAACC,SAAS,EAAE;QAClBlB,uBAAuB,CAACoB,uBAAuB,CAC7C,IAAI,CAACF,SAAS,EACdD,YACF,CAAC;MACH;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;IAJEZ,eAAA,2BAK2BgB,aAAsB,IAAK;MACpD,IAAI,IAAI,CAACH,SAAS,EAAE;QAClBlB,uBAAuB,CAACsB,4BAA4B,CAClD,IAAI,CAACJ,SAAS,EACdG,aACF,CAAC;MACH;IACF,CAAC;EAAA;EAnFD,IAAYE,sBAAsBA,CAAA,EAAG;IACnC,IACE,CAAC,IAAI,CAAChB,KAAK,CAACiB,eAAe;IAC3B;IACA,IAAI,CAACjB,KAAK,CAACiB,eAAe,KAAK,QAAQ,EACvC;MACA,OAAO5B,WAAW,CAAC6B,KAAK,GAAG,KAAK,GAAG,KAAK;IAC1C,CAAC,MAAM;MACL,OAAO,IAAI,CAAClB,KAAK,CAACiB,eAAe;IACnC;EACF;EA2EAE,MAAMA,CAAA,EAAG;IACL,oBACEjC,KAAA,CAAAkC,aAAA,CAAC7B,wBAAwB,EAAA8B,QAAA,KACnB,IAAI,CAACrB,KAAK;MACdsB,GAAG,EAAGA,GAAG,IAAK;QACZ,IAAI,CAACX,SAAS,GAAGW,GAAG;MACtB,CAAE;MACFC,KAAK,EAAE,IAAI,CAACvB,KAAK,CAACuB,KAAM;MACxBN,eAAe,EAAE,IAAI,CAACD,sBAAuB;MAC7Cf,YAAY,EAAE,IAAI,CAACuB,aAAc;MACjCnB,wBAAwB,EAAE,IAAI,CAACoB,yBAA0B;MACzDhB,cAAc,EAAE,IAAI,CAACiB,eAAgB;MACrCC,+BAA+B,EAC7B,IAAI,CAACC,gCACN;MACDC,QAAQ,EAAEvC,2BAA2B,CAAC,IAAI,CAACU,KAAK,CAAC6B,QAAQ;IAAE,EAC5D,CAAC;EAER;AACF", "ignoreList": []}
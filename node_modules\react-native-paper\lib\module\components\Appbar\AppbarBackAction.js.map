{"version": 3, "names": ["React", "AppbarAction", "AppbarBackIcon", "forwardRef", "AppbarBackAction", "accessibilityLabel", "rest", "ref", "createElement", "_extends", "icon", "isLeading", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/Appbar/AppbarBackAction.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAU9B,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,UAAU,QAAQ,wBAAwB;AA8BnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAGD,UAAU,CACjC,CAAC;EAAEE,kBAAkB,GAAG,MAAM;EAAE,GAAGC;AAAY,CAAC,EAAEC,GAAG,kBACnDP,KAAA,CAAAQ,aAAA,CAACP,YAAY,EAAAQ,QAAA;EACXJ,kBAAkB,EAAEA;AAAmB,GACnCC,IAAI;EACRI,IAAI,EAAER,cAAe;EACrBS,SAAS;EACTJ,GAAG,EAAEA;AAAI,EACV,CAEL,CAAC;AAEDH,gBAAgB,CAACQ,WAAW,GAAG,mBAAmB;AAElD,eAAeR,gBAAgB;;AAE/B;AACA,SAASA,gBAAgB", "ignoreList": []}
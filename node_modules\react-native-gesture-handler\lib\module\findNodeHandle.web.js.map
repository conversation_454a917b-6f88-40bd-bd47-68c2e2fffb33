{"version": 3, "sources": ["findNodeHandle.web.ts"], "names": ["FlatList", "isRNSVGElement", "findNodeHandle", "viewRef", "_listRef", "_scrollRef", "<PERSON><PERSON><PERSON><PERSON>", "viewTag", "undefined", "Element", "style", "display", "elementRef", "current", "element"], "mappings": "AAAA,SAASA,QAAT,QAAyB,cAAzB;AAEA,SAASC,cAAT,QAA+B,aAA/B;AAEA,eAAe,SAASC,cAAT,CACbC,OADa,EAEsB;AACnC;AACA,MAAIA,OAAO,YAAYH,QAAvB,EAAiC;AAC/B;AACA,WAAOG,OAAO,CAACC,QAAR,CAAiBC,UAAjB,CAA4BC,UAAnC;AACD,GALkC,CAMnC;AACA;AACA;;;AACA,MAAI,CAACH,OAAD,aAACA,OAAD,uBAACA,OAAD,CAAgCI,OAAhC,MAA4CC,SAAhD,EAA2D;AACzD,WAAON,cAAc,CAAEC,OAAD,CAA+BI,OAAhC,CAArB;AACD;;AAED,MAAIJ,OAAO,YAAYM,OAAvB,EAAgC;AAC9B,QAAIN,OAAO,CAACO,KAAR,CAAcC,OAAd,KAA0B,UAA9B,EAA0C;AACxC,aAAOT,cAAc,CAACC,OAAO,CAACG,UAAT,CAArB;AACD;;AAED,WAAOH,OAAP;AACD;;AAED,MAAIF,cAAc,CAACE,OAAD,CAAlB,EAA6B;AAC3B,WAAQA,OAAD,CAAoBS,UAApB,CAA+BC,OAAtC;AACD,GAvBkC,CAyBnC;AACA;;;AACA,MAAIC,OAAO,GAAIX,OAAJ,aAAIA,OAAJ,uBAAIA,OAAD,CAAgCU,OAA9C;;AAEA,SAAOC,OAAO,IAAIA,OAAO,CAACJ,KAAR,CAAcC,OAAd,KAA0B,UAA5C,EAAwD;AACtDG,IAAAA,OAAO,GAAGA,OAAO,CAACR,UAAlB;AACD;;AAED,SAAOQ,OAAP;AACD", "sourcesContent": ["import { FlatList } from 'react-native';\nimport type { GestureHandlerRef, SVGRef } from './web/interfaces';\nimport { isRNSVGElement } from './web/utils';\n\nexport default function findNodeHandle(\n  viewRef: GestureHandlerRef | SVGRef | HTMLElement | SVGElement\n): HTMLElement | SVGElement | number {\n  // TODO: Remove this once we remove old API.\n  if (viewRef instanceof FlatList) {\n    // @ts-ignore This is the only way to get the scroll ref from FlatList.\n    return viewRef._listRef._scrollRef.firstChild;\n  }\n  // Old API assumes that child handler is HTMLElement.\n  // However, if we nest handlers, we will get ref to another handler.\n  // In that case, we want to recursively call findN<PERSON>Handle with new handler viewTag (which can also be ref to another handler).\n  if ((viewRef as GestureHandlerRef)?.viewTag !== undefined) {\n    return findNodeHandle((viewRef as GestureHandlerRef).viewTag);\n  }\n\n  if (viewRef instanceof Element) {\n    if (viewRef.style.display === 'contents') {\n      return findNodeHandle(viewRef.firstChild as HTMLElement);\n    }\n\n    return viewRef;\n  }\n\n  if (isRNSVGElement(viewRef)) {\n    return (viewRef as SVGRef).elementRef.current;\n  }\n\n  // In new API, we receive ref object which `current` field points to  wrapper `div` with `display: contents;`.\n  // We want to return the first descendant (in DFS order) that doesn't have this property.\n  let element = (viewRef as GestureHandlerRef)?.current;\n\n  while (element && element.style.display === 'contents') {\n    element = element.firstChild as HTMLElement;\n  }\n\n  return element;\n}\n"]}
{"version": 3, "sources": ["NodeManager.ts"], "names": ["gestures", "<PERSON><PERSON><PERSON><PERSON>", "tag", "Error", "createGestureHandler", "handlerTag", "handler", "dropGestureHandler", "destroy", "getNodes"], "mappings": ";;;;;;;;;;AAAO,MAAMA,QAA6B,GAAG,EAAtC;;;AAEA,SAASC,UAAT,CAAoBC,GAApB,EAAiC;AACtC,MAAIA,GAAG,IAAIF,QAAX,EAAqB;AACnB,WAAOA,QAAQ,CAACE,GAAD,CAAf;AACD;;AAED,QAAM,IAAIC,KAAJ,CAAW,sBAAqBD,GAAI,EAApC,CAAN;AACD;;AAEM,SAASE,oBAAT,CAA8BC,UAA9B,EAAkDC,OAAlD,EAAgE;AACrE,MAAID,UAAU,IAAIL,QAAlB,EAA4B;AAC1B,UAAM,IAAIG,KAAJ,CAAW,oBAAmBE,UAAW,iBAAzC,CAAN;AACD;;AACDL,EAAAA,QAAQ,CAACK,UAAD,CAAR,GAAuBC,OAAvB,CAJqE,CAKrE;;AACAN,EAAAA,QAAQ,CAACK,UAAD,CAAR,CAAqBA,UAArB,GAAkCA,UAAlC;AACD;;AAEM,SAASE,kBAAT,CAA4BF,UAA5B,EAAgD;AACrD;AACA;AACA,MAAI,EAAEA,UAAU,IAAIL,QAAhB,CAAJ,EAA+B;AAC7B;AACD;;AACDC,EAAAA,UAAU,CAACI,UAAD,CAAV,CAAuBG,OAAvB,GANqD,CAOrD;;AACA,SAAOR,QAAQ,CAACK,UAAD,CAAf;AACD;;AAEM,SAASI,QAAT,GAAoB;AACzB,SAAO,EAAE,GAAGT;AAAL,GAAP;AACD", "sourcesContent": ["export const gestures: Record<number, any> = {};\n\nexport function getHandler(tag: number) {\n  if (tag in gestures) {\n    return gestures[tag];\n  }\n\n  throw new Error(`No handler for tag ${tag}`);\n}\n\nexport function createGestureHandler(handlerTag: number, handler: any) {\n  if (handlerTag in gestures) {\n    throw new Error(`Hand<PERSON> with tag ${handlerTag} already exists`);\n  }\n  gestures[handlerTag] = handler;\n  // @ts-ignore no types for web handlers yet\n  gestures[handlerTag].handlerTag = handlerTag;\n}\n\nexport function dropGestureHandler(handlerTag: number) {\n  // Since React 18, there are cases where componentWillUnmount gets called twice in a row\n  // so skip this if the tag was already removed.\n  if (!(handlerTag in gestures)) {\n    return;\n  }\n  getHandler(handlerTag).destroy();\n  // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n  delete gestures[handlerTag];\n}\n\nexport function getNodes() {\n  return { ...gestures };\n}\n"]}
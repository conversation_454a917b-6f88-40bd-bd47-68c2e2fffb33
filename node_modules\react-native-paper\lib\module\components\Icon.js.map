{"version": 3, "names": ["React", "I18nManager", "Image", "Platform", "accessibilityProps", "Consumer", "SettingsConsumer", "useInternalTheme", "isImageSource", "source", "Object", "prototype", "hasOwnProperty", "call", "uri", "OS", "startsWith", "test", "getIconId", "isValidIcon", "isEqualIcon", "a", "b", "Icon", "color", "size", "theme", "themeOverrides", "testID", "rest", "direction", "getConstants", "isRTL", "s", "iconColor", "isV3", "colors", "onSurface", "text", "createElement", "_extends", "style", "transform", "scaleX", "width", "height", "tintColor", "resizeMode", "accessibilityIgnoresInvertColors", "icon", "name"], "sourceRoot": "../../../src", "sources": ["components/Icon.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,WAAW,EACXC,KAAK,EAELC,QAAQ,QACH,cAAc;AAErB,SAASC,kBAAkB,QAAQ,yBAAyB;AAC5D,SAASC,QAAQ,IAAIC,gBAAgB,QAAQ,kBAAkB;AAC/D,SAASC,gBAAgB,QAAQ,iBAAiB;AAkBlD,MAAMC,aAAa,GAAIC,MAAW;AAChC;AACC,OAAOA,MAAM,KAAK,QAAQ,IACzBA,MAAM,KAAK,IAAI,IACfC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAE,KAAK,CAAC,IACnD,OAAOA,MAAM,CAACK,GAAG,KAAK,QAAQ;AAChC;AACA,OAAOL,MAAM,KAAK,QAAQ;AAC1B;AACCN,QAAQ,CAACY,EAAE,KAAK,KAAK,IACpB,OAAON,MAAM,KAAK,QAAQ,KACzBA,MAAM,CAACO,UAAU,CAAC,YAAY,CAAC,IAC9B,+BAA+B,CAACC,IAAI,CAACR,MAAM,CAAC,CAAE;AAEpD,MAAMS,SAAS,GAAIT,MAAW,IAAK;EACjC,IACE,OAAOA,MAAM,KAAK,QAAQ,IAC1BA,MAAM,KAAK,IAAI,IACfC,MAAM,CAACC,SAAS,CAACC,cAAc,CAACC,IAAI,CAACJ,MAAM,EAAE,KAAK,CAAC,IACnD,OAAOA,MAAM,CAACK,GAAG,KAAK,QAAQ,EAC9B;IACA,OAAOL,MAAM,CAACK,GAAG;EACnB;EAEA,OAAOL,MAAM;AACf,CAAC;AAED,OAAO,MAAMU,WAAW,GAAIV,MAAW,IACrC,OAAOA,MAAM,KAAK,QAAQ,IAC1B,OAAOA,MAAM,KAAK,UAAU,IAC5BD,aAAa,CAACC,MAAM,CAAC;AAEvB,OAAO,MAAMW,WAAW,GAAGA,CAACC,CAAM,EAAEC,CAAM,KACxCD,CAAC,KAAKC,CAAC,IAAIJ,SAAS,CAACG,CAAC,CAAC,KAAKH,SAAS,CAACI,CAAC,CAAC;AAqB1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,IAAI,GAAGA,CAAC;EACZd,MAAM;EACNe,KAAK;EACLC,IAAI;EACJC,KAAK,EAAEC,cAAc;EACrBC,MAAM;EACN,GAAGC;AACE,CAAC,KAAK;EACX,MAAMH,KAAK,GAAGnB,gBAAgB,CAACoB,cAAc,CAAC;EAC9C,MAAMG,SAAS,GACb,OAAOrB,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACqB,SAAS,IAAIrB,MAAM,CAACA,MAAM,GAC3DA,MAAM,CAACqB,SAAS,KAAK,MAAM,GACzB7B,WAAW,CAAC8B,YAAY,CAAC,CAAC,CAACC,KAAK,GAC9B,KAAK,GACL,KAAK,GACPvB,MAAM,CAACqB,SAAS,GAClB,IAAI;EAEV,MAAMG,CAAC,GACL,OAAOxB,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACqB,SAAS,IAAIrB,MAAM,CAACA,MAAM,GAC3DA,MAAM,CAACA,MAAM,GACbA,MAAM;EACZ,MAAMyB,SAAS,GACbV,KAAK,KAAKE,KAAK,CAACS,IAAI,GAAGT,KAAK,CAACU,MAAM,CAACC,SAAS,GAAGX,KAAK,CAACU,MAAM,CAACE,IAAI,CAAC;EAEpE,IAAI9B,aAAa,CAACyB,CAAC,CAAC,EAAE;IACpB,oBACEjC,KAAA,CAAAuC,aAAA,CAACrC,KAAK,EAAAsC,QAAA,KACAX,IAAI;MACRD,MAAM,EAAEA,MAAO;MACfnB,MAAM,EAAEwB,CAAE;MACVQ,KAAK,EAAE,CACL;QACEC,SAAS,EAAE,CAAC;UAAEC,MAAM,EAAEb,SAAS,KAAK,KAAK,GAAG,CAAC,CAAC,GAAG;QAAE,CAAC;MACtD,CAAC,EACD;QACEc,KAAK,EAAEnB,IAAI;QACXoB,MAAM,EAAEpB,IAAI;QACZqB,SAAS,EAAEtB,KAAK;QAChBuB,UAAU,EAAE;MACd,CAAC;IACD,GACE3C,kBAAkB;MACtB4C,gCAAgC;IAAA,EACjC,CAAC;EAEN,CAAC,MAAM,IAAI,OAAOf,CAAC,KAAK,QAAQ,EAAE;IAChC,oBACEjC,KAAA,CAAAuC,aAAA,CAACjC,gBAAgB,QACd,CAAC;MAAE2C;IAAK,CAAC,KAAK;MACb,OAAOA,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAG;QACZC,IAAI,EAAEjB,CAAC;QACPT,KAAK,EAAEU,SAAS;QAChBT,IAAI;QACJK,SAAS;QACTF;MACF,CAAC,CAAC;IACJ,CACgB,CAAC;EAEvB,CAAC,MAAM,IAAI,OAAOK,CAAC,KAAK,UAAU,EAAE;IAClC,OAAOA,CAAC,CAAC;MAAET,KAAK,EAAEU,SAAS;MAAET,IAAI;MAAEK,SAAS;MAAEF;IAAO,CAAC,CAAC;EACzD;EAEA,OAAO,IAAI;AACb,CAAC;AAED,eAAeL,IAAI", "ignoreList": []}
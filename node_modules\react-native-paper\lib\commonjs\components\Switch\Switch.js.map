{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_utils", "_theming", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "version", "NativeModules", "PlatformConstants", "reactNativeVersion", "undefined", "Switch", "value", "disabled", "onValueChange", "color", "theme", "themeOverrides", "rest", "useInternalTheme", "checkedColor", "onTintColor", "thumbTintColor", "getSwitchColor", "props", "major", "minor", "Platform", "OS", "activeTrackColor", "thumbColor", "activeThumbColor", "trackColor", "true", "false", "createElement", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/Switch/Switch.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAQA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAAsD,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAkB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAjB,CAAA,aAAAJ,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAC,CAAA,GAAAqB,SAAA,CAAAtB,CAAA,YAAAG,CAAA,IAAAF,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAe,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAGtD,MAAMG,OAAO,GAAGC,0BAAa,CAACC,iBAAiB,GAC3CD,0BAAa,CAACC,iBAAiB,CAACC,kBAAkB,GAClDC,SAAS;AA0Bb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAGA,CAAC;EACdC,KAAK;EACLC,QAAQ;EACRC,aAAa;EACbC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EACX,MAAMF,KAAK,GAAG,IAAAG,yBAAgB,EAACF,cAAc,CAAC;EAC9C,MAAM;IAAEG,YAAY;IAAEC,WAAW;IAAEC;EAAe,CAAC,GAAG,IAAAC,qBAAc,EAAC;IACnEP,KAAK;IACLH,QAAQ;IACRD,KAAK;IACLG;EACF,CAAC,CAAC;EAEF,MAAMS,KAAK,GACTlB,OAAO,IAAIA,OAAO,CAACmB,KAAK,KAAK,CAAC,IAAInB,OAAO,CAACoB,KAAK,IAAI,EAAE,GACjD;IACEL,WAAW;IACXC;EACF,CAAC,GACDK,qBAAQ,CAACC,EAAE,KAAK,KAAK,GACrB;IACEC,gBAAgB,EAAER,WAAW;IAC7BS,UAAU,EAAER,cAAc;IAC1BS,gBAAgB,EAAEX;EACpB,CAAC,GACD;IACEU,UAAU,EAAER,cAAc;IAC1BU,UAAU,EAAE;MACVC,IAAI,EAAEZ,WAAW;MACjBa,KAAK,EAAEb;IACT;EACF,CAAC;EAEP,oBACE9C,KAAA,CAAA4D,aAAA,CAACzD,YAAA,CAAAiC,MAAY,EAAAX,QAAA;IACXY,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA,QAAS;IACnBC,aAAa,EAAED,QAAQ,GAAGH,SAAS,GAAGI;EAAc,GAChDU,KAAK,EACLN,IAAI,CACT,CAAC;AAEN,CAAC;AAAC,IAAAkB,QAAA,GAAAC,OAAA,CAAA9C,OAAA,GAEaoB,MAAM", "ignoreList": []}
{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "defaultSize", "AvatarImage", "size", "source", "style", "onError", "onLayout", "onLoad", "onLoadEnd", "onLoadStart", "onProgress", "theme", "themeOverrides", "testID", "rest", "colors", "useInternalTheme", "backgroundColor", "primary", "StyleSheet", "flatten", "createElement", "View", "width", "height", "borderRadius", "Image", "accessibilityIgnoresInvertColors", "displayName", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/Avatar/AvatarImage.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,QAAA,GAAAF,OAAA;AAAsD,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAkB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAjB,CAAA,aAAAJ,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAC,CAAA,GAAAqB,SAAA,CAAAtB,CAAA,YAAAG,CAAA,IAAAF,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAe,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAGtD,MAAMG,WAAW,GAAG,EAAE;AAgDtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAC;EACnBC,IAAI,GAAGF,WAAW;EAClBG,MAAM;EACNC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC,MAAM;EACNC,SAAS;EACTC,WAAW;EACXC,UAAU;EACVC,KAAK,EAAEC,cAAc;EACrBC,MAAM;EACN,GAAGC;AACE,CAAC,KAAK;EACX,MAAM;IAAEC;EAAO,CAAC,GAAG,IAAAC,yBAAgB,EAACJ,cAAc,CAAC;EACnD,MAAM;IAAEK,eAAe,GAAGF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEG;EAAQ,CAAC,GAAGC,uBAAU,CAACC,OAAO,CAAChB,KAAK,CAAC,IAAI,CAAC,CAAC;EAE7E,oBACElC,KAAA,CAAAmD,aAAA,CAAChD,YAAA,CAAAiD,IAAI,EAAA5B,QAAA;IACHU,KAAK,EAAE,CACL;MACEmB,KAAK,EAAErB,IAAI;MACXsB,MAAM,EAAEtB,IAAI;MACZuB,YAAY,EAAEvB,IAAI,GAAG,CAAC;MACtBe;IACF,CAAC,EACDb,KAAK;EACL,GACEU,IAAI,GAEP,OAAOX,MAAM,KAAK,UAAU,IAAIA,MAAM,CAAC;IAAED;EAAK,CAAC,CAAC,EAChD,OAAOC,MAAM,KAAK,UAAU,iBAC3BjC,KAAA,CAAAmD,aAAA,CAAChD,YAAA,CAAAqD,KAAK;IACJb,MAAM,EAAEA,MAAO;IACfV,MAAM,EAAEA,MAAO;IACfC,KAAK,EAAE;MAAEmB,KAAK,EAAErB,IAAI;MAAEsB,MAAM,EAAEtB,IAAI;MAAEuB,YAAY,EAAEvB,IAAI,GAAG;IAAE,CAAE;IAC7DG,OAAO,EAAEA,OAAQ;IACjBC,QAAQ,EAAEA,QAAS;IACnBC,MAAM,EAAEA,MAAO;IACfC,SAAS,EAAEA,SAAU;IACrBC,WAAW,EAAEA,WAAY;IACzBC,UAAU,EAAEA,UAAW;IACvBiB,gCAAgC;EAAA,CACjC,CAEC,CAAC;AAEX,CAAC;AAED1B,WAAW,CAAC2B,WAAW,GAAG,cAAc;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA7C,OAAA,GAE1BgB,WAAW", "ignoreList": []}
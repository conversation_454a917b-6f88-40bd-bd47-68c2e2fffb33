/// <reference types="react" />
declare const FAB: import("../../utils/forwardRef").ForwardRefComponent<import("react-native").View, import("./FAB").Props> & {
    Group: {
        ({ actions, icon, open, onPress, onLongPress, toggleStackOnLongPress, accessibilityLabel, theme: themeOverrides, style, fabStyle, visible, label, testID, onStateChange, color: colorProp, delayLongPress, variant, enableLongPressWhenStackOpened, backdropColor: customBackdropColor, rippleColor, }: import("./FABGroup").Props): import("react").JSX.Element;
        displayName: string;
    };
};
export default FAB;
//# sourceMappingURL=index.d.ts.map
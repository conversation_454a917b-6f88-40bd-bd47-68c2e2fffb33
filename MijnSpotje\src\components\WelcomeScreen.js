import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Dimensions,
  ScrollView,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { LinearGradient } from 'expo-linear-gradient';
import { useTranslation } from '../i18n/useTranslation';
import { AnimatedLogo } from './Logo';

const { width, height } = Dimensions.get('window');

const WelcomeScreen = ({ onStart, isDark }) => {
  const { t, currentLanguage, changeLanguage, getAvailableLanguages } = useTranslation();
  const [showLanguageSelector, setShowLanguageSelector] = useState(false);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const logoScale = useRef(new Animated.Value(0.8)).current;
  const featureAnims = useRef([
    new Animated.Value(0),
    new Animated.Value(0),
    new Animated.Value(0),
  ]).current;

  useEffect(() => {
    // Start animations sequence
    const animationSequence = Animated.sequence([
      // Logo animation
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.spring(logoScale, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        }),
      ]),
      // Content slide up
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
      // Features stagger animation
      Animated.stagger(200, featureAnims.map(anim =>
        Animated.spring(anim, {
          toValue: 1,
          tension: 50,
          friction: 7,
          useNativeDriver: true,
        })
      )),
    ]);

    animationSequence.start();
  }, []);

  const handleLanguageSelect = () => {
    const languages = getAvailableLanguages();
    Alert.alert(
      t('welcome.selectLanguage'),
      t('welcome.selectLanguageMessage'),
      [
        ...languages.map(lang => ({
          text: lang.name,
          onPress: () => changeLanguage(lang.code)
        })),
        { text: t('common.cancel'), style: 'cancel' }
      ]
    );
  };

  const features = [
    {
      icon: '🗺️',
      title: t('welcome.features.discover.title'),
      description: t('welcome.features.discover.description'),
    },
    {
      icon: '📍',
      title: t('welcome.features.share.title'),
      description: t('welcome.features.share.description'),
    },
    {
      icon: '👥',
      title: t('welcome.features.connect.title'),
      description: t('welcome.features.connect.description'),
    },
  ];

  return (
    <View style={styles.container}>
      <StatusBar style={isDark ? "light" : "dark"} />
      
      {/* Background Gradient */}
      <LinearGradient
        colors={isDark 
          ? ['#000000', '#1a1a1a', '#2a2a2a']
          : ['#ffffff', '#f8f9fa', '#e9ecef']
        }
        style={styles.gradient}
      />

      {/* Floating Background Elements */}
      <View style={styles.backgroundElements}>
        <Animated.View style={[
          styles.floatingElement,
          styles.element1,
          { opacity: fadeAnim }
        ]}>
          <Text style={styles.floatingIcon}>📍</Text>
        </Animated.View>
        <Animated.View style={[
          styles.floatingElement,
          styles.element2,
          { opacity: fadeAnim }
        ]}>
          <Text style={styles.floatingIcon}>🗺️</Text>
        </Animated.View>
        <Animated.View style={[
          styles.floatingElement,
          styles.element3,
          { opacity: fadeAnim }
        ]}>
          <Text style={styles.floatingIcon}>⭐</Text>
        </Animated.View>
      </View>

      <ScrollView 
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Language Selector */}
        <TouchableOpacity 
          style={styles.languageButton}
          onPress={handleLanguageSelect}
        >
          <Text style={[styles.languageText, isDark && styles.languageTextDark]}>
            🌐 {currentLanguage.toUpperCase()}
          </Text>
        </TouchableOpacity>

        {/* Logo Section */}
        <Animated.View style={[
          styles.logoSection,
          {
            opacity: fadeAnim,
            transform: [{ scale: logoScale }]
          }
        ]}>
          <AnimatedLogo size={140} isDark={isDark} showText={true} />
        </Animated.View>

        {/* Tagline */}
        <Animated.View style={[
          styles.taglineSection,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}>
          <Text style={[styles.tagline, isDark && styles.taglineDark]}>
            {t('welcome.tagline')}
          </Text>
          <Text style={[styles.subtitle, isDark && styles.subtitleDark]}>
            {t('welcome.subtitle')}
          </Text>
        </Animated.View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          {features.map((feature, index) => (
            <Animated.View
              key={index}
              style={[
                styles.featureCard,
                isDark && styles.featureCardDark,
                {
                  opacity: featureAnims[index],
                  transform: [{
                    translateY: featureAnims[index].interpolate({
                      inputRange: [0, 1],
                      outputRange: [30, 0],
                    })
                  }]
                }
              ]}
            >
              <View style={[styles.featureIcon, isDark && styles.featureIconDark]}>
                <Text style={styles.featureIconText}>{feature.icon}</Text>
              </View>
              <View style={styles.featureContent}>
                <Text style={[styles.featureTitle, isDark && styles.featureTitleDark]}>
                  {feature.title}
                </Text>
                <Text style={[styles.featureDescription, isDark && styles.featureDescriptionDark]}>
                  {feature.description}
                </Text>
              </View>
            </Animated.View>
          ))}
        </View>

        {/* Get Started Button */}
        <Animated.View style={[
          styles.buttonSection,
          {
            opacity: fadeAnim,
            transform: [{ translateY: slideAnim }]
          }
        ]}>
          <TouchableOpacity
            style={[styles.startButton, isDark && styles.startButtonDark]}
            onPress={onStart}
            activeOpacity={0.8}
          >
            <LinearGradient
              colors={['#FF8A50', '#FF6B35']}
              style={styles.buttonGradient}
              start={{ x: 0, y: 0 }}
              end={{ x: 1, y: 1 }}
            >
              <Text style={styles.startButtonText}>
                {t('welcome.getStarted')}
              </Text>
              <Text style={styles.startButtonIcon}>→</Text>
            </LinearGradient>
          </TouchableOpacity>
          
          <Text style={[styles.versionText, isDark && styles.versionTextDark]}>
            {t('welcome.version')} 1.0.0
          </Text>
        </Animated.View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    bottom: 0,
  },
  backgroundElements: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  floatingElement: {
    position: 'absolute',
    opacity: 0.1,
  },
  element1: {
    top: '15%',
    right: '10%',
  },
  element2: {
    top: '60%',
    left: '5%',
  },
  element3: {
    top: '40%',
    right: '15%',
  },
  floatingIcon: {
    fontSize: 40,
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 40,
    minHeight: height,
    justifyContent: 'center',
  },
  languageButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  languageText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
  },
  languageTextDark: {
    color: '#FFFFFF',
  },
  logoSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  taglineSection: {
    alignItems: 'center',
    marginBottom: 50,
  },
  tagline: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 30,
  },
  taglineDark: {
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 22,
  },
  subtitleDark: {
    color: '#8E8E93',
  },
  featuresSection: {
    marginBottom: 50,
  },
  featureCard: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  featureCardDark: {
    backgroundColor: 'rgba(28, 28, 30, 0.9)',
  },
  featureIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F8F9FA',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  featureIconDark: {
    backgroundColor: '#2C2C2E',
  },
  featureIconText: {
    fontSize: 24,
  },
  featureContent: {
    flex: 1,
    justifyContent: 'center',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 4,
  },
  featureTitleDark: {
    color: '#FFFFFF',
  },
  featureDescription: {
    fontSize: 14,
    color: '#7F8C8D',
    lineHeight: 20,
  },
  featureDescriptionDark: {
    color: '#8E8E93',
  },
  buttonSection: {
    alignItems: 'center',
  },
  startButton: {
    borderRadius: 25,
    overflow: 'hidden',
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    marginBottom: 20,
  },
  startButtonDark: {
    shadowColor: '#FF8A50',
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 40,
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    marginRight: 8,
  },
  startButtonIcon: {
    fontSize: 18,
    color: '#FFFFFF',
    fontWeight: 'bold',
  },
  versionText: {
    fontSize: 12,
    color: '#BDC3C7',
    textAlign: 'center',
  },
  versionTextDark: {
    color: '#6C6C70',
  },
});

export default WelcomeScreen;

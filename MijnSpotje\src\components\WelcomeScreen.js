import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { useTranslation } from '../i18n/useTranslation';

const WelcomeScreen = ({ onStart, isDark }) => {
  const { t, currentLanguage, changeLanguage, getAvailableLanguages } = useTranslation();

  const handleLanguageSelect = () => {
    const languages = getAvailableLanguages();
    Alert.alert(
      t('welcome.selectLanguage'),
      t('welcome.selectLanguageMessage'),
      [
        ...languages.map(lang => ({
          text: lang.name,
          onPress: () => changeLanguage(lang.code)
        })),
        { text: t('common.cancel'), style: 'cancel' }
      ]
    );
  };

  const features = [
    {
      icon: '🗺️',
      title: t('welcome.features.discover.title'),
      description: t('welcome.features.discover.description'),
    },
    {
      icon: '📍',
      title: t('welcome.features.share.title'),
      description: t('welcome.features.share.description'),
    },
    {
      icon: '👥',
      title: t('welcome.features.connect.title'),
      description: t('welcome.features.connect.description'),
    },
  ];

  return (
    <View style={[styles.container, isDark && styles.containerDark]}>
      <StatusBar style={isDark ? "light" : "dark"} />

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {/* Language Selector */}
        <TouchableOpacity
          style={styles.languageButton}
          onPress={handleLanguageSelect}
        >
          <Text style={[styles.languageText, isDark && styles.languageTextDark]}>
            🌐 {currentLanguage.toUpperCase()}
          </Text>
        </TouchableOpacity>

        {/* Logo Section */}
        <View style={styles.logoSection}>
          <Text style={[styles.logoIcon, isDark && styles.logoIconDark]}>🗺️</Text>
          <Text style={[styles.logoText, isDark && styles.logoTextDark]}>
            Mijn Spotje
          </Text>
          <Text style={[styles.logoSubtext, isDark && styles.logoSubtextDark]}>
            Ontdek & Deel
          </Text>
        </View>

        {/* Tagline */}
        <View style={styles.taglineSection}>
          <Text style={[styles.tagline, isDark && styles.taglineDark]}>
            {t('welcome.tagline')}
          </Text>
          <Text style={[styles.subtitle, isDark && styles.subtitleDark]}>
            {t('welcome.subtitle')}
          </Text>
        </View>

        {/* Features Section */}
        <View style={styles.featuresSection}>
          {features.map((feature, index) => (
            <View
              key={index}
              style={[styles.featureCard, isDark && styles.featureCardDark]}
            >
              <View style={[styles.featureIcon, isDark && styles.featureIconDark]}>
                <Text style={styles.featureIconText}>{feature.icon}</Text>
              </View>
              <View style={styles.featureContent}>
                <Text style={[styles.featureTitle, isDark && styles.featureTitleDark]}>
                  {feature.title}
                </Text>
                <Text style={[styles.featureDescription, isDark && styles.featureDescriptionDark]}>
                  {feature.description}
                </Text>
              </View>
            </View>
          ))}
        </View>

        {/* Get Started Button */}
        <View style={styles.buttonSection}>
          <TouchableOpacity
            style={[styles.startButton, isDark && styles.startButtonDark]}
            onPress={onStart}
            activeOpacity={0.8}
          >
            <Text style={styles.startButtonText}>
              {t('welcome.getStarted')} →
            </Text>
          </TouchableOpacity>

          <Text style={[styles.versionText, isDark && styles.versionTextDark]}>
            {t('welcome.version')} 1.0.0
          </Text>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  content: {
    flex: 1,
  },
  contentContainer: {
    paddingHorizontal: 20,
    paddingTop: 60,
    paddingBottom: 40,
    justifyContent: 'center',
  },
  languageButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
  },
  languageText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#666666',
  },
  languageTextDark: {
    color: '#FFFFFF',
  },
  logoSection: {
    alignItems: 'center',
    marginBottom: 40,
  },
  logoIcon: {
    fontSize: 80,
    marginBottom: 16,
  },
  logoIconDark: {
    // Same for dark mode
  },
  logoText: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 8,
  },
  logoTextDark: {
    color: '#FFFFFF',
  },
  logoSubtext: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    fontWeight: '500',
  },
  logoSubtextDark: {
    color: '#8E8E93',
  },
  taglineSection: {
    alignItems: 'center',
    marginBottom: 50,
  },
  tagline: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    marginBottom: 8,
    lineHeight: 30,
  },
  taglineDark: {
    color: '#FFFFFF',
  },
  subtitle: {
    fontSize: 16,
    color: '#7F8C8D',
    textAlign: 'center',
    lineHeight: 22,
  },
  subtitleDark: {
    color: '#8E8E93',
  },
  featuresSection: {
    marginBottom: 50,
  },
  featureCard: {
    flexDirection: 'row',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  featureCardDark: {
    backgroundColor: 'rgba(28, 28, 30, 0.9)',
  },
  featureIcon: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: '#F8F9FA',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  featureIconDark: {
    backgroundColor: '#2C2C2E',
  },
  featureIconText: {
    fontSize: 24,
  },
  featureContent: {
    flex: 1,
    justifyContent: 'center',
  },
  featureTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2C3E50',
    marginBottom: 4,
  },
  featureTitleDark: {
    color: '#FFFFFF',
  },
  featureDescription: {
    fontSize: 14,
    color: '#7F8C8D',
    lineHeight: 20,
  },
  featureDescriptionDark: {
    color: '#8E8E93',
  },
  buttonSection: {
    alignItems: 'center',
  },
  startButton: {
    backgroundColor: '#FF6B35',
    borderRadius: 25,
    paddingVertical: 16,
    paddingHorizontal: 40,
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
    marginBottom: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  startButtonDark: {
    backgroundColor: '#FF8A50',
    shadowColor: '#FF8A50',
  },
  startButtonText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#FFFFFF',
    textAlign: 'center',
  },
  versionText: {
    fontSize: 12,
    color: '#BDC3C7',
    textAlign: 'center',
  },
  versionTextDark: {
    color: '#6C6C70',
  },
});

export default WelcomeScreen;

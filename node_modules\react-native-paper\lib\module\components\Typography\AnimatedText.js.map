{"version": 3, "names": ["React", "Animated", "I18nManager", "StyleSheet", "useInternalTheme", "forwardRef", "AnimatedText", "style", "theme", "themeOverrides", "variant", "rest", "ref", "writingDirection", "getConstants", "isRTL", "isV3", "font", "fonts", "Error", "Object", "keys", "join", "createElement", "Text", "_extends", "styles", "text", "color", "colors", "onSurface", "regular", "bodyMedium", "textStyle", "create", "textAlign", "customAnimatedText"], "sourceRoot": "../../../../src", "sources": ["components/Typography/AnimatedText.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EACRC,WAAW,EAEXC,UAAU,QAGL,cAAc;AAGrB,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,SAASC,UAAU,QAAQ,wBAAwB;AAyBnD;AACA;AACA;AACA;AACA;AACA,MAAMC,YAAY,GAAGD,UAAU,CAC7B,SAASC,YAAYA,CACnB;EAAEC,KAAK;EAAEC,KAAK,EAAEC,cAAc;EAAEC,OAAO;EAAE,GAAGC;AAAK,CAAC,EAClDC,GAAG,EACH;EACA,MAAMJ,KAAK,GAAGJ,gBAAgB,CAACK,cAAc,CAAC;EAC9C,MAAMI,gBAAgB,GAAGX,WAAW,CAACY,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzE,IAAIP,KAAK,CAACQ,IAAI,IAAIN,OAAO,EAAE;IACzB,MAAMO,IAAI,GAAGT,KAAK,CAACU,KAAK,CAACR,OAAO,CAAC;IACjC,IAAI,OAAOO,IAAI,KAAK,QAAQ,EAAE;MAC5B,MAAM,IAAIE,KAAK,CACb,WAAWT,OAAO,kDAAkDU,MAAM,CAACC,IAAI,CAC7Eb,KAAK,CAACU,KACR,CAAC,CAACI,IAAI,CAAC,IAAI,CAAC,GACd,CAAC;IACH;IAEA,oBACEtB,KAAA,CAAAuB,aAAA,CAACtB,QAAQ,CAACuB,IAAI,EAAAC,QAAA;MACZb,GAAG,EAAEA;IAAI,GACLD,IAAI;MACRJ,KAAK,EAAE,CACLU,IAAI,EACJS,MAAM,CAACC,IAAI,EACX;QAAEd,gBAAgB;QAAEe,KAAK,EAAEpB,KAAK,CAACqB,MAAM,CAACC;MAAU,CAAC,EACnDvB,KAAK;IACL,EACH,CAAC;EAEN,CAAC,MAAM;IACL,MAAMU,IAAI,GAAG,CAACT,KAAK,CAACQ,IAAI,GAAGR,KAAK,CAACU,KAAK,CAACa,OAAO,GAAGvB,KAAK,CAACU,KAAK,CAACc,UAAU;IACvE,MAAMC,SAAS,GAAG;MAChB,GAAGhB,IAAI;MACPW,KAAK,EAAEpB,KAAK,CAACQ,IAAI,GAAGR,KAAK,CAACqB,MAAM,CAACC,SAAS,GAAGtB,KAAK,CAACqB,MAAM,CAACF;IAC5D,CAAC;IACD,oBACE3B,KAAA,CAAAuB,aAAA,CAACtB,QAAQ,CAACuB,IAAI,EAAAC,QAAA;MACZb,GAAG,EAAEA;IAAI,GACLD,IAAI;MACRJ,KAAK,EAAE,CACLmB,MAAM,CAACC,IAAI,EACXM,SAAS,EACT;QACEpB;MACF,CAAC,EACDN,KAAK;IACL,EACH,CAAC;EAEN;AACF,CACF,CAAC;AAED,MAAMmB,MAAM,GAAGvB,UAAU,CAAC+B,MAAM,CAAC;EAC/BP,IAAI,EAAE;IACJQ,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,OAAO,MAAMC,kBAAkB,GAAGA,CAAA,KAChC9B,YAAgD;AAElD,eAAeA,YAAY", "ignoreList": []}
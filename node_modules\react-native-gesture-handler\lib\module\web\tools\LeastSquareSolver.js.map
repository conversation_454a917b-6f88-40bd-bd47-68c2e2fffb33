{"version": 3, "sources": ["LeastSquareSolver.ts"], "names": ["Vector", "constructor", "length", "offset", "elements", "Array", "fromVOL", "values", "result", "get", "index", "set", "value", "dot", "other", "i", "norm", "Math", "sqrt", "Matrix", "rows", "columns", "row", "column", "getRow", "PolynomialFit", "degree", "coefficients", "precisionErrorTolerance", "LeastSquareSolver", "x", "y", "w", "solve", "m", "n", "a", "h", "q", "r", "j", "inverseNorm", "wy"], "mappings": ";;AAAA;AACA;AAEA,MAAMA,MAAN,CAAa;AAKXC,EAAAA,WAAW,CAACC,MAAD,EAAiB;AAAA;;AAAA;;AAAA;;AAC1B,SAAKC,MAAL,GAAc,CAAd;AACA,SAAKD,MAAL,GAAcA,MAAd;AACA,SAAKE,QAAL,GAAgB,IAAIC,KAAJ,CAAkBH,MAAlB,CAAhB;AACD;;AAEoB,SAAPI,OAAO,CACnBC,MADmB,EAEnBJ,MAFmB,EAGnBD,MAHmB,EAIX;AACR,UAAMM,MAAM,GAAG,IAAIR,MAAJ,CAAW,CAAX,CAAf;AAEAQ,IAAAA,MAAM,CAACL,MAAP,GAAgBA,MAAhB;AACAK,IAAAA,MAAM,CAACN,MAAP,GAAgBA,MAAhB;AACAM,IAAAA,MAAM,CAACJ,QAAP,GAAkBG,MAAlB;AAEA,WAAOC,MAAP;AACD;;AAEMC,EAAAA,GAAG,CAACC,KAAD,EAAwB;AAChC,WAAO,KAAKN,QAAL,CAAc,KAAKD,MAAL,GAAcO,KAA5B,CAAP;AACD;;AAEMC,EAAAA,GAAG,CAACD,KAAD,EAAgBE,KAAhB,EAAqC;AAC7C,SAAKR,QAAL,CAAc,KAAKD,MAAL,GAAcO,KAA5B,IAAqCE,KAArC;AACD;;AAEMC,EAAAA,GAAG,CAACC,KAAD,EAAwB;AAChC,QAAIN,MAAM,GAAG,CAAb;;AACA,SAAK,IAAIO,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAG,KAAKb,MAAzB,EAAiCa,CAAC,EAAlC,EAAsC;AACpCP,MAAAA,MAAM,IAAI,KAAKC,GAAL,CAASM,CAAT,IAAcD,KAAK,CAACL,GAAN,CAAUM,CAAV,CAAxB;AACD;;AACD,WAAOP,MAAP;AACD;;AAEMQ,EAAAA,IAAI,GAAG;AACZ,WAAOC,IAAI,CAACC,IAAL,CAAU,KAAKL,GAAL,CAAS,IAAT,CAAV,CAAP;AACD;;AA3CU;;AA8Cb,MAAMM,MAAN,CAAa;AAIXlB,EAAAA,WAAW,CAACmB,IAAD,EAAeC,OAAf,EAAgC;AAAA;;AAAA;;AACzC,SAAKA,OAAL,GAAeA,OAAf;AACA,SAAKjB,QAAL,GAAgB,IAAIC,KAAJ,CAAkBe,IAAI,GAAGC,OAAzB,CAAhB;AACD;;AAEMZ,EAAAA,GAAG,CAACa,GAAD,EAAcC,MAAd,EAAsC;AAC9C,WAAO,KAAKnB,QAAL,CAAckB,GAAG,GAAG,KAAKD,OAAX,GAAqBE,MAAnC,CAAP;AACD;;AAEMZ,EAAAA,GAAG,CAACW,GAAD,EAAcC,MAAd,EAA8BX,KAA9B,EAAmD;AAC3D,SAAKR,QAAL,CAAckB,GAAG,GAAG,KAAKD,OAAX,GAAqBE,MAAnC,IAA6CX,KAA7C;AACD;;AAEMY,EAAAA,MAAM,CAACF,GAAD,EAAsB;AACjC,WAAOtB,MAAM,CAACM,OAAP,CAAe,KAAKF,QAApB,EAA8BkB,GAAG,GAAG,KAAKD,OAAzC,EAAkD,KAAKA,OAAvD,CAAP;AACD;;AAnBU,C,CAsBb;;;AACA,MAAMI,aAAN,CAAoB;AAClB;AACA;AACA;AACA;AAGA;AACA;AACA;AACAxB,EAAAA,WAAW,CAACyB,MAAD,EAAiB;AAAA;;AAC1B,SAAKC,YAAL,GAAoB,IAAItB,KAAJ,CAAkBqB,MAAM,GAAG,CAA3B,CAApB;AACD;;AAZiB;;AAepB,MAAME,uBAAuB,GAAG,KAAhC,C,CAEA;;AACA,eAAe,MAAMC,iBAAN,CAAwB;AACrC;AAEA;AAEA;AAGA;AACA;AACA;AACA5B,EAAAA,WAAW,CAAC6B,CAAD,EAAcC,CAAd,EAA2BC,CAA3B,EAAwC;AAAA;;AAAA;;AAAA;;AACjD,SAAKF,CAAL,GAASA,CAAT;AACA,SAAKC,CAAL,GAASA,CAAT;AACA,SAAKC,CAAL,GAASA,CAAT;AACD,GAfoC,CAiBrC;AACA;AACA;;;AACOC,EAAAA,KAAK,CAACP,MAAD,EAAuC;AACjD,QAAIA,MAAM,GAAG,KAAKI,CAAL,CAAO5B,MAApB,EAA4B;AAC1B;AACA,aAAO,IAAP;AACD;;AAED,UAAMM,MAAM,GAAG,IAAIiB,aAAJ,CAAkBC,MAAlB,CAAf,CANiD,CAQjD;;AACA,UAAMQ,CAAC,GAAG,KAAKJ,CAAL,CAAO5B,MAAjB;AACA,UAAMiC,CAAC,GAAGT,MAAM,GAAG,CAAnB,CAViD,CAYjD;;AACA,UAAMU,CAAC,GAAG,IAAIjB,MAAJ,CAAWgB,CAAX,EAAcD,CAAd,CAAV;;AACA,SAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,CAApB,EAAuBG,CAAC,EAAxB,EAA4B;AAC1BD,MAAAA,CAAC,CAACzB,GAAF,CAAM,CAAN,EAAS0B,CAAT,EAAY,KAAKL,CAAL,CAAOK,CAAP,CAAZ;;AAEA,WAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoB,CAApB,EAAuBpB,CAAC,EAAxB,EAA4B;AAC1BqB,QAAAA,CAAC,CAACzB,GAAF,CAAMI,CAAN,EAASsB,CAAT,EAAYD,CAAC,CAAC3B,GAAF,CAAMM,CAAC,GAAG,CAAV,EAAasB,CAAb,IAAkB,KAAKP,CAAL,CAAOO,CAAP,CAA9B;AACD;AACF,KApBgD,CAsBjD;AAEA;;;AACA,UAAMC,CAAC,GAAG,IAAInB,MAAJ,CAAWgB,CAAX,EAAcD,CAAd,CAAV,CAzBiD,CA0BjD;;AACA,UAAMK,CAAC,GAAG,IAAIpB,MAAJ,CAAWgB,CAAX,EAAcD,CAAd,CAAV;;AAEA,SAAK,IAAIM,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGL,CAApB,EAAuBK,CAAC,IAAI,CAA5B,EAA+B;AAC7B,WAAK,IAAIH,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,CAApB,EAAuBG,CAAC,IAAI,CAA5B,EAA+B;AAC7BC,QAAAA,CAAC,CAAC3B,GAAF,CAAM6B,CAAN,EAASH,CAAT,EAAYD,CAAC,CAAC3B,GAAF,CAAM+B,CAAN,EAASH,CAAT,CAAZ;AACD;;AACD,WAAK,IAAItB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGyB,CAApB,EAAuBzB,CAAC,IAAI,CAA5B,EAA+B;AAC7B,cAAMF,GAAG,GAAGyB,CAAC,CAACd,MAAF,CAASgB,CAAT,EAAY3B,GAAZ,CAAgByB,CAAC,CAACd,MAAF,CAAST,CAAT,CAAhB,CAAZ;;AACA,aAAK,IAAIsB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,CAApB,EAAuBG,CAAC,IAAI,CAA5B,EAA+B;AAC7BC,UAAAA,CAAC,CAAC3B,GAAF,CAAM6B,CAAN,EAASH,CAAT,EAAYC,CAAC,CAAC7B,GAAF,CAAM+B,CAAN,EAASH,CAAT,IAAcxB,GAAG,GAAGyB,CAAC,CAAC7B,GAAF,CAAMM,CAAN,EAASsB,CAAT,CAAhC;AACD;AACF;;AAED,YAAMrB,IAAI,GAAGsB,CAAC,CAACd,MAAF,CAASgB,CAAT,EAAYxB,IAAZ,EAAb;;AACA,UAAIA,IAAI,GAAGY,uBAAX,EAAoC;AAClC;AACA,eAAO,IAAP;AACD;;AAED,YAAMa,WAAW,GAAG,MAAMzB,IAA1B;;AACA,WAAK,IAAIqB,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,CAApB,EAAuBG,CAAC,IAAI,CAA5B,EAA+B;AAC7BC,QAAAA,CAAC,CAAC3B,GAAF,CAAM6B,CAAN,EAASH,CAAT,EAAYC,CAAC,CAAC7B,GAAF,CAAM+B,CAAN,EAASH,CAAT,IAAcI,WAA1B;AACD;;AACD,WAAK,IAAI1B,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGoB,CAApB,EAAuBpB,CAAC,IAAI,CAA5B,EAA+B;AAC7BwB,QAAAA,CAAC,CAAC5B,GAAF,CAAM6B,CAAN,EAASzB,CAAT,EAAYA,CAAC,GAAGyB,CAAJ,GAAQ,GAAR,GAAcF,CAAC,CAACd,MAAF,CAASgB,CAAT,EAAY3B,GAAZ,CAAgBuB,CAAC,CAACZ,MAAF,CAAST,CAAT,CAAhB,CAA1B;AACD;AACF,KArDgD,CAuDjD;AACA;;;AACA,UAAM2B,EAAE,GAAG,IAAI1C,MAAJ,CAAWkC,CAAX,CAAX;;AACA,SAAK,IAAIG,CAAC,GAAG,CAAb,EAAgBA,CAAC,GAAGH,CAApB,EAAuBG,CAAC,IAAI,CAA5B,EAA+B;AAC7BK,MAAAA,EAAE,CAAC/B,GAAH,CAAO0B,CAAP,EAAU,KAAKN,CAAL,CAAOM,CAAP,IAAY,KAAKL,CAAL,CAAOK,CAAP,CAAtB;AACD;;AACD,SAAK,IAAItB,CAAC,GAAGoB,CAAC,GAAG,CAAjB,EAAoBpB,CAAC,IAAI,CAAzB,EAA4BA,CAAC,IAAI,CAAjC,EAAoC;AAClCP,MAAAA,MAAM,CAACmB,YAAP,CAAoBZ,CAApB,IAAyBuB,CAAC,CAACd,MAAF,CAAST,CAAT,EAAYF,GAAZ,CAAgB6B,EAAhB,CAAzB;;AACA,WAAK,IAAIF,CAAC,GAAGL,CAAC,GAAG,CAAjB,EAAoBK,CAAC,GAAGzB,CAAxB,EAA2ByB,CAAC,IAAI,CAAhC,EAAmC;AACjChC,QAAAA,MAAM,CAACmB,YAAP,CAAoBZ,CAApB,KAA0BwB,CAAC,CAAC9B,GAAF,CAAMM,CAAN,EAASyB,CAAT,IAAchC,MAAM,CAACmB,YAAP,CAAoBa,CAApB,CAAxC;AACD;;AACDhC,MAAAA,MAAM,CAACmB,YAAP,CAAoBZ,CAApB,KAA0BwB,CAAC,CAAC9B,GAAF,CAAMM,CAAN,EAASA,CAAT,CAA1B;AACD;;AAED,WAAOP,MAAP;AACD;;AA1FoC", "sourcesContent": ["// Implementation taken from Flutter's LeastSquareSolver\n// https://github.com/flutter/flutter/blob/master/packages/flutter/lib/src/gestures/lsq_solver.dart\n\nclass Vector {\n  private offset: number;\n  private length: number;\n  private elements: number[];\n\n  constructor(length: number) {\n    this.offset = 0;\n    this.length = length;\n    this.elements = new Array<number>(length);\n  }\n\n  public static fromVOL(\n    values: number[],\n    offset: number,\n    length: number\n  ): Vector {\n    const result = new Vector(0);\n\n    result.offset = offset;\n    result.length = length;\n    result.elements = values;\n\n    return result;\n  }\n\n  public get(index: number): number {\n    return this.elements[this.offset + index];\n  }\n\n  public set(index: number, value: number): void {\n    this.elements[this.offset + index] = value;\n  }\n\n  public dot(other: Vector): number {\n    let result = 0;\n    for (let i = 0; i < this.length; i++) {\n      result += this.get(i) * other.get(i);\n    }\n    return result;\n  }\n\n  public norm() {\n    return Math.sqrt(this.dot(this));\n  }\n}\n\nclass Matrix {\n  private columns: number;\n  private elements: number[];\n\n  constructor(rows: number, columns: number) {\n    this.columns = columns;\n    this.elements = new Array<number>(rows * columns);\n  }\n\n  public get(row: number, column: number): number {\n    return this.elements[row * this.columns + column];\n  }\n\n  public set(row: number, column: number, value: number): void {\n    this.elements[row * this.columns + column] = value;\n  }\n\n  public getRow(row: number): Vector {\n    return Vector.fromVOL(this.elements, row * this.columns, this.columns);\n  }\n}\n\n// An nth degree polynomial fit to a dataset.\nclass PolynomialFit {\n  // The polynomial coefficients of the fit.\n  //\n  // For each `i`, the element `coefficients[i]` is the coefficient of\n  // the `i`-th power of the variable.\n  public coefficients: number[];\n\n  // Creates a polynomial fit of the given degree.\n  //\n  // There are n + 1 coefficients in a fit of degree n.\n  constructor(degree: number) {\n    this.coefficients = new Array<number>(degree + 1);\n  }\n}\n\nconst precisionErrorTolerance = 1e-10;\n\n// Uses the least-squares algorithm to fit a polynomial to a set of data.\nexport default class LeastSquareSolver {\n  // The x-coordinates of each data point.\n  private x: number[];\n  // The y-coordinates of each data point.\n  private y: number[];\n  // The weight to use for each data point.\n  private w: number[];\n\n  // Creates a least-squares solver.\n  //\n  // The [x], [y], and [w] arguments must not be null.\n  constructor(x: number[], y: number[], w: number[]) {\n    this.x = x;\n    this.y = y;\n    this.w = w;\n  }\n\n  // Fits a polynomial of the given degree to the data points.\n  //\n  // When there is not enough data to fit a curve null is returned.\n  public solve(degree: number): PolynomialFit | null {\n    if (degree > this.x.length) {\n      // Not enough data to fit a curve.\n      return null;\n    }\n\n    const result = new PolynomialFit(degree);\n\n    // Shorthands for the purpose of notation equivalence to original C++ code.\n    const m = this.x.length;\n    const n = degree + 1;\n\n    // Expand the X vector to a matrix A, pre-multiplied by the weights.\n    const a = new Matrix(n, m);\n    for (let h = 0; h < m; h++) {\n      a.set(0, h, this.w[h]);\n\n      for (let i = 1; i < n; i++) {\n        a.set(i, h, a.get(i - 1, h) * this.x[h]);\n      }\n    }\n\n    // Apply the Gram-Schmidt process to A to obtain its QR decomposition.\n\n    // Orthonormal basis, column-major ordVectorer.\n    const q = new Matrix(n, m);\n    // Upper triangular matrix, row-major order.\n    const r = new Matrix(n, m);\n\n    for (let j = 0; j < n; j += 1) {\n      for (let h = 0; h < m; h += 1) {\n        q.set(j, h, a.get(j, h));\n      }\n      for (let i = 0; i < j; i += 1) {\n        const dot = q.getRow(j).dot(q.getRow(i));\n        for (let h = 0; h < m; h += 1) {\n          q.set(j, h, q.get(j, h) - dot * q.get(i, h));\n        }\n      }\n\n      const norm = q.getRow(j).norm();\n      if (norm < precisionErrorTolerance) {\n        // Vectors are linearly dependent or zero so no solution.\n        return null;\n      }\n\n      const inverseNorm = 1.0 / norm;\n      for (let h = 0; h < m; h += 1) {\n        q.set(j, h, q.get(j, h) * inverseNorm);\n      }\n      for (let i = 0; i < n; i += 1) {\n        r.set(j, i, i < j ? 0.0 : q.getRow(j).dot(a.getRow(i)));\n      }\n    }\n\n    // Solve R B = Qt W Y to find B. This is easy because R is upper triangular.\n    // We just work from bottom-right to top-left calculating B's coefficients.\n    const wy = new Vector(m);\n    for (let h = 0; h < m; h += 1) {\n      wy.set(h, this.y[h] * this.w[h]);\n    }\n    for (let i = n - 1; i >= 0; i -= 1) {\n      result.coefficients[i] = q.getRow(i).dot(wy);\n      for (let j = n - 1; j > i; j -= 1) {\n        result.coefficients[i] -= r.get(i, j) * result.coefficients[j];\n      }\n      result.coefficients[i] /= r.get(i, i);\n    }\n\n    return result;\n  }\n}\n"]}
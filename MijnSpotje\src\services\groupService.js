import { supabase, TABLES } from '../config/supabase';

export const groupService = {
  // Create a new group
  async createGroup(groupData) {
    try {
      const { data: group, error: groupError } = await supabase
        .from(TABLES.GROUPS)
        .insert([{
          ...groupData,
          invite_code: this.generateInviteCode(),
          created_at: new Date().toISOString(),
        }])
        .select()
        .single();

      if (groupError) throw groupError;

      // Add creator as admin member
      const { error: memberError } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .insert([{
          group_id: group.id,
          user_id: groupData.created_by,
          role: 'admin',
          joined_at: new Date().toISOString(),
        }]);

      if (memberError) throw memberError;

      return { data: group, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Generate unique invite code
  generateInviteCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },

  // Join group by invite code
  async joinGroupByCode(inviteCode, userId) {
    try {
      // Find group by invite code
      const { data: group, error: groupError } = await supabase
        .from(TABLES.GROUPS)
        .select('*')
        .eq('invite_code', inviteCode.toUpperCase())
        .single();

      if (groupError) throw new Error('Ongeldige uitnodigingscode');

      // Check if user is already a member
      const { data: existingMember } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .select('id')
        .eq('group_id', group.id)
        .eq('user_id', userId)
        .single();

      if (existingMember) {
        throw new Error('Je bent al lid van deze groep');
      }

      // Add user to group
      const { data: member, error: memberError } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .insert([{
          group_id: group.id,
          user_id: userId,
          role: 'member',
          joined_at: new Date().toISOString(),
        }])
        .select()
        .single();

      if (memberError) throw memberError;

      return { data: { group, member }, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get user's groups
  async getUserGroups(userId) {
    try {
      const { data, error } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .select(`
          role,
          joined_at,
          groups (
            id,
            name,
            description,
            is_public,
            invite_code,
            created_by,
            created_at,
            group_members (
              user_id,
              users (
                username,
                avatar_url
              )
            )
          )
        `)
        .eq('user_id', userId)
        .order('joined_at', { ascending: false });

      if (error) throw error;

      const groups = data.map(item => ({
        ...item.groups,
        userRole: item.role,
        joinedAt: item.joined_at,
        memberCount: item.groups.group_members.length,
        members: item.groups.group_members.map(member => member.users),
      }));

      return { data: groups, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get group details
  async getGroupById(groupId, userId) {
    try {
      const { data, error } = await supabase
        .from(TABLES.GROUPS)
        .select(`
          *,
          group_members (
            user_id,
            role,
            joined_at,
            users (
              username,
              avatar_url,
              level
            )
          )
        `)
        .eq('id', groupId)
        .single();

      if (error) throw error;

      // Check if user is a member
      const userMembership = data.group_members.find(member => member.user_id === userId);
      
      if (!userMembership && !data.is_public) {
        throw new Error('Je hebt geen toegang tot deze groep');
      }

      const group = {
        ...data,
        userRole: userMembership?.role || null,
        memberCount: data.group_members.length,
        members: data.group_members.map(member => ({
          ...member.users,
          role: member.role,
          joinedAt: member.joined_at,
        })),
      };

      return { data: group, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get group members
  async getGroupMembers(groupId) {
    try {
      const { data, error } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .select(`
          role,
          joined_at,
          users (
            id,
            username,
            avatar_url,
            level,
            experience_points
          )
        `)
        .eq('group_id', groupId)
        .order('joined_at', { ascending: true });

      if (error) throw error;

      const members = data.map(member => ({
        ...member.users,
        role: member.role,
        joinedAt: member.joined_at,
      }));

      return { data: members, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Leave group
  async leaveGroup(groupId, userId) {
    try {
      const { error } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', userId);

      if (error) throw error;
      return { data: true, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Update group
  async updateGroup(groupId, updates) {
    try {
      const { data, error } = await supabase
        .from(TABLES.GROUPS)
        .update(updates)
        .eq('id', groupId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Remove member (admin only)
  async removeMember(groupId, userId, adminId) {
    try {
      // Check if requester is admin
      const { data: adminMember } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .select('role')
        .eq('group_id', groupId)
        .eq('user_id', adminId)
        .single();

      if (!adminMember || adminMember.role !== 'admin') {
        throw new Error('Alleen admins kunnen leden verwijderen');
      }

      const { error } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .delete()
        .eq('group_id', groupId)
        .eq('user_id', userId);

      if (error) throw error;
      return { data: true, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Update member role (admin only)
  async updateMemberRole(groupId, userId, newRole, adminId) {
    try {
      // Check if requester is admin
      const { data: adminMember } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .select('role')
        .eq('group_id', groupId)
        .eq('user_id', adminId)
        .single();

      if (!adminMember || adminMember.role !== 'admin') {
        throw new Error('Alleen admins kunnen rollen wijzigen');
      }

      const { data, error } = await supabase
        .from(TABLES.GROUP_MEMBERS)
        .update({ role: newRole })
        .eq('group_id', groupId)
        .eq('user_id', userId)
        .select()
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Search public groups
  async searchPublicGroups(searchTerm = '') {
    try {
      let query = supabase
        .from(TABLES.GROUPS)
        .select(`
          *,
          group_members (
            user_id
          )
        `)
        .eq('is_public', true);

      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,description.ilike.%${searchTerm}%`);
      }

      query = query.order('created_at', { ascending: false });

      const { data, error } = await query;

      if (error) throw error;

      const groups = data.map(group => ({
        ...group,
        memberCount: group.group_members.length,
      }));

      return { data: groups, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },
};

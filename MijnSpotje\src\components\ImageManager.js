import React, { useState, useCallback, memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Alert,
  Modal,
  Dimensions,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import OptimizedImage from './OptimizedImage';
import PerformanceMonitor from '../utils/PerformanceMonitor';

const { width } = Dimensions.get('window');

const ImageManager = memo(({
  images = [],
  onImagesChange,
  isDark,
  maxImages = 10,
  editable = true
}) => {
  const [selectedImage, setSelectedImage] = useState(null);
  const [showImageViewer, setShowImageViewer] = useState(false);

  const handleAddImage = useCallback(() => {
    if (images.length >= maxImages) {
      Alert.alert(
        'Maximum bereikt',
        `Je kunt maximaal ${maxImages} afbeeldingen toevoegen.`,
        [{ text: 'OK' }]
      );
      return;
    }

    Alert.alert(
      'Afbeelding toevoegen',
      'Kies hoe je een afbeelding wilt toevoegen',
      [
        { text: 'Annuleren', style: 'cancel' },
        { text: 'Camera', onPress: handleTakePhoto },
        { text: 'Galerij', onPress: handlePickImage },
      ]
    );
  }, [images.length, maxImages]);

  const handleTakePhoto = useCallback(async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Toestemming Vereist',
          'We hebben toegang tot je camera nodig om foto\'s te maken.',
          [{ text: 'OK' }]
        );
        return;
      }

      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });

      if (!result.canceled && result.assets[0]) {
        const newImage = {
          id: Date.now().toString(),
          uri: result.assets[0].uri,
          width: result.assets[0].width,
          height: result.assets[0].height,
          type: 'photo',
          createdAt: new Date().toISOString(),
        };
        
        onImagesChange([...images, newImage]);
      }
    } catch (error) {
      console.error('Error taking photo:', error);
      Alert.alert('Fout', 'Kon foto niet maken');
    }
  }, [images, onImagesChange]);

  const handlePickImage = useCallback(async () => {
    try {
      const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert(
          'Toestemming Vereist',
          'We hebben toegang tot je foto\'s nodig om afbeeldingen toe te voegen.',
          [{ text: 'OK' }]
        );
        return;
      }

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (!result.canceled && result.assets[0]) {
        const newImage = {
          id: Date.now().toString(),
          uri: result.assets[0].uri,
          width: result.assets[0].width,
          height: result.assets[0].height,
          type: 'gallery',
          createdAt: new Date().toISOString(),
        };
        
        onImagesChange([...images, newImage]);
      }
    } catch (error) {
      console.error('Error picking image:', error);
      Alert.alert('Fout', 'Kon afbeelding niet toevoegen');
    }
  }, [images, onImagesChange]);

  const handleDeleteImage = useCallback((imageId) => {
    Alert.alert(
      'Afbeelding verwijderen',
      'Weet je zeker dat je deze afbeelding wilt verwijderen?',
      [
        { text: 'Annuleren', style: 'cancel' },
        {
          text: 'Verwijderen',
          style: 'destructive',
          onPress: () => {
            const updatedImages = images.filter(img => img.id !== imageId);
            onImagesChange(updatedImages);
          }
        }
      ]
    );
  }, [images, onImagesChange]);

  const handleImagePress = useCallback((image) => {
    setSelectedImage(image);
    setShowImageViewer(true);
  }, []);

  const handleMoveImage = useCallback((fromIndex, toIndex) => {
    const newImages = [...images];
    const [movedImage] = newImages.splice(fromIndex, 1);
    newImages.splice(toIndex, 0, movedImage);
    onImagesChange(newImages);
  }, [images, onImagesChange]);

  const renderImage = (image, index) => (
    <View key={image.id} style={styles.imageContainer}>
      <TouchableOpacity
        style={styles.imageWrapper}
        onPress={() => handleImagePress(image)}
        activeOpacity={0.8}
      >
        <OptimizedImage source={{ uri: image.uri }} style={styles.image} />
        
        {/* Image overlay with info */}
        <View style={styles.imageOverlay}>
          <Text style={styles.imageIndex}>{index + 1}</Text>
        </View>
      </TouchableOpacity>

      {/* Delete button */}
      {editable && (
        <TouchableOpacity
          style={styles.deleteButton}
          onPress={() => handleDeleteImage(image.id)}
        >
          <Text style={styles.deleteButtonText}>×</Text>
        </TouchableOpacity>
      )}

      {/* Move buttons */}
      {editable && images.length > 1 && (
        <View style={styles.moveButtons}>
          {index > 0 && (
            <TouchableOpacity
              style={[styles.moveButton, styles.moveButtonLeft]}
              onPress={() => handleMoveImage(index, index - 1)}
            >
              <Text style={styles.moveButtonText}>←</Text>
            </TouchableOpacity>
          )}
          {index < images.length - 1 && (
            <TouchableOpacity
              style={[styles.moveButton, styles.moveButtonRight]}
              onPress={() => handleMoveImage(index, index + 1)}
            >
              <Text style={styles.moveButtonText}>→</Text>
            </TouchableOpacity>
          )}
        </View>
      )}
    </View>
  );

  const renderAddButton = () => (
    <TouchableOpacity
      style={[styles.addButton, isDark && styles.addButtonDark]}
      onPress={handleAddImage}
    >
      <Text style={styles.addButtonIcon}>📷</Text>
      <Text style={[styles.addButtonText, isDark && styles.addButtonTextDark]}>
        Foto toevoegen
      </Text>
    </TouchableOpacity>
  );

  return (
    <View style={styles.container}>
      {/* Images Grid */}
      {images.length > 0 && (
        <ScrollView 
          horizontal 
          showsHorizontalScrollIndicator={false}
          style={styles.imagesScroll}
          contentContainerStyle={styles.imagesContainer}
        >
          {images.map(renderImage)}
        </ScrollView>
      )}

      {/* Add Image Button */}
      {editable && images.length < maxImages && renderAddButton()}

      {/* Image Count */}
      <View style={styles.imageCount}>
        <Text style={[styles.imageCountText, isDark && styles.imageCountTextDark]}>
          {images.length} van {maxImages} afbeeldingen
        </Text>
      </View>

      {/* Full Screen Image Viewer */}
      <Modal
        visible={showImageViewer}
        transparent={true}
        animationType="fade"
        onRequestClose={() => setShowImageViewer(false)}
      >
        <View style={styles.imageViewerContainer}>
          <TouchableOpacity
            style={styles.imageViewerBackground}
            onPress={() => setShowImageViewer(false)}
          >
            <View style={styles.imageViewerContent}>
              {selectedImage && (
                <OptimizedImage
                  source={{ uri: selectedImage.uri }}
                  style={styles.fullScreenImage}
                  resizeMode="contain"
                />
              )}
              
              <TouchableOpacity
                style={styles.closeButton}
                onPress={() => setShowImageViewer(false)}
              >
                <Text style={styles.closeButtonText}>×</Text>
              </TouchableOpacity>

              {editable && selectedImage && (
                <TouchableOpacity
                  style={styles.deleteFullScreenButton}
                  onPress={() => {
                    setShowImageViewer(false);
                    handleDeleteImage(selectedImage.id);
                  }}
                >
                  <Text style={styles.deleteFullScreenButtonText}>🗑️ Verwijderen</Text>
                </TouchableOpacity>
              )}
            </View>
          </TouchableOpacity>
        </View>
      </Modal>
    </View>
  );
});

ImageManager.displayName = 'ImageManager';

const styles = StyleSheet.create({
  container: {
    marginVertical: 8,
  },
  imagesScroll: {
    marginBottom: 12,
  },
  imagesContainer: {
    paddingHorizontal: 4,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  imageWrapper: {
    position: 'relative',
  },
  image: {
    width: 120,
    height: 120,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
  },
  imageOverlay: {
    position: 'absolute',
    top: 8,
    left: 8,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 12,
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  imageIndex: {
    color: '#ffffff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  deleteButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#ff3b30',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  deleteButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    lineHeight: 16,
  },
  moveButtons: {
    position: 'absolute',
    bottom: 8,
    left: 8,
    right: 8,
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  moveButton: {
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 16,
    width: 32,
    height: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moveButtonLeft: {
    marginRight: 'auto',
  },
  moveButtonRight: {
    marginLeft: 'auto',
  },
  moveButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  addButton: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
    marginBottom: 8,
  },
  addButtonDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
  },
  addButtonIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  addButtonText: {
    fontSize: 14,
    color: '#666666',
    fontWeight: '500',
  },
  addButtonTextDark: {
    color: '#8e8e93',
  },
  imageCount: {
    alignItems: 'center',
  },
  imageCountText: {
    fontSize: 12,
    color: '#666666',
  },
  imageCountTextDark: {
    color: '#8e8e93',
  },
  imageViewerContainer: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.9)',
  },
  imageViewerBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageViewerContent: {
    width: width,
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  fullScreenImage: {
    width: width - 40,
    height: '70%',
  },
  closeButton: {
    position: 'absolute',
    top: 60,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  closeButtonText: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  deleteFullScreenButton: {
    position: 'absolute',
    bottom: 100,
    backgroundColor: '#ff3b30',
    borderRadius: 25,
    paddingHorizontal: 20,
    paddingVertical: 12,
  },
  deleteFullScreenButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ImageManager;

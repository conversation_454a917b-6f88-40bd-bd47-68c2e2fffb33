import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Alert,
  Linking,
} from 'react-native';
import { useTranslation } from '../i18n/useTranslation';

const ContactSupportModal = ({ visible, onClose, isDark }) => {
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState('contact');
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    subject: '',
    category: 'technical',
    message: '',
  });
  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const categories = [
    { value: 'technical', label: t('support.categories.technical') },
    { value: 'account', label: t('support.categories.account') },
    { value: 'feature', label: t('support.categories.feature') },
    { value: 'bug', label: t('support.categories.bug') },
    { value: 'other', label: t('support.categories.other') },
  ];

  const validateForm = useCallback(() => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = t('support.form.nameRequired');
    }
    
    if (!formData.email.trim()) {
      newErrors.email = t('support.form.emailRequired');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = t('support.form.emailInvalid');
    }
    
    if (!formData.subject.trim()) {
      newErrors.subject = t('support.form.subjectRequired');
    }
    
    if (!formData.message.trim()) {
      newErrors.message = t('support.form.messageRequired');
    } else if (formData.message.length < 10) {
      newErrors.message = t('support.form.messageMinLength');
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, t]);

  const handleSubmit = useCallback(async () => {
    if (!validateForm()) return;
    
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      Alert.alert(
        t('support.form.successTitle'),
        t('support.form.successMessage'),
        [{ text: t('common.ok'), onPress: () => {
          setFormData({
            name: '',
            email: '',
            subject: '',
            category: 'technical',
            message: '',
          });
          setErrors({});
          onClose();
        }}]
      );
    } catch (error) {
      Alert.alert(t('common.error'), t('support.form.errorMessage'));
    } finally {
      setIsSubmitting(false);
    }
  }, [validateForm, t, onClose]);

  const handleEmailPress = () => {
    Linking.openURL('mailto:<EMAIL>?subject=Support Request');
  };

  const renderContactForm = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={styles.formGroup}>
        <Text style={[styles.label, isDark && styles.labelDark]}>{t('support.form.name')} *</Text>
        <TextInput
          style={[styles.input, isDark && styles.inputDark, errors.name && styles.inputError]}
          value={formData.name}
          onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
          placeholder={t('support.form.namePlaceholder')}
          placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
          editable={!isSubmitting}
        />
        {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, isDark && styles.labelDark]}>{t('support.form.email')} *</Text>
        <TextInput
          style={[styles.input, isDark && styles.inputDark, errors.email && styles.inputError]}
          value={formData.email}
          onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
          placeholder={t('support.form.emailPlaceholder')}
          placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
          keyboardType="email-address"
          autoCapitalize="none"
          editable={!isSubmitting}
        />
        {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, isDark && styles.labelDark]}>{t('support.form.category')} *</Text>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
          {categories.map((category) => (
            <TouchableOpacity
              key={category.value}
              style={[
                styles.categoryChip,
                formData.category === category.value && styles.categoryChipSelected,
                isDark && styles.categoryChipDark,
                isDark && formData.category === category.value && styles.categoryChipSelectedDark,
              ]}
              onPress={() => setFormData(prev => ({ ...prev, category: category.value }))}
              disabled={isSubmitting}
            >
              <Text style={[
                styles.categoryChipText,
                formData.category === category.value && styles.categoryChipTextSelected,
                isDark && styles.categoryChipTextDark,
                isDark && formData.category === category.value && styles.categoryChipTextSelectedDark,
              ]}>
                {category.label}
              </Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, isDark && styles.labelDark]}>{t('support.form.subject')} *</Text>
        <TextInput
          style={[styles.input, isDark && styles.inputDark, errors.subject && styles.inputError]}
          value={formData.subject}
          onChangeText={(text) => setFormData(prev => ({ ...prev, subject: text }))}
          placeholder={t('support.form.subjectPlaceholder')}
          placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
          editable={!isSubmitting}
        />
        {errors.subject && <Text style={styles.errorText}>{errors.subject}</Text>}
      </View>

      <View style={styles.formGroup}>
        <Text style={[styles.label, isDark && styles.labelDark]}>{t('support.form.message')} *</Text>
        <TextInput
          style={[styles.textArea, isDark && styles.inputDark, errors.message && styles.inputError]}
          value={formData.message}
          onChangeText={(text) => setFormData(prev => ({ ...prev, message: text }))}
          placeholder={t('support.form.messagePlaceholder')}
          placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
          multiline
          numberOfLines={6}
          textAlignVertical="top"
          editable={!isSubmitting}
        />
        {errors.message && <Text style={styles.errorText}>{errors.message}</Text>}
      </View>

      <TouchableOpacity
        style={[styles.submitButton, isDark && styles.submitButtonDark, isSubmitting && styles.submitButtonDisabled]}
        onPress={handleSubmit}
        disabled={isSubmitting}
      >
        <Text style={[styles.submitButtonText, isSubmitting && styles.submitButtonTextDisabled]}>
          {isSubmitting ? t('support.form.submitting') : t('support.form.submit')}
        </Text>
      </TouchableOpacity>

      <View style={styles.bottomPadding} />
    </ScrollView>
  );

  const renderFAQ = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={styles.faqItem}>
        <Text style={[styles.faqQuestion, isDark && styles.textDark]}>
          {t('support.faq.question1')}
        </Text>
        <Text style={[styles.faqAnswer, isDark && styles.textSecondaryDark]}>
          {t('support.faq.answer1')}
        </Text>
      </View>

      <View style={styles.faqItem}>
        <Text style={[styles.faqQuestion, isDark && styles.textDark]}>
          {t('support.faq.question2')}
        </Text>
        <Text style={[styles.faqAnswer, isDark && styles.textSecondaryDark]}>
          {t('support.faq.answer2')}
        </Text>
      </View>

      <View style={styles.faqItem}>
        <Text style={[styles.faqQuestion, isDark && styles.textDark]}>
          {t('support.faq.question3')}
        </Text>
        <Text style={[styles.faqAnswer, isDark && styles.textSecondaryDark]}>
          {t('support.faq.answer3')}
        </Text>
      </View>

      <View style={styles.faqItem}>
        <Text style={[styles.faqQuestion, isDark && styles.textDark]}>
          {t('support.faq.question4')}
        </Text>
        <Text style={[styles.faqAnswer, isDark && styles.textSecondaryDark]}>
          {t('support.faq.answer4')}
        </Text>
      </View>

      <View style={styles.bottomPadding} />
    </ScrollView>
  );

  const renderContact = () => (
    <ScrollView style={styles.tabContent} showsVerticalScrollIndicator={false}>
      <View style={styles.contactSection}>
        <Text style={[styles.contactTitle, isDark && styles.textDark]}>
          {t('support.contact.title')}
        </Text>
        <Text style={[styles.contactText, isDark && styles.textSecondaryDark]}>
          {t('support.contact.description')}
        </Text>
      </View>

      <TouchableOpacity style={[styles.contactMethod, isDark && styles.contactMethodDark]} onPress={handleEmailPress}>
        <Text style={styles.contactIcon}>📧</Text>
        <View style={styles.contactInfo}>
          <Text style={[styles.contactMethodTitle, isDark && styles.textDark]}>
            {t('support.contact.email')}
          </Text>
          <Text style={[styles.contactMethodText, isDark && styles.textSecondaryDark]}>
            <EMAIL>
          </Text>
        </View>
        <Text style={[styles.contactArrow, isDark && styles.textSecondaryDark]}>›</Text>
      </TouchableOpacity>

      <View style={[styles.contactMethod, isDark && styles.contactMethodDark]}>
        <Text style={styles.contactIcon}>📞</Text>
        <View style={styles.contactInfo}>
          <Text style={[styles.contactMethodTitle, isDark && styles.textDark]}>
            {t('support.contact.phone')}
          </Text>
          <Text style={[styles.contactMethodText, isDark && styles.textSecondaryDark]}>
            +31 20 123 4567
          </Text>
        </View>
      </View>

      <View style={[styles.contactMethod, isDark && styles.contactMethodDark]}>
        <Text style={styles.contactIcon}>🕒</Text>
        <View style={styles.contactInfo}>
          <Text style={[styles.contactMethodTitle, isDark && styles.textDark]}>
            {t('support.contact.hours')}
          </Text>
          <Text style={[styles.contactMethodText, isDark && styles.textSecondaryDark]}>
            {t('support.contact.hoursText')}
          </Text>
        </View>
      </View>

      <View style={styles.bottomPadding} />
    </ScrollView>
  );

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, isDark && styles.containerDark]}>
        <View style={[styles.header, isDark && styles.headerDark]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.button, styles.closeButton]}>{t('common.close')}</Text>
          </TouchableOpacity>
          <Text style={[styles.title, isDark && styles.textDark]}>{t('support.title')}</Text>
          <View style={styles.placeholder} />
        </View>

        <View style={[styles.tabBar, isDark && styles.tabBarDark]}>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'contact' && styles.tabActive]}
            onPress={() => setActiveTab('contact')}
          >
            <Text style={[
              styles.tabText,
              activeTab === 'contact' && styles.tabTextActive,
              isDark && styles.tabTextDark,
              isDark && activeTab === 'contact' && styles.tabTextActiveDark
            ]}>
              {t('support.tabs.contact')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'form' && styles.tabActive]}
            onPress={() => setActiveTab('form')}
          >
            <Text style={[
              styles.tabText,
              activeTab === 'form' && styles.tabTextActive,
              isDark && styles.tabTextDark,
              isDark && activeTab === 'form' && styles.tabTextActiveDark
            ]}>
              {t('support.tabs.form')}
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.tab, activeTab === 'faq' && styles.tabActive]}
            onPress={() => setActiveTab('faq')}
          >
            <Text style={[
              styles.tabText,
              activeTab === 'faq' && styles.tabTextActive,
              isDark && styles.tabTextDark,
              isDark && activeTab === 'faq' && styles.tabTextActiveDark
            ]}>
              {t('support.tabs.faq')}
            </Text>
          </TouchableOpacity>
        </View>

        {activeTab === 'contact' && renderContact()}
        {activeTab === 'form' && renderContactForm()}
        {activeTab === 'faq' && renderFAQ()}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
  },
  textDark: {
    color: '#ffffff',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  button: {
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {
    color: '#007AFF',
  },
  placeholder: {
    width: 60,
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#f8f9fa',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tabBarDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  tab: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
  },
  tabActive: {
    borderBottomWidth: 2,
    borderBottomColor: '#007AFF',
  },
  tabText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#666666',
  },
  tabTextActive: {
    color: '#007AFF',
  },
  tabTextDark: {
    color: '#8e8e93',
  },
  tabTextActiveDark: {
    color: '#007AFF',
  },
  tabContent: {
    flex: 1,
    padding: 20,
  },
  // Form styles
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  labelDark: {
    color: '#ffffff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
  },
  inputDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
    color: '#ffffff',
  },
  inputError: {
    borderColor: '#ff3b30',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
    height: 120,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#ff3b30',
    fontSize: 12,
    marginTop: 4,
  },
  categoryScroll: {
    marginTop: 8,
  },
  categoryChip: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  categoryChipDark: {
    backgroundColor: '#2c2c2e',
  },
  categoryChipSelected: {
    backgroundColor: '#007AFF',
  },
  categoryChipSelectedDark: {
    backgroundColor: '#0A84FF',
  },
  categoryChipText: {
    fontSize: 14,
    color: '#000000',
  },
  categoryChipTextDark: {
    color: '#ffffff',
  },
  categoryChipTextSelected: {
    color: '#ffffff',
  },
  categoryChipTextSelectedDark: {
    color: '#ffffff',
  },
  submitButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    paddingVertical: 16,
    alignItems: 'center',
    marginTop: 20,
  },
  submitButtonDark: {
    backgroundColor: '#0A84FF',
  },
  submitButtonDisabled: {
    opacity: 0.5,
  },
  submitButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  submitButtonTextDisabled: {
    opacity: 0.7,
  },
  // FAQ styles
  faqItem: {
    marginBottom: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  faqQuestion: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  faqAnswer: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  // Contact styles
  contactSection: {
    marginBottom: 24,
  },
  contactTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  contactText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  contactMethod: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
  },
  contactMethodDark: {
    backgroundColor: '#1c1c1e',
  },
  contactIcon: {
    fontSize: 24,
    marginRight: 12,
  },
  contactInfo: {
    flex: 1,
  },
  contactMethodTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 2,
  },
  contactMethodText: {
    fontSize: 14,
    color: '#666666',
  },
  contactArrow: {
    fontSize: 18,
    color: '#666666',
  },
  bottomPadding: {
    height: 50,
  },
});

export default ContactSupportModal;

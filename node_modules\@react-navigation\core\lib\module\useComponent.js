"use strict";

import * as React from 'react';
import { jsx as _jsx } from "react/jsx-runtime";
const NavigationContent = ({
  render,
  children
}) => {
  return render(children);
};
export function useComponent(render) {
  const renderRef = React.useRef(render);

  // Normally refs shouldn't be mutated in render
  // But we return a component which will be rendered
  // So it's just for immediate consumption
  renderRef.current = render;
  React.useEffect(() => {
    renderRef.current = null;
  });
  return React.useRef(({
    children
  }) => {
    const render = renderRef.current;
    if (render === null) {
      throw new Error('The returned component must be rendered in the same render phase as the hook.');
    }
    return /*#__PURE__*/_jsx(NavigationContent, {
      render: render,
      children: children
    });
  }).current;
}
//# sourceMappingURL=useComponent.js.map
{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "_colors", "_getContrastingColor", "_interopRequireDefault", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "defaultSize", "Badge", "children", "size", "style", "theme", "themeOverrides", "visible", "rest", "_theme$colors", "useInternalTheme", "current", "opacity", "useRef", "Animated", "Value", "fontScale", "useWindowDimensions", "isFirstRendering", "animation", "scale", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "backgroundColor", "isV3", "colors", "error", "notification", "restStyle", "StyleSheet", "flatten", "textColor", "onError", "getContrastingColor", "white", "black", "borderRadius", "paddingHorizontal", "createElement", "Text", "numberOfLines", "color", "fontSize", "fonts", "regular", "lineHeight", "height", "min<PERSON><PERSON><PERSON>", "styles", "container", "_default", "exports", "create", "alignSelf", "textAlign", "textAlignVertical", "overflow"], "sourceRoot": "../../../src", "sources": ["components/Badge.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAQA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAEA,IAAAI,oBAAA,GAAAC,sBAAA,CAAAL,OAAA;AAA+D,SAAAK,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAO,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAE/D,MAAMG,WAAW,GAAG,EAAE;AAuBtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAGA,CAAC;EACbC,QAAQ;EACRC,IAAI,GAAGH,WAAW;EAClBI,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrBC,OAAO,GAAG,IAAI;EACd,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMJ,KAAK,GAAG,IAAAK,yBAAgB,EAACJ,cAAc,CAAC;EAC9C,MAAM;IAAEK,OAAO,EAAEC;EAAQ,CAAC,GAAG7C,KAAK,CAAC8C,MAAM,CACvC,IAAIC,qBAAQ,CAACC,KAAK,CAACR,OAAO,GAAG,CAAC,GAAG,CAAC,CACpC,CAAC;EACD,MAAM;IAAES;EAAU,CAAC,GAAG,IAAAC,gCAAmB,EAAC,CAAC;EAE3C,MAAMC,gBAAgB,GAAGnD,KAAK,CAAC8C,MAAM,CAAU,IAAI,CAAC;EAEpD,MAAM;IACJM,SAAS,EAAE;MAAEC;IAAM;EACrB,CAAC,GAAGf,KAAK;EAETtC,KAAK,CAACsD,SAAS,CAAC,MAAM;IACpB;IACA,IAAIH,gBAAgB,CAACP,OAAO,EAAE;MAC5BO,gBAAgB,CAACP,OAAO,GAAG,KAAK;MAChC;IACF;IAEAG,qBAAQ,CAACQ,MAAM,CAACV,OAAO,EAAE;MACvBW,OAAO,EAAEhB,OAAO,GAAG,CAAC,GAAG,CAAC;MACxBiB,QAAQ,EAAE,GAAG,GAAGJ,KAAK;MACrBK,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACnB,OAAO,EAAEK,OAAO,EAAEQ,KAAK,CAAC,CAAC;EAE7B,MAAM;IACJO,eAAe,GAAGtB,KAAK,CAACuB,IAAI,GACxBvB,KAAK,CAACwB,MAAM,CAACC,KAAK,IAAArB,aAAA,GAClBJ,KAAK,CAACwB,MAAM,cAAApB,aAAA,uBAAZA,aAAA,CAAcsB,YAAY;IAC9B,GAAGC;EACL,CAAC,GAAIC,uBAAU,CAACC,OAAO,CAAC9B,KAAK,CAAC,IAAI,CAAC,CAAe;EAElD,MAAM+B,SAAS,GAAG9B,KAAK,CAACuB,IAAI,GACxBvB,KAAK,CAACwB,MAAM,CAACO,OAAO,GACpB,IAAAC,4BAAmB,EAACV,eAAe,EAAEW,aAAK,EAAEC,aAAK,CAAC;EAEtD,MAAMC,YAAY,GAAGrC,IAAI,GAAG,CAAC;EAE7B,MAAMsC,iBAAiB,GAAGpC,KAAK,CAACuB,IAAI,GAAG,CAAC,GAAG,CAAC;EAE5C,oBACE7D,KAAA,CAAA2E,aAAA,CAACxE,YAAA,CAAA4C,QAAQ,CAAC6B,IAAI,EAAAjD,QAAA;IACZkD,aAAa,EAAE,CAAE;IACjBxC,KAAK,EAAE,CACL;MACEQ,OAAO;MACPe,eAAe;MACfkB,KAAK,EAAEV,SAAS;MAChBW,QAAQ,EAAE3C,IAAI,GAAG,GAAG;MACpB,IAAI,CAACE,KAAK,CAACuB,IAAI,IAAIvB,KAAK,CAAC0C,KAAK,CAACC,OAAO,CAAC;MACvCC,UAAU,EAAE9C,IAAI,GAAGa,SAAS;MAC5BkC,MAAM,EAAE/C,IAAI;MACZgD,QAAQ,EAAEhD,IAAI;MACdqC,YAAY;MACZC;IACF,CAAC,EACDW,MAAM,CAACC,SAAS,EAChBrB,SAAS;EACT,GACExB,IAAI,GAEPN,QACY,CAAC;AAEpB,CAAC;AAAC,IAAAoD,QAAA,GAAAC,OAAA,CAAA9E,OAAA,GAEawB,KAAK;AAEpB,MAAMmD,MAAM,GAAGnB,uBAAU,CAACuB,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE,QAAQ;IAC3BC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}
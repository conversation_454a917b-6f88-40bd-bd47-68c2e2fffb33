{"version": 3, "names": ["splitStyles", "styles", "filters", "process", "env", "NODE_ENV", "length", "console", "error", "newStyles", "map", "rest", "outer", "item", "Object", "entries", "i", "push", "unshift", "fromEntries"], "sourceRoot": "../../../src", "sources": ["utils/splitStyles.ts"], "mappings": ";;;;;;AAWA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,WAAWA,CACzBC,MAAiB,EACjB,GAAGC,OAAc,EACjB;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,IAAIH,OAAO,CAACI,MAAM,KAAK,CAAC,EAAE;IACjEC,OAAO,CAACC,KAAK,CAAC,iDAAiD,CAAC;EAClE;;EAEA;EACA;EACA,MAAMC,SAAS,GAAGP,OAAO,CAACQ,GAAG,CAAC,MAAM,EAAa,CAAC;;EAElD;EACA,MAAMC,IAAa,GAAG,EAAE;;EAExB;EACAC,KAAK,EAAE,KAAK,MAAMC,IAAI,IAAIC,MAAM,CAACC,OAAO,CAACd,MAAM,CAAC,EAAa;IAC3D;IACA,KAAK,IAAIe,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,OAAO,CAACI,MAAM,EAAEU,CAAC,EAAE,EAAE;MACvC;MACA,IAAId,OAAO,CAACc,CAAC,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QACvBJ,SAAS,CAACO,CAAC,CAAC,CAACC,IAAI,CAACJ,IAAI,CAAC,CAAC,CAAC;QACzB,SAASD,KAAK,CAAC,CAAC;MAClB;IACF;;IAEA;IACAD,IAAI,CAACM,IAAI,CAACJ,IAAI,CAAC;EACjB;;EAEA;EACAJ,SAAS,CAACS,OAAO,CAACP,IAAI,CAAC;;EAEvB;EACA,OAAOF,SAAS,CAACC,GAAG,CAAET,MAAM,IAAKa,MAAM,CAACK,WAAW,CAAClB,MAAM,CAAC,CAAC;AAI9D", "ignoreList": []}
import React, { createContext, useContext, useEffect, useState } from 'react';
// AsyncStorage removed for Expo Go compatibility

const AuthContext = createContext({});

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [session, setSession] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing session in AsyncStorage
    checkExistingSession();
  }, []);

  const checkExistingSession = async () => {
    try {
      // In production, this would check AsyncStorage
      // For Expo Go compatibility, we'll start with no user
      console.log('Session check completed (no persistent storage)');
    } catch (error) {
      console.error('Error checking existing session:', error);
    } finally {
      setLoading(false);
    }
  };

  const signUp = async (email, password, userData = {}) => {
    try {
      // Mock sign up for Expo Go compatibility
      const mockUser = {
        id: Date.now().toString(),
        email: email,
        user_metadata: {
          username: userData.username || email.split('@')[0],
          full_name: userData.full_name || '',
          level: 1,
          experience_points: 0,
        },
        created_at: new Date().toISOString(),
      };

      // In production, this would store user in AsyncStorage
      setUser(mockUser);
      setSession({ user: mockUser });

      return { data: { user: mockUser }, error: null };
    } catch (error) {
      return { data: null, error };
    }
  };

  const signIn = async (email, password) => {
    try {
      // Mock sign in for Expo Go compatibility
      const mockUser = {
        id: Date.now().toString(),
        email: email,
        user_metadata: {
          username: email.split('@')[0],
          full_name: 'Demo Gebruiker',
          level: 3,
          experience_points: 250,
        },
        created_at: new Date().toISOString(),
      };

      // In production, this would store user in AsyncStorage
      setUser(mockUser);
      setSession({ user: mockUser });

      return { data: { user: mockUser }, error: null };
    } catch (error) {
      return { data: null, error };
    }
  };

  const signOut = async () => {
    try {
      // In production, this would clear user from AsyncStorage
      setUser(null);
      setSession(null);
      return { error: null };
    } catch (error) {
      return { error };
    }
  };

  const resetPassword = async (email) => {
    try {
      // Mock password reset for Expo Go compatibility
      return { data: { message: 'Password reset email sent' }, error: null };
    } catch (error) {
      return { data: null, error };
    }
  };

  const updateProfile = async (updates) => {
    try {
      // Mock profile update for Expo Go compatibility
      const updatedUser = {
        ...user,
        user_metadata: {
          ...user.user_metadata,
          ...updates,
        },
      };

      // In production, this would store updated user in AsyncStorage
      setUser(updatedUser);
      setSession({ user: updatedUser });

      return { data: updatedUser, error: null };
    } catch (error) {
      return { data: null, error };
    }
  };

  const value = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updateProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

{"version": 3, "names": ["React", "NavigationBuilderContext", "useIsomorphicLayoutEffect", "useScheduleUpdate", "callback", "scheduleUpdate", "flushUpdates", "useContext"], "sourceRoot": "../../src", "sources": ["useScheduleUpdate.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,wBAAwB,QAAQ,+BAA4B;AACrE,SAASC,yBAAyB,QAAQ,6BAA6B;;AAEvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,iBAAiBA,CAACC,QAAoB,EAAE;EACtD,MAAM;IAAEC,cAAc;IAAEC;EAAa,CAAC,GAAGN,KAAK,CAACO,UAAU,CACvDN,wBACF,CAAC;;EAED;EACA;EACAI,cAAc,CAACD,QAAQ,CAAC;EAExBF,yBAAyB,CAACI,YAAY,CAAC;AACzC", "ignoreList": []}
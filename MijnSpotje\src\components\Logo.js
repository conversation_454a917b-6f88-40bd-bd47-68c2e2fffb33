import React from 'react';
import { View, Text, StyleSheet } from 'react-native';

// Main Logo Component with iPhone-compatible design
const Logo = ({
  size = 120,
  showText = true,
  isDark = false,
  animated = false
}) => {
  const iconSize = size * 0.6;
  const textSize = size * 0.25;

  return (
    <View style={[styles.container, { width: showText ? size * 2 : size }]}>
      {/* Logo Icon - Simple emoji-based design for iPhone compatibility */}
      <View style={[
        styles.iconContainer,
        {
          width: iconSize,
          height: iconSize,
          backgroundColor: isDark ? '#FF6B35' : '#FF6B35',
          borderRadius: iconSize / 2,
        }
      ]}>
        <View style={[styles.iconBackground, isDark && styles.iconBackgroundDark]}>
          <Text style={[styles.mainIcon, { fontSize: iconSize * 0.5 }]}>📍</Text>
          <View style={styles.accentDots}>
            <Text style={[styles.accentDot, { fontSize: iconSize * 0.15 }]}>📍</Text>
            <Text style={[styles.accentDot, { fontSize: iconSize * 0.12 }]}>⭐</Text>
            <Text style={[styles.accentDot, { fontSize: iconSize * 0.1 }]}>📍</Text>
          </View>
        </View>
      </View>

      {/* Logo Text */}
      {showText && (
        <View style={styles.textContainer}>
          <Text style={[
            styles.logoText,
            { fontSize: textSize },
            isDark && styles.logoTextDark
          ]}>
            🗺️ Mijn Spotje
          </Text>
          <Text style={[
            styles.logoSubtext,
            { fontSize: textSize * 0.5 },
            isDark && styles.logoSubtextDark
          ]}>
            Ontdek & Deel
          </Text>
        </View>
      )}
    </View>
  );
};

// Icon-only version for app icon and small spaces
export const LogoIcon = ({ size = 60, isDark = false }) => (
  <Logo size={size} showText={false} isDark={isDark} />
);

// Animated version for splash screen
export const AnimatedLogo = ({ size = 150, isDark = false, showText = true }) => {
  return (
    <View style={styles.animatedContainer}>
      <Logo size={size} showText={showText} isDark={isDark} animated={true} />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
    shadowColor: '#FF6B35',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 6,
  },
  iconBackground: {
    width: '100%',
    height: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    position: 'relative',
  },
  iconBackgroundDark: {
    // Dark mode specific styles if needed
  },
  mainIcon: {
    textAlign: 'center',
    color: '#FFFFFF',
  },
  accentDots: {
    position: 'absolute',
    width: '100%',
    height: '100%',
  },
  accentDot: {
    position: 'absolute',
    opacity: 0.6,
  },
  textContainer: {
    alignItems: 'center',
  },
  logoText: {
    fontWeight: 'bold',
    color: '#2C3E50',
    textAlign: 'center',
    letterSpacing: -0.5,
  },
  logoTextDark: {
    color: '#FFFFFF',
  },
  logoSubtext: {
    color: '#7F8C8D',
    textAlign: 'center',
    marginTop: 4,
    fontWeight: '500',
  },
  logoSubtextDark: {
    color: '#8E8E93',
  },
  animatedContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
});

export default Logo;

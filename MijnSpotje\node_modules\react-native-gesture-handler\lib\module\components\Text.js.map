{"version": 3, "sources": ["Text.tsx"], "names": ["React", "forwardRef", "useEffect", "useRef", "Platform", "Text", "RNText", "GestureObjects", "Gesture", "GestureDetector", "props", "ref", "onPress", "onLongPress", "rest", "textRef", "native", "Native", "runOnJS", "ref<PERSON><PERSON><PERSON>", "node", "current", "rngh", "OS", "textElement", "setAttribute"], "mappings": ";;AAAA,OAAOA,KAAP,IAEEC,UAFF,EAIEC,SAJF,EAKEC,MALF,QAMO,OANP;AAOA,SACEC,QADF,EAEEC,IAAI,IAAIC,MAFV,QAIO,cAJP;AAMA,SAASC,cAAc,IAAIC,OAA3B,QAA0C,qCAA1C;AACA,SAASC,eAAT,QAAgC,sCAAhC;AAEA,OAAO,MAAMJ,IAAI,gBAAGJ,UAAU,CAC5B,CAACS,KAAD,EAAqBC,GAArB,KAAmD;AACjD,QAAM;AAAEC,IAAAA,OAAF;AAAWC,IAAAA,WAAX;AAAwB,OAAGC;AAA3B,MAAoCJ,KAA1C;AAEA,QAAMK,OAAO,GAAGZ,MAAM,CAAgB,IAAhB,CAAtB;AACA,QAAMa,MAAM,GAAGR,OAAO,CAACS,MAAR,GAAiBC,OAAjB,CAAyB,IAAzB,CAAf;;AAEA,QAAMC,UAAU,GAAIC,IAAD,IAAe;AAChCL,IAAAA,OAAO,CAACM,OAAR,GAAkBD,IAAlB;;AAEA,QAAIT,GAAG,KAAK,IAAZ,EAAkB;AAChB;AACD;;AAED,QAAI,OAAOA,GAAP,KAAe,UAAnB,EAA+B;AAC7BA,MAAAA,GAAG,CAACS,IAAD,CAAH;AACD,KAFD,MAEO;AACLT,MAAAA,GAAG,CAACU,OAAJ,GAAcD,IAAd;AACD;AACF,GAZD,CANiD,CAoBjD;AACA;AACA;;;AACAD,EAAAA,UAAU,CAACG,IAAX,GAAkB,IAAlB;AAEApB,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIE,QAAQ,CAACmB,EAAT,KAAgB,KAApB,EAA2B;AACzB;AACD;;AAED,UAAMC,WAAW,GAAGb,GAAG,GAClBA,GAAD,CAA2BU,OADR,GAEnBN,OAAO,CAACM,OAFZ,CALc,CASd;;AACCG,IAAAA,WAAD,aAACA,WAAD,uBAACA,WAAD,CAA4CC,YAA5C,CACE,UADF,EAEE,MAFF;AAID,GAdQ,EAcN,EAdM,CAAT;AAgBA,SAAOb,OAAO,IAAIC,WAAX,gBACL,oBAAC,eAAD;AAAiB,IAAA,OAAO,EAAEG;AAA1B,kBACE,oBAAC,MAAD;AACE,IAAA,OAAO,EAAEJ,OADX;AAEE,IAAA,WAAW,EAAEC,WAFf;AAGE,IAAA,GAAG,EAAEM;AAHP,KAIML,IAJN,EADF,CADK,gBAUL,oBAAC,MAAD;AAAQ,IAAA,GAAG,EAAEH;AAAb,KAAsBG,IAAtB,EAVF;AAYD,CAtD2B,CAAvB,C,CAwDP", "sourcesContent": ["import React, {\n  ForwardedRef,\n  forwardRef,\n  RefObject,\n  useEffect,\n  useRef,\n} from 'react';\nimport {\n  Platform,\n  Text as RNText,\n  TextProps as RNTextProps,\n} from 'react-native';\n\nimport { GestureObjects as Gesture } from '../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../handlers/gestures/GestureDetector';\n\nexport const Text = forwardRef(\n  (props: RNTextProps, ref: ForwardedRef<RNText>) => {\n    const { onPress, onLongPress, ...rest } = props;\n\n    const textRef = useRef<RNText | null>(null);\n    const native = Gesture.Native().runOnJS(true);\n\n    const refHandler = (node: any) => {\n      textRef.current = node;\n\n      if (ref === null) {\n        return;\n      }\n\n      if (typeof ref === 'function') {\n        ref(node);\n      } else {\n        ref.current = node;\n      }\n    };\n\n    // This is a special case for `Text` component. After https://github.com/software-mansion/react-native-gesture-handler/pull/3379 we check for\n    // `displayName` field. However, `Text` from RN has this field set to `Text`, but is also present in `RNSVGElements` set.\n    // We don't want to treat our `Text` as the one from `SVG`, therefore we add special field to ref.\n    refHandler.rngh = true;\n\n    useEffect(() => {\n      if (Platform.OS !== 'web') {\n        return;\n      }\n\n      const textElement = ref\n        ? (ref as RefObject<RNText>).current\n        : textRef.current;\n\n      // At this point we are sure that textElement is div in HTML tree\n      (textElement as unknown as HTMLDivElement)?.setAttribute(\n        'rnghtext',\n        'true'\n      );\n    }, []);\n\n    return onPress || onLongPress ? (\n      <GestureDetector gesture={native}>\n        <RNText\n          onPress={onPress}\n          onLongPress={onLongPress}\n          ref={refHandler}\n          {...rest}\n        />\n      </GestureDetector>\n    ) : (\n      <RNText ref={ref} {...rest} />\n    );\n  }\n);\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type Text = typeof Text & RNText;\n"]}
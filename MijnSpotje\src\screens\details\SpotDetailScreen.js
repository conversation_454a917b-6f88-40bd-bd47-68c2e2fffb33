import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  Dimensions,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { spotService } from '../../services/spotService';
import { theme } from '../../config/theme';

const { width } = Dimensions.get('window');

const SpotDetailScreen = ({ route, navigation }) => {
  const { spotId } = route.params;
  const [spot, setSpot] = useState(null);
  const [loading, setLoading] = useState(true);
  const [isFavorited, setIsFavorited] = useState(false);
  const [userRating, setUserRating] = useState(0);
  const { user } = useAuth();

  useEffect(() => {
    loadSpotDetails();
  }, [spotId]);

  const loadSpotDetails = async () => {
    try {
      // Using mock data for Expo Go compatibility
      const mockSpotDetails = {
        1: {
          id: 1,
          title: 'Vondelpark',
          description: 'Prachtig park in het centrum van Amsterdam met veel groen en vijvers. Perfect voor een wandeling of picknick. Het park heeft verschillende ingangen en is het hele jaar door toegankelijk. Er zijn speeltuinen voor kinderen, vijvers met eenden en veel bankjes om te ontspannen.',
          latitude: 52.3579,
          longitude: 4.8686,
          averageRating: 4.5,
          ratingCount: 23,
          category: 'park',
          users: { username: 'amsterdam_lover', level: 5 },
          images: [],
          ratings: [
            { rating: 5, user_id: 'user1', users: { username: 'nature_lover' } },
            { rating: 4, user_id: 'user2', users: { username: 'walker123' } }
          ],
          comments: [
            {
              id: 1,
              comment: 'Geweldige plek om te ontspannen! Vooral in de lente is het hier prachtig.',
              created_at: '2024-01-20T10:00:00Z',
              users: { username: 'nature_lover' }
            },
            {
              id: 2,
              comment: 'Perfect voor een picknick met vrienden. Veel ruimte en altijd gezellig.',
              created_at: '2024-01-18T15:30:00Z',
              users: { username: 'walker123' }
            }
          ]
        },
        2: {
          id: 2,
          title: 'Anne Frank Huis',
          description: 'Historisch museum en monument waar Anne Frank ondergedoken zat tijdens de Tweede Wereldoorlog. Een indrukwekkende ervaring die je niet mag missen.',
          latitude: 52.3752,
          longitude: 4.8840,
          averageRating: 4.8,
          ratingCount: 156,
          category: 'museum',
          users: { username: 'history_buff', level: 8 },
          images: [],
          ratings: [
            { rating: 5, user_id: 'user3', users: { username: 'history_fan' } }
          ],
          comments: [
            {
              id: 3,
              comment: 'Zeer indrukwekkend en emotioneel. Een must-see in Amsterdam.',
              created_at: '2024-01-22T14:00:00Z',
              users: { username: 'history_fan' }
            }
          ]
        }
      };

      const spotData = mockSpotDetails[spotId] || mockSpotDetails[1];
      setSpot(spotData);

      // Check if user has rated this spot (mock check)
      const userRatingData = spotData.ratings.find(r => r.user_id === user?.id);
      if (userRatingData) {
        setUserRating(userRatingData.rating);
      }
    } catch (error) {
      console.error('Error loading spot details:', error);
      Alert.alert('Fout', 'Er ging iets mis bij het laden van de spot');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const handleRating = async (rating) => {
    try {
      // Mock rating functionality for Expo Go compatibility
      setUserRating(rating);
      Alert.alert('Rating opgeslagen!', `Je hebt deze spot ${rating} ster${rating !== 1 ? 'ren' : ''} gegeven.`);
    } catch (error) {
      console.error('Error rating spot:', error);
      Alert.alert('Fout', 'Er ging iets mis bij het beoordelen');
    }
  };

  const handleToggleFavorite = async () => {
    try {
      // Mock favorite functionality for Expo Go compatibility
      const newFavoriteStatus = !isFavorited;
      setIsFavorited(newFavoriteStatus);
      Alert.alert(
        newFavoriteStatus ? 'Toegevoegd aan favorieten!' : 'Verwijderd uit favorieten',
        newFavoriteStatus ? 'Deze spot is toegevoegd aan je favorieten.' : 'Deze spot is verwijderd uit je favorieten.'
      );
    } catch (error) {
      console.error('Error toggling favorite:', error);
      Alert.alert('Fout', 'Er ging iets mis bij het wijzigen van favoriet');
    }
  };

  const renderRatingStars = (rating, onPress = null) => {
    const stars = [];
    for (let i = 1; i <= 5; i++) {
      stars.push(
        <TouchableOpacity
          key={i}
          onPress={() => onPress && onPress(i)}
          disabled={!onPress}
          style={styles.starButton}
        >
          <Ionicons
            name={i <= rating ? "star" : "star-outline"}
            size={24}
            color="#F39C12"
          />
        </TouchableOpacity>
      );
    }
    return <View style={styles.starsContainer}>{stars}</View>;
  };

  if (loading || !spot) {
    return (
      <View style={styles.loadingContainer}>
        <Text>Laden...</Text>
      </View>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Image Gallery */}
      {spot.images && spot.images.length > 0 && (
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          style={styles.imageGallery}
        >
          {spot.images.map((image, index) => (
            <Image
              key={index}
              source={{ uri: image.image_url }}
              style={styles.spotImage}
              resizeMode="cover"
            />
          ))}
        </ScrollView>
      )}

      {/* Spot Info */}
      <View style={styles.contentContainer}>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.spotTitle}>{spot.title}</Text>
            <TouchableOpacity onPress={handleToggleFavorite}>
              <Ionicons
                name={isFavorited ? "heart" : "heart-outline"}
                size={24}
                color={isFavorited ? theme.colors.error : theme.colors.textSecondary}
              />
            </TouchableOpacity>
          </View>
          
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={16} color="#F39C12" />
            <Text style={styles.ratingText}>
              {spot.averageRating > 0 ? spot.averageRating.toFixed(1) : 'Geen rating'}
            </Text>
            <Text style={styles.ratingCount}>
              ({spot.ratingCount} reviews)
            </Text>
          </View>
        </View>

        {/* Description */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Beschrijving</Text>
          <Text style={styles.description}>{spot.description}</Text>
        </View>

        {/* Category & Author */}
        <View style={styles.metaContainer}>
          {spot.category && (
            <View style={styles.categoryContainer}>
              <Text style={styles.categoryLabel}>Categorie:</Text>
              <View style={styles.categoryBadge}>
                <Text style={styles.categoryText}>{spot.category}</Text>
              </View>
            </View>
          )}
          
          <View style={styles.authorContainer}>
            <Text style={styles.authorLabel}>Toegevoegd door:</Text>
            <Text style={styles.authorName}>{spot.users?.username || 'Onbekend'}</Text>
          </View>
        </View>

        {/* User Rating */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Jouw beoordeling</Text>
          {renderRatingStars(userRating, handleRating)}
          {userRating > 0 && (
            <Text style={styles.userRatingText}>
              Je hebt deze spot {userRating} ster{userRating !== 1 ? 'ren' : ''} gegeven
            </Text>
          )}
        </View>

        {/* Comments Section */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Reviews ({spot.comments?.length || 0})
          </Text>
          
          {spot.comments && spot.comments.length > 0 ? (
            spot.comments.map((comment) => (
              <View key={comment.id} style={styles.commentCard}>
                <View style={styles.commentHeader}>
                  <Text style={styles.commentAuthor}>
                    {comment.users?.username || 'Onbekend'}
                  </Text>
                  <Text style={styles.commentDate}>
                    {new Date(comment.created_at).toLocaleDateString('nl-NL')}
                  </Text>
                </View>
                <Text style={styles.commentText}>{comment.comment}</Text>
              </View>
            ))
          ) : (
            <Text style={styles.noComments}>Nog geen reviews</Text>
          )}
        </View>

        {/* Location Info */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Locatie</Text>
          <Text style={styles.coordinates}>
            {spot.latitude.toFixed(6)}, {spot.longitude.toFixed(6)}
          </Text>
          <TouchableOpacity
            style={styles.mapButton}
            onPress={() => {
              // TODO: Open in maps app or navigate to map view
              Alert.alert('Binnenkort beschikbaar', 'Navigatie functie wordt binnenkort toegevoegd');
            }}
          >
            <Ionicons name="map-outline" size={20} color="#fff" />
            <Text style={styles.mapButtonText}>Bekijk op kaart</Text>
          </TouchableOpacity>
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  imageGallery: {
    height: 250,
  },
  spotImage: {
    width: width,
    height: 250,
  },
  contentContainer: {
    padding: theme.spacing.lg,
  },
  header: {
    marginBottom: theme.spacing.lg,
  },
  titleContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  spotTitle: {
    flex: 1,
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginRight: theme.spacing.md,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 16,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: theme.spacing.xs,
  },
  ratingCount: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  section: {
    marginBottom: theme.spacing.xl,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginBottom: theme.spacing.md,
  },
  description: {
    fontSize: 16,
    color: theme.colors.text,
    lineHeight: 24,
  },
  metaContainer: {
    marginBottom: theme.spacing.xl,
  },
  categoryContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: theme.spacing.md,
  },
  categoryLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: theme.spacing.sm,
  },
  categoryBadge: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  categoryText: {
    fontSize: 12,
    color: '#fff',
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  authorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  authorLabel: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginRight: theme.spacing.sm,
  },
  authorName: {
    fontSize: 14,
    color: theme.colors.text,
    fontWeight: '600',
  },
  starsContainer: {
    flexDirection: 'row',
    marginBottom: theme.spacing.sm,
  },
  starButton: {
    marginRight: theme.spacing.xs,
  },
  userRatingText: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
  },
  commentCard: {
    backgroundColor: theme.colors.surface,
    padding: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
    marginBottom: theme.spacing.md,
  },
  commentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  commentAuthor: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
  },
  commentDate: {
    fontSize: 12,
    color: theme.colors.textSecondary,
  },
  commentText: {
    fontSize: 14,
    color: theme.colors.text,
    lineHeight: 20,
  },
  noComments: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    fontStyle: 'italic',
    textAlign: 'center',
    paddingVertical: theme.spacing.lg,
  },
  coordinates: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    marginBottom: theme.spacing.md,
  },
  mapButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: theme.colors.primary,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  mapButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
    marginLeft: theme.spacing.sm,
  },
});

export default SpotDetailScreen;

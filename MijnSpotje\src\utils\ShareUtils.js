import { Share, Alert } from 'react-native';

class ShareUtils {
  // Format spot data for sharing
  formatSpotForSharing(spot) {
    const categoryEmojis = {
      restaurant: '🍽️',
      cafe: '☕',
      park: '🌳',
      museum: '🏛️',
      shop: '🛍️',
      bar: '🍺',
      attraction: '🎡',
      other: '📍',
    };

    const emoji = categoryEmojis[spot.category] || '📍';
    const stars = '⭐'.repeat(Math.floor(spot.rating));
    const ratingText = `${stars} ${spot.rating}/5`;

    const shareText = `${emoji} ${spot.title}

${spot.description}

📍 Locatie: ${spot.location}
${ratingText}
🏷️ Categorie: ${spot.category}

Gedeeld via Mijn Spotje 📱`;

    return {
      title: `${emoji} ${spot.title} - Mijn Spotje`,
      message: shareText,
      url: undefined, // Could add deep link in future
    };
  }

  // Share a spot using native share functionality
  async shareSpot(spot) {
    try {
      console.log('📤 Sharing spot:', spot.title);
      
      const shareContent = this.formatSpotForSharing(spot);
      
      const result = await Share.share({
        title: shareContent.title,
        message: shareContent.message,
      }, {
        dialogTitle: `Deel ${spot.title}`,
        subject: shareContent.title, // For email sharing
      });

      if (result.action === Share.sharedAction) {
        if (result.activityType) {
          console.log('✅ Spot shared via:', result.activityType);
          return { success: true, activityType: result.activityType };
        } else {
          console.log('✅ Spot shared successfully');
          return { success: true };
        }
      } else if (result.action === Share.dismissedAction) {
        console.log('❌ Share dialog dismissed');
        return { success: false, dismissed: true };
      }
    } catch (error) {
      console.error('❌ Error sharing spot:', error);
      Alert.alert(
        'Delen Mislukt',
        'Er is een fout opgetreden bij het delen van deze spot. Probeer het opnieuw.',
        [{ text: 'OK' }]
      );
      return { success: false, error: error.message };
    }
  }

  // Share multiple spots (for future use)
  async shareMultipleSpots(spots) {
    try {
      if (!spots || spots.length === 0) {
        Alert.alert('Geen Spots', 'Er zijn geen spots om te delen.', [{ text: 'OK' }]);
        return { success: false };
      }

      const spotsText = spots.map((spot, index) => {
        const emoji = this.getCategoryEmoji(spot.category);
        return `${index + 1}. ${emoji} ${spot.title}\n   📍 ${spot.location}\n   ⭐ ${spot.rating}/5`;
      }).join('\n\n');

      const shareText = `Mijn favoriete spots 🗺️

${spotsText}

Gedeeld via Mijn Spotje 📱`;

      const result = await Share.share({
        title: 'Mijn Favoriete Spots - Mijn Spotje',
        message: shareText,
      }, {
        dialogTitle: `Deel ${spots.length} spots`,
        subject: 'Mijn Favoriete Spots - Mijn Spotje',
      });

      return { success: result.action === Share.sharedAction };
    } catch (error) {
      console.error('❌ Error sharing multiple spots:', error);
      Alert.alert(
        'Delen Mislukt',
        'Er is een fout opgetreden bij het delen van de spots.',
        [{ text: 'OK' }]
      );
      return { success: false, error: error.message };
    }
  }

  // Get category emoji helper
  getCategoryEmoji(category) {
    const emojis = {
      restaurant: '🍽️',
      cafe: '☕',
      park: '🌳',
      museum: '🏛️',
      shop: '🛍️',
      bar: '🍺',
      attraction: '🎡',
      other: '📍',
    };
    return emojis[category] || '📍';
  }

  // Check if sharing is available (always true on modern devices)
  isShareAvailable() {
    return true; // Share API is available on all React Native platforms
  }

  // Share app itself (for future marketing)
  async shareApp() {
    try {
      const shareText = `Ontdek geweldige plekken met Mijn Spotje! 🗺️

Een handige app om je favoriete locaties op te slaan en te delen.

📍 Bewaar je favoriete spots
🗺️ Bekijk ze op de kaart  
📤 Deel ze met vrienden
📸 Voeg foto's toe

Download Mijn Spotje nu! 📱`;

      const result = await Share.share({
        title: 'Mijn Spotje - Ontdek Geweldige Plekken',
        message: shareText,
      }, {
        dialogTitle: 'Deel Mijn Spotje',
        subject: 'Mijn Spotje - Ontdek Geweldige Plekken',
      });

      return { success: result.action === Share.sharedAction };
    } catch (error) {
      console.error('❌ Error sharing app:', error);
      return { success: false, error: error.message };
    }
  }

  // Generate shareable text for a spot (without triggering share dialog)
  generateShareText(spot) {
    const shareContent = this.formatSpotForSharing(spot);
    return shareContent.message;
  }

  // Copy spot details to clipboard (for future use)
  async copySpotToClipboard(spot) {
    try {
      const shareText = this.generateShareText(spot);
      // Note: Clipboard functionality would require expo-clipboard
      // For now, we'll just return the text
      console.log('📋 Spot text ready for clipboard:', shareText);
      return { success: true, text: shareText };
    } catch (error) {
      console.error('❌ Error preparing clipboard text:', error);
      return { success: false, error: error.message };
    }
  }
}

// Export singleton instance
export default new ShareUtils();

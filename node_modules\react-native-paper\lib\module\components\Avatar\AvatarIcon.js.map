{"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "white", "getContrastingColor", "Icon", "defaultSize", "Avatar", "icon", "size", "style", "theme", "themeOverrides", "rest", "_theme$colors", "backgroundColor", "colors", "primary", "restStyle", "flatten", "textColor", "color", "createElement", "_extends", "width", "height", "borderRadius", "styles", "container", "source", "displayName", "create", "justifyContent", "alignItems"], "sourceRoot": "../../../../src", "sources": ["components/Avatar/AvatarIcon.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAoBC,UAAU,EAAEC,IAAI,QAAmB,cAAc;AAErE,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,KAAK,QAAQ,+BAA+B;AAErD,OAAOC,mBAAmB,MAAM,iCAAiC;AACjE,OAAOC,IAAI,MAAsB,SAAS;AAE1C,MAAMC,WAAW,GAAG,EAAE;AAsBtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAGA,CAAC;EACdC,IAAI;EACJC,IAAI,GAAGH,WAAW;EAClBI,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMH,KAAK,GAAGT,gBAAgB,CAACU,cAAc,CAAC;EAC9C,MAAM;IAAEG,eAAe,IAAAD,aAAA,GAAGH,KAAK,CAACK,MAAM,cAAAF,aAAA,uBAAZA,aAAA,CAAcG,OAAO;IAAE,GAAGC;EAAU,CAAC,GAC7DlB,UAAU,CAACmB,OAAO,CAACT,KAAK,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMU,SAAS,GACbP,IAAI,CAACQ,KAAK,IACVjB,mBAAmB,CAACW,eAAe,EAAEZ,KAAK,EAAE,oBAAoB,CAAC;EAEnE,oBACEJ,KAAA,CAAAuB,aAAA,CAACrB,IAAI,EAAAsB,QAAA;IACHb,KAAK,EAAE,CACL;MACEc,KAAK,EAAEf,IAAI;MACXgB,MAAM,EAAEhB,IAAI;MACZiB,YAAY,EAAEjB,IAAI,GAAG,CAAC;MACtBM;IACF,CAAC,EACDY,MAAM,CAACC,SAAS,EAChBV,SAAS;EACT,GACEL,IAAI,gBAERd,KAAA,CAAAuB,aAAA,CAACjB,IAAI;IAACwB,MAAM,EAAErB,IAAK;IAACa,KAAK,EAAED,SAAU;IAACX,IAAI,EAAEA,IAAI,GAAG;EAAI,CAAE,CACrD,CAAC;AAEX,CAAC;AAEDF,MAAM,CAACuB,WAAW,GAAG,aAAa;AAElC,MAAMH,MAAM,GAAG3B,UAAU,CAAC+B,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAe1B,MAAM", "ignoreList": []}
{"version": 3, "file": "ExpoLocalization.native.js", "sourceRoot": "", "sources": ["../src/ExpoLocalization.native.ts"], "names": [], "mappings": "AAAA,OAAO,EAA0B,mBAAmB,EAAE,MAAM,mBAAmB,CAAC;AAEhF,MAAM,sBAAsB,GAAG,mBAAmB,CAAC,kBAAkB,CAAC,CAAC;AAEvE,MAAM,UAAU,iBAAiB;AAC/B,+CAA+C;AAC/C,QAAmC;IAEnC,OAAO,sBAAsB,CAAC,WAAW,CAAC,yBAAyB,EAAE,QAAQ,CAAC,CAAC;AACjF,CAAC;AAED,MAAM,UAAU,mBAAmB;AACjC,+CAA+C;AAC/C,QAAmC;IAEnC,OAAO,sBAAsB,CAAC,WAAW,CAAC,2BAA2B,EAAE,QAAQ,CAAC,CAAC;AACnF,CAAC;AAED,MAAM,UAAU,kBAAkB,CAAC,YAA+B;IAChE,YAAY,CAAC,MAAM,EAAE,CAAC;AACxB,CAAC;AAED,eAAe,sBAAsB,CAAC", "sourcesContent": ["import { type EventSubscription, requireNativeModule } from 'expo-modules-core';\n\nconst ExpoLocalizationModule = requireNativeModule('ExpoLocalization');\n\nexport function addLocaleListener(\n  // NOTE(@kitten): We never use the event's data\n  listener: (event?: unknown) => void\n): EventSubscription {\n  return ExpoLocalizationModule.addListener('onLocaleSettingsChanged', listener);\n}\n\nexport function addCalendarListener(\n  // NOTE(@kitten): We never use the event's data\n  listener: (event?: unknown) => void\n): EventSubscription {\n  return ExpoLocalizationModule.addListener('onCalendarSettingsChanged', listener);\n}\n\nexport function removeSubscription(subscription: EventSubscription) {\n  subscription.remove();\n}\n\nexport default ExpoLocalizationModule;\n"]}
{"version": 3, "file": "Localization.js", "sourceRoot": "", "sources": ["../src/Localization.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,OAAO,CAAC;AAEvD,OAAO,gBAAgB,EAAE,EACvB,mBAAmB,EACnB,iBAAiB,EACjB,kBAAkB,GACnB,MAAM,oBAAoB,CAAC;AAE5B,cAAc,sBAAsB,CAAC;AAErC,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AAElD,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC;AAElE,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,sBAAsB,GAAG,gBAAgB,CAAC,sBAAsB,CAAC;AAE9E,cAAc;AACd;;;;GAIG;AACH,MAAM,CAAC,MAAM,gBAAgB,GAAG,gBAAgB,CAAC,gBAAgB,CAAC;AAElE,cAAc;AACd;;;;;GAKG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AAElD,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,KAAK,GAAG,gBAAgB,CAAC,KAAK,CAAC;AAE5C,cAAc;AACd;;;;;;;GAOG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;AAE9C,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC;AAEhD,cAAc;AACd;;;;;;;;;;GAUG;AACH,MAAM,CAAC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,QAAQ,CAAC;AAElD,cAAc;AACd;;;;;;;;GAQG;AACH,MAAM,CAAC,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC;AAE9C;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,CAAC,MAAM,UAAU,GAAG,gBAAgB,CAAC,UAAU,CAAC;AAEtD;;;;;;;;;;;;;GAaG;AACH,MAAM,CAAC,MAAM,YAAY,GAAG,gBAAgB,CAAC,YAAY,CAAC;AAE1D;;;;;;;;;;;;;;;;;;;;;;GAsBG;AACH,MAAM,UAAU,UAAU;IACxB,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,MAAM,OAAO,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,UAAU,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACnD,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,iBAAiB,CAAC,UAAU,CAAC,CAAC;QACnD,OAAO,GAAG,EAAE;YACV,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,OAAO,OAAO,CAAC;AACjB,CAAC;AAED;;;;;;;;;;;;;;GAcG;AACH,MAAM,UAAU,YAAY;IAC1B,MAAM,CAAC,GAAG,EAAE,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,MAAM,SAAS,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC,YAAY,EAAE,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC;IACvD,SAAS,CAAC,GAAG,EAAE;QACb,MAAM,YAAY,GAAG,mBAAmB,CAAC,UAAU,CAAC,CAAC;QACrD,OAAO,GAAG,EAAE;YACV,kBAAkB,CAAC,YAAY,CAAC,CAAC;QACnC,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC,CAAC;IACP,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,cAAc;AACd;;;;;;;;;;;;;;;GAeG;AACH,MAAM,CAAC,KAAK,UAAU,oBAAoB;IACxC,OAAO,MAAM,gBAAgB,CAAC,oBAAoB,EAAE,CAAC;AACvD,CAAC", "sourcesContent": ["import { useEffect, useReducer, useMemo } from 'react';\n\nimport ExpoLocalization, {\n  addCalendarListener,\n  addLocaleListener,\n  removeSubscription,\n} from './ExpoLocalization';\nimport { Localization } from './Localization.types';\nexport * from './Localization.types';\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getLocales() instead.\n * Three-character ISO 4217 currency code. Returns `null` on web.\n *\n * @example\n * `'USD'`, `'EUR'`, `'CNY'`, `null`\n */\nexport const currency = ExpoLocalization.currency;\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getLocales() instead.\n * Decimal separator used for formatting numbers.\n *\n * @example\n * `','`, `'.'`\n */\nexport const decimalSeparator = ExpoLocalization.decimalSeparator;\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getLocales() instead.\n * Digit grouping separator used when formatting numbers larger than 1000.\n *\n * @example\n * `'.'`, `''`, `','`\n */\nexport const digitGroupingSeparator = ExpoLocalization.digitGroupingSeparator;\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getLocales() instead.\n * A list of all the supported language ISO codes.\n */\nexport const isoCurrencyCodes = ExpoLocalization.isoCurrencyCodes;\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getLocales() instead.\n * Boolean value that indicates whether the system uses the metric system.\n * On Android and web, this is inferred from the current region.\n */\nexport const isMetric = ExpoLocalization.isMetric;\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getLocales() instead.\n * Returns if the system's language is written from Right-to-Left.\n * This can be used to build features like [bidirectional icons](https://material.io/design/usability/bidirectionality.html).\n *\n * Returns `false` in Server Side Rendering (SSR) environments.\n */\nexport const isRTL = ExpoLocalization.isRTL;\n\n// @needsAudit\n/**\n * @deprecated Use [`Localization.getLocales()`](#localizationgetlocales) instead.\n * An [IETF BCP 47 language tag](https://en.wikipedia.org/wiki/IETF_language_tag),\n * consisting of a two-character language code and optional script, region and variant codes.\n *\n * @example\n * `'en'`, `'en-US'`, `'zh-Hans'`, `'zh-Hans-CN'`, `'en-emodeng'`\n */\nexport const locale = ExpoLocalization.locale;\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getLocales() instead.\n * List of all the native languages provided by the user settings.\n * These are returned in the order the user defines in their device settings.\n *\n * @example\n * `['en', 'en-US', 'zh-Hans', 'zh-Hans-CN', 'en-emodeng']`\n */\nexport const locales = ExpoLocalization.locales;\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getCalendars() instead.\n * The current time zone in display format.\n * On Web time zone is calculated with Intl.DateTimeFormat().resolvedOptions().timeZone. For a\n * better estimation you could use the moment-timezone package but it will add significant bloat to\n * your website's bundle size.\n *\n * @example\n * `'America/Los_Angeles'`\n */\nexport const timezone = ExpoLocalization.timezone;\n\n// @needsAudit\n/**\n * @hidden\n * @deprecated Use Localization.getLocales() instead.\n * The region code for your device that comes from the Region setting under Language & Region on iOS.\n * This value is always available on iOS, but might return `null` on Android or web.\n *\n * @example\n * `'US'`, `'NZ'`, `null`\n */\nexport const region = ExpoLocalization.region;\n\n/**\n * List of user's locales, returned as an array of objects of type `Locale`.\n * Guaranteed to contain at least 1 element.\n * These are returned in the order the user defines in their device settings.\n * On the web currency and measurements systems are not provided, instead returned as null.\n * If needed, you can infer them from the current region using a lookup table.\n * @example\n * ```js\n * [{\n *   \"languageTag\": \"pl-PL\",\n *   \"languageCode\": \"pl\",\n *   \"textDirection\": \"ltr\",\n *   \"digitGroupingSeparator\": \" \",\n *   \"decimalSeparator\": \",\",\n *   \"measurementSystem\": \"metric\",\n *   \"currencyCode\": \"PLN\",\n *   \"currencySymbol\": \"zł\",\n *   \"regionCode\": \"PL\",\n *   \"temperatureUnit\": \"celsius\"\n * }]\n * ```\n */\nexport const getLocales = ExpoLocalization.getLocales;\n\n/**\n * List of user's preferred calendars, returned as an array of objects of type `Calendar`.\n * Guaranteed to contain at least 1 element.\n * For now always returns a single element, but it's likely to return a user preference list on some platforms in the future.\n * @example\n * ```js\n * [{\n *   \"calendar\": \"gregory\",\n *   \"timeZone\": \"Europe/Warsaw\",\n *   \"uses24hourClock\": true,\n *   \"firstWeekday\": 1\n * }]\n * ```\n */\nexport const getCalendars = ExpoLocalization.getCalendars;\n\n/**\n * A hook providing a list of user's locales, returned as an array of objects of type `Locale`.\n * Guaranteed to contain at least 1 element.\n * These are returned in the order the user defines in their device settings.\n * On the web currency and measurements systems are not provided, instead returned as null.\n * If needed, you can infer them from the current region using a lookup table.\n * If the OS settings change, the hook will rerender with a new list of locales.\n * @example\n * ```js\n * [{\n *   \"languageTag\": \"pl-PL\",\n *   \"languageCode\": \"pl\",\n *   \"textDirection\": \"ltr\",\n *   \"digitGroupingSeparator\": \" \",\n *   \"decimalSeparator\": \",\",\n *   \"measurementSystem\": \"metric\",\n *   \"currencyCode\": \"PLN\",\n *   \"currencySymbol\": \"zł\",\n *   \"regionCode\": \"PL\",\n *   \"temperatureUnit\": \"celsius\"\n * }]\n * ```\n */\nexport function useLocales() {\n  const [key, invalidate] = useReducer((k) => k + 1, 0);\n  const locales = useMemo(() => getLocales(), [key]);\n  useEffect(() => {\n    const subscription = addLocaleListener(invalidate);\n    return () => {\n      removeSubscription(subscription);\n    };\n  }, []);\n  return locales;\n}\n\n/**\n * A hook providing a list of user's preferred calendars, returned as an array of objects of type `Calendar`.\n * Guaranteed to contain at least 1 element.\n * For now always returns a single element, but it's likely to return a user preference list on some platforms in the future.\n * If the OS settings change, the hook will rerender with a new list of calendars.\n * @example\n * ```js\n * [{\n *   \"calendar\": \"gregory\",\n *   \"timeZone\": \"Europe/Warsaw\",\n *   \"uses24hourClock\": true,\n *   \"firstWeekday\": 1\n * }]\n * ```\n */\nexport function useCalendars() {\n  const [key, invalidate] = useReducer((k) => k + 1, 0);\n  const calendars = useMemo(() => getCalendars(), [key]);\n  useEffect(() => {\n    const subscription = addCalendarListener(invalidate);\n    return () => {\n      removeSubscription(subscription);\n    };\n  }, []);\n  return calendars;\n}\n\n// @needsAudit\n/**\n * @hidden\n * Get the latest native values from the device. Locale can be changed on some Android devices\n * without resetting the app.\n * > On iOS, changing the locale will cause the device to reset meaning the constants will always be\n * correct.\n *\n * @example\n * ```ts\n * // When the app returns from the background on Android...\n *\n * const { locale } = await Localization.getLocalizationAsync();\n * ```\n * @deprecated\n * Use Localization.getLocales() or Localization.getCalendars() instead.\n */\nexport async function getLocalizationAsync(): Promise<Localization> {\n  return await ExpoLocalization.getLocalizationAsync();\n}\n"]}
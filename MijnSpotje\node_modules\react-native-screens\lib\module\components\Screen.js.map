{"version": 3, "names": ["_extends", "Object", "assign", "bind", "n", "e", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "React", "Animated", "Platform", "TransitionProgressContext", "DelayedFreeze", "freezeEnabled", "isNativePlatformSupported", "screensEnabled", "ScreenNativeComponent", "ModalScreenNativeComponent", "usePrevious", "EDGE_TO_EDGE", "transformEdgeToEdgeProps", "SHEET_DIMMED_ALWAYS", "resolveSheetAllowedDetents", "resolveSheetInitialDetentIndex", "resolveSheetLargestUndimmedDetent", "AnimatedNativeScreen", "createAnimatedComponent", "AnimatedNativeModalScreen", "InnerScreen", "forwardRef", "props", "ref", "innerRef", "useRef", "useImperativeHandle", "current", "prevActivityState", "activityState", "setRef", "onComponentRef", "closing", "Value", "progress", "goingForward", "enabled", "freezeOnBlur", "shouldFreeze", "rest", "sheetAllowedDetents", "sheetLargestUndimmedDetentIndex", "sheetGrabberVisible", "sheetCornerRadius", "sheetExpandsWhenScrolledToEdge", "sheetElevation", "sheetInitialDetentIndex", "stackPresentation", "onAppear", "onDisappear", "onWillAppear", "onWillDisappear", "resolvedSheetAllowedDetents", "resolvedSheetLargestUndimmedDetent", "resolvedSheetInitialDetentIndex", "shouldUseModalScreenComponent", "select", "ios", "undefined", "android", "default", "AnimatedScreen", "active", "children", "isNativeStack", "gestureResponseDistance", "onGestureCancel", "style", "console", "warn", "Error", "handleRef", "viewConfig", "validAttributes", "display", "_viewConfig", "freeze", "createElement", "zIndex", "sheetLargestUndimmedDetent", "sheetInitialDetent", "start", "end", "top", "bottom", "onTransitionProgress", "event", "nativeEvent", "useNativeDriver", "Provider", "value", "View", "ScreenContext", "createContext", "Screen", "ScreenWrapper", "useContext", "displayName"], "sourceRoot": "../../../src", "sources": ["components/Screen.tsx"], "mappings": "AAAA,YAAY;;AAAC,SAAAA,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAC,CAAA,MAAAA,CAAA,GAAAC,SAAA,CAAAC,MAAA,EAAAF,CAAA,UAAAG,CAAA,GAAAF,SAAA,CAAAD,CAAA,YAAAI,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAL,CAAA,CAAAK,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAL,CAAA,KAAAJ,QAAA,CAAAY,KAAA,OAAAN,SAAA;AAEb,OAAOO,KAAK,MAAM,OAAO;AACzB,SAASC,QAAQ,EAAQC,QAAQ,QAAQ,cAAc;AAEvD,OAAOC,yBAAyB,MAAM,8BAA8B;AACpE,OAAOC,aAAa,MAAM,yBAAyB;AAGnD,SACEC,aAAa,EACbC,yBAAyB,EACzBC,cAAc,QACT,SAAS;;AAEhB;AACA,OAAOC,qBAAqB,MAErB,iCAAiC;AACxC,OAAOC,0BAA0B,MAE1B,sCAAsC;AAE7C,SAASC,WAAW,QAAQ,uBAAuB;AACnD,SAASC,YAAY,EAAEC,wBAAwB,QAAQ,wBAAwB;AAC/E,SACEC,mBAAmB,EACnBC,0BAA0B,EAC1BC,8BAA8B,EAC9BC,iCAAiC,QAC5B,iBAAiB;AAGxB,MAAMC,oBAAoB,GAAGhB,QAAQ,CAACiB,uBAAuB,CAC3DV,qBACF,CAAC;AACD,MAAMW,yBAAyB,GAAGlB,QAAQ,CAACiB,uBAAuB,CAChET,0BACF,CAAC;;AAED;AACA;;AAkBA,OAAO,MAAMW,WAAW,gBAAGpB,KAAK,CAACqB,UAAU,CACzC,SAASD,WAAWA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC/B,MAAMC,QAAQ,GAAGxB,KAAK,CAACyB,MAAM,CAAoB,IAAI,CAAC;EACtDzB,KAAK,CAAC0B,mBAAmB,CAACH,GAAG,EAAE,MAAMC,QAAQ,CAACG,OAAQ,EAAE,EAAE,CAAC;EAC3D,MAAMC,iBAAiB,GAAGlB,WAAW,CAACY,KAAK,CAACO,aAAa,CAAC;EAE1D,MAAMC,MAAM,GAAIP,GAAe,IAAK;IAClCC,QAAQ,CAACG,OAAO,GAAGJ,GAAG;IACtBD,KAAK,CAACS,cAAc,GAAGR,GAAG,CAAC;EAC7B,CAAC;EAED,MAAMS,OAAO,GAAGhC,KAAK,CAACyB,MAAM,CAAC,IAAIxB,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAC3D,MAAMO,QAAQ,GAAGlC,KAAK,CAACyB,MAAM,CAAC,IAAIxB,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAC5D,MAAMQ,YAAY,GAAGnC,KAAK,CAACyB,MAAM,CAAC,IAAIxB,QAAQ,CAACgC,KAAK,CAAC,CAAC,CAAC,CAAC,CAACN,OAAO;EAEhE,MAAM;IACJS,OAAO,GAAG7B,cAAc,CAAC,CAAC;IAC1B8B,YAAY,GAAGhC,aAAa,CAAC,CAAC;IAC9BiC,YAAY;IACZ,GAAGC;EACL,CAAC,GAAGjB,KAAK;;EAET;EACA;EACA,MAAM;IACJ;IACAkB,mBAAmB,GAAG,CAAC,GAAG,CAAC;IAC3BC,+BAA+B,GAAG5B,mBAAmB;IACrD6B,mBAAmB,GAAG,KAAK;IAC3BC,iBAAiB,GAAG,CAAC,GAAG;IACxBC,8BAA8B,GAAG,IAAI;IACrCC,cAAc,GAAG,EAAE;IACnBC,uBAAuB,GAAG,CAAC;IAC3B;IACAC,iBAAiB;IACjB;IACAC,QAAQ;IACRC,WAAW;IACXC,YAAY;IACZC;EACF,CAAC,GAAGZ,IAAI;EAER,IAAIH,OAAO,IAAI9B,yBAAyB,EAAE;IACxC,MAAM8C,2BAA2B,GAC/BtC,0BAA0B,CAAC0B,mBAAmB,CAAC;IACjD,MAAMa,kCAAkC,GACtCrC,iCAAiC,CAC/ByB,+BAA+B,EAC/BW,2BAA2B,CAAC1D,MAAM,GAAG,CACvC,CAAC;IACH,MAAM4D,+BAA+B,GAAGvC,8BAA8B,CACpE+B,uBAAuB,EACvBM,2BAA2B,CAAC1D,MAAM,GAAG,CACvC,CAAC;;IAED;IACA;IACA,MAAM6D,6BAA6B,GAAGrD,QAAQ,CAACsD,MAAM,CAAC;MACpDC,GAAG,EAAE,EACHV,iBAAiB,KAAKW,SAAS,IAC/BX,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,gBAAgB,IACtCA,iBAAiB,KAAK,2BAA2B,CAClD;MACDY,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,MAAMC,cAAc,GAAGN,6BAA6B,GAChDpC,yBAAyB,GACzBF,oBAAoB;IAExB,IAAI;MACF;MACA;MACA;MACA6C,MAAM;MACNjC,aAAa;MACbkC,QAAQ;MACRC,aAAa;MACbC,uBAAuB;MACvBC,eAAe;MACfC,KAAK;MACL,GAAG7C;IACL,CAAC,GAAGiB,IAAI;IAER,IAAIuB,MAAM,KAAKJ,SAAS,IAAI7B,aAAa,KAAK6B,SAAS,EAAE;MACvDU,OAAO,CAACC,IAAI,CACV,+QACF,CAAC;MACDxC,aAAa,GAAGiC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IACxC;IAEA,IACEE,aAAa,IACbpC,iBAAiB,KAAK8B,SAAS,IAC/B7B,aAAa,KAAK6B,SAAS,EAC3B;MACA,IAAI9B,iBAAiB,GAAGC,aAAa,EAAE;QACrC,MAAM,IAAIyC,KAAK,CACb,8DACF,CAAC;MACH;IACF;IAEA,MAAMC,SAAS,GAAIhD,GAAe,IAAK;MACrC;MACA;MACA,IAAIA,GAAG,EAAEiD,UAAU,EAAEC,eAAe,EAAEN,KAAK,EAAE;QAC3C5C,GAAG,CAACiD,UAAU,CAACC,eAAe,CAACN,KAAK,GAAG;UACrC,GAAG5C,GAAG,CAACiD,UAAU,CAACC,eAAe,CAACN,KAAK;UACvCO,OAAO,EAAE;QACX,CAAC;QACD5C,MAAM,CAACP,GAAG,CAAC;MACb,CAAC,MAAM,IAAIA,GAAG,EAAEoD,WAAW,EAAEF,eAAe,EAAEN,KAAK,EAAE;QACnD5C,GAAG,CAACoD,WAAW,CAACF,eAAe,CAACN,KAAK,GAAG;UACtC,GAAG5C,GAAG,CAACoD,WAAW,CAACF,eAAe,CAACN,KAAK;UACxCO,OAAO,EAAE;QACX,CAAC;QACD5C,MAAM,CAACP,GAAG,CAAC;MACb;IACF,CAAC;IAED,MAAMqD,MAAM,GACVvC,YAAY,KACXC,YAAY,KAAKoB,SAAS,GAAGpB,YAAY,GAAGT,aAAa,KAAK,CAAC,CAAC;IAEnE,oBACE7B,KAAA,CAAA6E,aAAA,CAACzE,aAAa;MAACwE,MAAM,EAAEA;IAAO,gBAC5B5E,KAAA,CAAA6E,aAAA,CAAChB,cAAc,EAAA1E,QAAA,KACTmC,KAAK;MACT;AACZ;AACA;AACA;AACA;MACY0B,QAAQ,EAAEA,QAAoC;MAC9CC,WAAW,EAAEA,WAA0C;MACvDC,YAAY,EAAEA,YAA4C;MAC1DC,eAAe,EAAEA,eAAkD;MACnEe,eAAe,EACZA,eAAe,KACf,MAAM;QACL;MAAA,CACD;MAEH;MACA;MACA;MACA;MACA;MAAA;MACAC,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEW,MAAM,EAAEpB;MAAU,CAAC,CAAE;MACtC7B,aAAa,EAAEA,aAAc;MAC7BW,mBAAmB,EAAEY,2BAA4B;MACjD2B,0BAA0B,EAAE1B,kCAAmC;MAC/DR,cAAc,EAAEA,cAAe;MAC/BH,mBAAmB,EAAEA,mBAAoB;MACzCC,iBAAiB,EAAEA,iBAAkB;MACrCC,8BAA8B,EAAEA,8BAA+B;MAC/DoC,kBAAkB,EAAE1B,+BAAgC;MACpDW,uBAAuB,EAAE;QACvBgB,KAAK,EAAEhB,uBAAuB,EAAEgB,KAAK,IAAI,CAAC,CAAC;QAC3CC,GAAG,EAAEjB,uBAAuB,EAAEiB,GAAG,IAAI,CAAC,CAAC;QACvCC,GAAG,EAAElB,uBAAuB,EAAEkB,GAAG,IAAI,CAAC,CAAC;QACvCC,MAAM,EAAEnB,uBAAuB,EAAEmB,MAAM,IAAI,CAAC;MAC9C;MACA;MACA;MAAA;MACA7D,GAAG,EAAEgD,SAAU;MACfc,oBAAoB,EAClB,CAACrB,aAAa,GACVN,SAAS,GACTzD,QAAQ,CAACqF,KAAK,CACZ,CACE;QACEC,WAAW,EAAE;UACXrD,QAAQ;UACRF,OAAO;UACPG;QACF;MACF,CAAC,CACF,EACD;QAAEqD,eAAe,EAAE;MAAK,CAC1B;IACL,IACA,CAACxB,aAAa;IAAK;IAClBD,QAAQ,gBAER/D,KAAA,CAAA6E,aAAA,CAAC1E,yBAAyB,CAACsF,QAAQ;MACjCC,KAAK,EAAE;QACLxD,QAAQ;QACRF,OAAO;QACPG;MACF;IAAE,GACD4B,QACiC,CAExB,CACH,CAAC;EAEpB,CAAC,MAAM;IACL;IACA,IAAI;MACFD,MAAM;MACNjC,aAAa;MACbsC,KAAK;MACL;MACApC,cAAc;MACd,GAAGT;IACL,CAAC,GAAGiB,IAAI;IAER,IAAIuB,MAAM,KAAKJ,SAAS,IAAI7B,aAAa,KAAK6B,SAAS,EAAE;MACvD7B,aAAa,GAAGiC,MAAM,KAAK,CAAC,GAAG,CAAC,GAAG,CAAC;IACtC;IACA,oBACE9D,KAAA,CAAA6E,aAAA,CAAC5E,QAAQ,CAAC0F,IAAI,EAAAxG,QAAA;MACZgF,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEO,OAAO,EAAE7C,aAAa,KAAK,CAAC,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACnEN,GAAG,EAAEO;IAAO,GACRR,KAAK,CACV,CAAC;EAEN;AACF,CACF,CAAC;;AAED;AACA;AACA,OAAO,MAAMsE,aAAa,gBAAG5F,KAAK,CAAC6F,aAAa,CAACzE,WAAW,CAAC;AAE7D,MAAM0E,MAAM,gBAAG9F,KAAK,CAACqB,UAAU,CAAoB,CAACC,KAAK,EAAEC,GAAG,KAAK;EACjE,MAAMwE,aAAa,GAAG/F,KAAK,CAACgG,UAAU,CAACJ,aAAa,CAAC,IAAIxE,WAAW;EAEpE,oBACEpB,KAAA,CAAA6E,aAAA,CAACkB,aAAa,EAAA5G,QAAA,KACPwB,YAAY,GAAGC,wBAAwB,CAACU,KAAK,CAAC,GAAGA,KAAK;IAC3DC,GAAG,EAAEA;EAAI,EACV,CAAC;AAEN,CAAC,CAAC;AAEFuE,MAAM,CAACG,WAAW,GAAG,QAAQ;AAE7B,eAAeH,MAAM", "ignoreList": []}
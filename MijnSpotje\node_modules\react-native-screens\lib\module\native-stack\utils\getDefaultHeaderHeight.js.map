{"version": 3, "names": ["Platform", "formSheetModalHeight", "getDefaultHeaderHeight", "layout", "statusBarHeight", "stackPresentation", "is<PERSON>arge<PERSON><PERSON>er", "headerHeight", "OS", "isLandscape", "width", "height", "isFormSheetModal", "isPad", "isTV"], "sourceRoot": "../../../../src", "sources": ["native-stack/utils/getDefaultHeaderHeight.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,cAAc;AAIvC,MAAMC,oBAAoB,GAAG,EAAE;AAE/B,eAAe,SAASC,sBAAsBA,CAC5CC,MAAc,EACdC,eAAuB,EACvBC,iBAAyC,EACzCC,aAAa,GAAG,KAAK,EACb;EACR;EACA,IAAIC,YAAY,GAAGP,QAAQ,CAACQ,EAAE,KAAK,SAAS,GAAG,EAAE,GAAG,EAAE;EAEtD,IAAIR,QAAQ,CAACQ,EAAE,KAAK,KAAK,EAAE;IACzB,MAAMC,WAAW,GAAGN,MAAM,CAACO,KAAK,GAAGP,MAAM,CAACQ,MAAM;IAChD,MAAMC,gBAAgB,GACpBP,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,WAAW,IACjCA,iBAAiB,KAAK,WAAW;IACnC,IAAIO,gBAAgB,IAAI,CAACH,WAAW,EAAE;MACpC;MACAL,eAAe,GAAG,CAAC;IACrB;IAEA,IAAIJ,QAAQ,CAACa,KAAK,IAAIb,QAAQ,CAACc,IAAI,EAAE;MACnCP,YAAY,GAAGK,gBAAgB,GAAGX,oBAAoB,GAAG,EAAE;IAC7D,CAAC,MAAM;MACL,IAAIQ,WAAW,EAAE;QACfF,YAAY,GAAG,EAAE;MACnB,CAAC,MAAM;QACL,IAAIK,gBAAgB,EAAE;UACpBL,YAAY,GAAGN,oBAAoB;QACrC,CAAC,MAAM;UACLM,YAAY,GAAGD,aAAa,GAAG,EAAE,GAAG,EAAE;QACxC;MACF;IACF;EACF;EAEA,OAAOC,YAAY,GAAGH,eAAe;AACvC", "ignoreList": []}
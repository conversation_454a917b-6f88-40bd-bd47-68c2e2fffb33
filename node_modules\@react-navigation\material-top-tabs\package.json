{"name": "@react-navigation/material-top-tabs", "description": "Integration for the animated tab view component from react-native-tab-view", "version": "7.2.14", "keywords": ["react-native-component", "react-component", "react-native", "react-navigation", "ios", "android", "material", "tab"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/material-top-tabs"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/material-top-tab-navigator/", "source": "./src/index.tsx", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/elements": "^2.4.3", "color": "^4.2.3", "react-native-tab-view": "^4.1.1"}, "devDependencies": {"@jest/globals": "^29.7.0", "@react-navigation/native": "^7.1.10", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-builder-bob": "^0.40.9", "react-native-pager-view": "6.7.1", "react-native-safe-area-context": "5.4.0", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"@react-navigation/native": "^7.1.10", "react": ">= 18.2.0", "react-native": "*", "react-native-pager-view": ">= 6.0.0", "react-native-safe-area-context": ">= 4.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "52e5ec64745b399655adf7bd9773e31372d51e62"}
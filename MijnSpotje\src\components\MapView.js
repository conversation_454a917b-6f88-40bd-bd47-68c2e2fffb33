import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Dimensions,
} from 'react-native';
import MapView, { Marker, Callout, PROVIDER_GOOGLE } from 'react-native-maps';
import LocationService from '../services/LocationService';
import PerformanceMonitor from '../utils/PerformanceMonitor';

const { width, height } = Dimensions.get('window');

// Default region (Amsterdam)
const DEFAULT_REGION = {
  latitude: 52.3676,
  longitude: 4.9041,
  latitudeDelta: 0.0922,
  longitudeDelta: 0.0421,
};

const CustomMapView = React.memo(({ spots, isDark, onSpotPress, onCreateSpot, t }) => {
  const [region, setRegion] = useState(DEFAULT_REGION);
  const [userLocation, setUserLocation] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isMapReady, setIsMapReady] = useState(false);
  const [shouldFollowUser, setShouldFollowUser] = useState(false);
  const mapRef = useRef(null);
  const regionChangeTimeoutRef = useRef(null);

  // Auto-center map on user location when loading
  useEffect(() => {
    const loadUserLocation = async () => {
      try {
        console.log('🗺️ Requesting user location for map centering...');
        const location = await LocationService.getCurrentLocation();

        if (location) {
          console.log('📍 User location found:', location.latitude, location.longitude);
          setUserLocation(location);

          // Always center on user location when map loads initially
          const newRegion = {
            latitude: location.latitude,
            longitude: location.longitude,
            latitudeDelta: 0.0922,
            longitudeDelta: 0.0421,
          };

          setRegion(newRegion);
          console.log('🎯 Map centered on user location');
        } else {
          console.log('❌ Could not get user location, using default (Amsterdam)');
          // Fall back to Amsterdam if location access denied
          setRegion(DEFAULT_REGION);
        }
      } catch (error) {
        console.error('❌ Error loading user location:', error);
        // Fall back to Amsterdam on error
        setRegion(DEFAULT_REGION);
      } finally {
        setIsLoading(false);
      }
    };

    loadUserLocation();
  }, []); // Only run once on mount

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (regionChangeTimeoutRef.current) {
        clearTimeout(regionChangeTimeoutRef.current);
      }
    };
  }, []);

  const handleMyLocation = useCallback(async () => {
    try {
      setShouldFollowUser(true);
      const location = await LocationService.getCurrentLocation();
      if (location && mapRef.current && isMapReady) {
        setUserLocation(location);
        const newRegion = {
          latitude: location.latitude,
          longitude: location.longitude,
          latitudeDelta: 0.01,
          longitudeDelta: 0.01,
        };
        setRegion(newRegion);
        mapRef.current.animateToRegion(newRegion, 1000);
      }
    } catch (error) {
      Alert.alert('Fout', 'Kon je locatie niet ophalen');
    } finally {
      setShouldFollowUser(false);
    }
  }, [isMapReady]);

  const handleMapPress = useCallback((event) => {
    const { coordinate } = event.nativeEvent;
    Alert.alert(
      'Nieuwe Spot',
      'Wil je een nieuwe spot toevoegen op deze locatie?',
      [
        { text: 'Annuleren', style: 'cancel' },
        {
          text: 'Toevoegen',
          onPress: () => onCreateSpot(coordinate)
        }
      ]
    );
  }, [onCreateSpot]);

  // Stable region change handler with debouncing
  const handleRegionChangeComplete = useCallback((newRegion) => {
    if (regionChangeTimeoutRef.current) {
      clearTimeout(regionChangeTimeoutRef.current);
    }

    regionChangeTimeoutRef.current = setTimeout(() => {
      if (!shouldFollowUser && isMapReady) {
        setRegion(newRegion);
      }
    }, 200); // Increased debounce for better performance
  }, [shouldFollowUser, isMapReady]);

  const handleMapReady = useCallback(() => {
    PerformanceMonitor.endTiming('MapView.load');
    setIsMapReady(true);

    // Animate to user location when map is ready
    if (userLocation && mapRef.current) {
      const userRegion = {
        latitude: userLocation.latitude,
        longitude: userLocation.longitude,
        latitudeDelta: 0.0922,
        longitudeDelta: 0.0421,
      };

      setTimeout(() => {
        PerformanceMonitor.startTiming('MapView.animateToRegion');
        // Use faster animation duration for better performance
        mapRef.current.animateToRegion(userRegion, 800);
        console.log('🎬 Animated to user location');
        setTimeout(() => {
          PerformanceMonitor.endTiming('MapView.animateToRegion');
        }, 800);
      }, 300); // Reduced delay for faster response
    }
  }, [userLocation]);

  // Memoized category helpers
  const getCategoryIcon = useCallback((category) => {
    const icons = {
      restaurant: '🍽️',
      cafe: '☕',
      park: '🌳',
      museum: '🏛️',
      shop: '🛍️',
      bar: '🍺',
      attraction: '🎡',
      other: '📍',
    };
    return icons[category] || '📍';
  }, []);

  const getCategoryColor = useCallback((category) => {
    const colors = {
      restaurant: '#FF6B35',
      cafe: '#8B4513',
      park: '#228B22',
      museum: '#4169E1',
      shop: '#FF1493',
      bar: '#FFD700',
      attraction: '#FF69B4',
      other: '#007AFF',
    };
    return colors[category] || '#007AFF';
  }, []);

  // Enhanced interactive spot markers with performance monitoring
  const spotMarkers = useMemo(() => {
    PerformanceMonitor.startTiming('MapView.renderMarkers');
    const validSpots = spots.filter(spot => spot.latitude && spot.longitude);
    console.log(`🗺️ Rendering ${validSpots.length} spot markers on map`);

    const markers = validSpots.map((spot) => (
        <Marker
          key={spot.id}
          coordinate={{
            latitude: spot.latitude,
            longitude: spot.longitude,
          }}
          onPress={() => {
            console.log(`📍 Spot marker pressed: ${spot.title}`);
            onSpotPress(spot);
          }}
          tracksViewChanges={false} // Important for performance
        >
          <View style={[
            styles.markerContainer,
            { backgroundColor: getCategoryColor(spot.category) },
            styles.interactiveMarker
          ]}>
            <Text style={styles.markerIcon}>{getCategoryIcon(spot.category)}</Text>
          </View>
          <Callout
            style={styles.callout}
            onPress={() => {
              console.log(`💬 Callout pressed: ${spot.title}`);
              onSpotPress(spot);
            }}
          >
            <View style={styles.calloutContent}>
              <Text style={styles.calloutTitle}>{spot.title}</Text>
              <Text style={styles.calloutCategory}>{spot.category}</Text>
              <View style={styles.calloutRating}>
                <Text style={styles.star}>⭐</Text>
                <Text style={styles.ratingText}>{spot.rating}</Text>
              </View>
              <Text style={styles.calloutTapHint}>Tik voor details →</Text>
            </View>
          </Callout>
        </Marker>
      ));

    PerformanceMonitor.endTiming('MapView.renderMarkers');
    return markers;
  }, [spots, getCategoryIcon, getCategoryColor, onSpotPress]);

  // Track component renders
  React.useEffect(() => {
    PerformanceMonitor.trackRender('MapView');
  });

  // Start map loading timer
  React.useEffect(() => {
    PerformanceMonitor.startTiming('MapView.load');
  }, []);

  if (isLoading) {
    return (
      <View style={[styles.container, styles.loadingContainer, isDark && styles.containerDark]}>
        <Text style={[styles.loadingText, isDark && styles.textDark]}>🗺️</Text>
        <Text style={[styles.loadingSubtext, isDark && styles.textSecondaryDark]}>
          Kaart laden...
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <MapView
        ref={mapRef}
        style={styles.map}
        provider={PROVIDER_GOOGLE}
        initialRegion={region}
        onRegionChangeComplete={handleRegionChangeComplete}
        onMapReady={handleMapReady}
        onPress={handleMapPress}
        showsUserLocation={true}
        showsMyLocationButton={false}
        showsCompass={true}
        showsScale={true}
        customMapStyle={isDark ? darkMapStyle : []}
        moveOnMarkerPress={false}
        pitchEnabled={true}
        rotateEnabled={true}
        scrollEnabled={true}
        zoomEnabled={true}
        loadingEnabled={true}
        loadingIndicatorColor="#007AFF"
        loadingBackgroundColor={isDark ? '#000000' : '#ffffff'}
      >
        {/* Render optimized spot markers */}
        {spotMarkers}

        {/* User location marker */}
        {userLocation && (
          <Marker
            coordinate={{
              latitude: userLocation.latitude,
              longitude: userLocation.longitude,
            }}
            title="Je locatie"
            description="Huidige positie"
            tracksViewChanges={false}
          >
            <View style={styles.userLocationMarker}>
              <View style={styles.userLocationDot} />
            </View>
          </Marker>
        )}
      </MapView>

      {/* Map Controls */}
      <View style={styles.controls}>
        <TouchableOpacity
          style={[styles.controlButton, isDark && styles.controlButtonDark]}
          onPress={handleMyLocation}
        >
          <Text style={styles.controlIcon}>📍</Text>
        </TouchableOpacity>
      </View>

      {/* Map Info */}
      <View style={[styles.mapInfo, isDark && styles.mapInfoDark]}>
        <Text style={[styles.mapInfoText, isDark && styles.textDark]}>
          {spots.filter(spot => spot.latitude && spot.longitude).length} spots op de kaart
        </Text>
        <Text style={[styles.mapInfoSubtext, isDark && styles.textSecondaryDark]}>
          Tik op de kaart om een nieuwe spot toe te voegen
        </Text>
      </View>
    </View>
  );
});

CustomMapView.displayName = 'CustomMapView';

// Dark mode map style
const darkMapStyle = [
  {
    elementType: 'geometry',
    stylers: [{ color: '#212121' }],
  },
  {
    elementType: 'labels.icon',
    stylers: [{ visibility: 'off' }],
  },
  {
    elementType: 'labels.text.fill',
    stylers: [{ color: '#757575' }],
  },
  {
    elementType: 'labels.text.stroke',
    stylers: [{ color: '#212121' }],
  },
  {
    featureType: 'administrative',
    elementType: 'geometry',
    stylers: [{ color: '#757575' }],
  },
  {
    featureType: 'administrative.country',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#9e9e9e' }],
  },
  {
    featureType: 'administrative.locality',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#bdbdbd' }],
  },
  {
    featureType: 'poi',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#757575' }],
  },
  {
    featureType: 'poi.park',
    elementType: 'geometry',
    stylers: [{ color: '#181818' }],
  },
  {
    featureType: 'poi.park',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#616161' }],
  },
  {
    featureType: 'poi.park',
    elementType: 'labels.text.stroke',
    stylers: [{ color: '#1b1b1b' }],
  },
  {
    featureType: 'road',
    elementType: 'geometry.fill',
    stylers: [{ color: '#2c2c2c' }],
  },
  {
    featureType: 'road',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#8a8a8a' }],
  },
  {
    featureType: 'road.arterial',
    elementType: 'geometry',
    stylers: [{ color: '#373737' }],
  },
  {
    featureType: 'road.highway',
    elementType: 'geometry',
    stylers: [{ color: '#3c3c3c' }],
  },
  {
    featureType: 'road.highway.controlled_access',
    elementType: 'geometry',
    stylers: [{ color: '#4e4e4e' }],
  },
  {
    featureType: 'road.local',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#616161' }],
  },
  {
    featureType: 'transit',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#757575' }],
  },
  {
    featureType: 'water',
    elementType: 'geometry',
    stylers: [{ color: '#000000' }],
  },
  {
    featureType: 'water',
    elementType: 'labels.text.fill',
    stylers: [{ color: '#3d3d3d' }],
  },
];

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 60,
    marginBottom: 16,
  },
  loadingSubtext: {
    fontSize: 16,
    color: '#666666',
  },
  textDark: {
    color: '#ffffff',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  map: {
    width: width,
    height: height,
  },
  markerContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#ffffff',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  interactiveMarker: {
    transform: [{ scale: 1.0 }],
    // Add subtle animation effect for interactivity
  },
  markerIcon: {
    fontSize: 20,
  },
  userLocationMarker: {
    width: 20,
    height: 20,
    borderRadius: 10,
    backgroundColor: 'rgba(0, 122, 255, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: '#007AFF',
  },
  userLocationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007AFF',
  },
  callout: {
    width: 200,
  },
  calloutContent: {
    padding: 8,
  },
  calloutTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  calloutCategory: {
    fontSize: 12,
    color: '#666666',
    textTransform: 'capitalize',
    marginBottom: 4,
  },
  calloutRating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  star: {
    fontSize: 12,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 12,
    color: '#000000',
    fontWeight: '500',
  },
  calloutTapHint: {
    fontSize: 10,
    color: '#007AFF',
    fontStyle: 'italic',
    marginTop: 4,
    textAlign: 'center',
  },
  controls: {
    position: 'absolute',
    top: 60,
    right: 20,
  },
  controlButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: '#ffffff',
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 8,
  },
  controlButtonDark: {
    backgroundColor: '#1c1c1e',
  },
  controlIcon: {
    fontSize: 20,
  },
  mapInfo: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: 12,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  mapInfoDark: {
    backgroundColor: 'rgba(28, 28, 30, 0.95)',
  },
  mapInfoText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 4,
  },
  mapInfoSubtext: {
    fontSize: 12,
    color: '#666666',
  },
});

export default CustomMapView;

export declare const byteToHex: string[];
export declare const hexToByte: {
    [key: string]: number;
};
export declare const DNS = "6ba7b810-9dad-11d1-80b4-00c04fd430c8";
export declare const URL = "6ba7b811-9dad-11d1-80b4-00c04fd430c8";
export declare const OID = "6ba7b812-9dad-11d1-80b4-00c04fd430c8";
export declare const X500 = "6ba7b814-9dad-11d1-80b4-00c04fd430c8";
export declare const NIL = "00000000-0000-0000-0000-000000000000";
export declare const stringToBytes: (str: string) => Uint8Array;
export declare const bytesToString: (buf: ArrayBuffer) => string;

# Installation
> `npm install --save @types/react-native`

# Summary
This package contains type definitions for react-native (https://github.com/facebook/react-native).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/react-native/v0.70.

### Additional Details
 * Last updated: Sun, 03 Dec 2023 20:07:23 GMT
 * Dependencies: [@types/react](https://npmjs.com/package/@types/react)

# Credits
These definitions were written by [<PERSON><PERSON>](https://github.com/alloy), [<PERSON><PERSON>uan<PERSON>](https://github.com/huhuanming), [<PERSON>](https://github.com/iRoachie), [<PERSON>](https://github.com/timwangdev), [<PERSON>](https://github.com/kamal), [<PERSON>](https://github.com/alexdunne), [<PERSON>](https://github.com/swissmanu), [<PERSON>](https://github.com/bm-software), [<PERSON>](https://github.com/mvdam), [<PERSON><PERSON><PERSON>](https://github.com/esem<PERSON>), [<PERSON>](https://github.com/mrnickel), [Souvik Ghosh](https://github.com/souvik-ghosh), [Cheng Gibson](https://github.com/nossbigg), [Saransh Kataria](https://github.com/saranshkataria), [Wojciech Tyczynski](https://github.com/tykus160), [Jake Bloom](https://github.com/jakebloom), [Ceyhun Ozugur](https://github.com/ceyhun), [Mike Martin](https://github.com/mcmar), [Theo Henry de Villeneuve](https://github.com/theohdv), [Romain Faust](https://github.com/romain-faust), [Be Birchall](https://github.com/bebebebebe), [Jesse Katsumata](https://github.com/Naturalclar), [Xianming Zhong](https://github.com/chinesedfan), [Valentyn Tolochko](https://github.com/vtolochk), [Sergey Sychev](https://github.com/SychevSP), [Daiki Ihara](https://github.com/sasurau4), [Abe Dolinger](https://github.com/256hz), [Dominique Richard](https://github.com/doumart), [Mohamed Shaban](https://github.com/drmas), [Jérémy Barbet](https://github.com/jeremybarbet), [David Sheldrick](https://github.com/ds300), [Natsathorn Yuthakovit](https://github.com/natsathorn), [ConnectDotz](https://github.com/connectdotz), [Alexey Molchan](https://github.com/alexeymolchan), [Alex Brazier](https://github.com/alexbrazier), [Arafat Zahan](https://github.com/kuasha420), [Pedro Hernández](https://github.com/phvillegas), [Sebastian Silbermann](https://github.com/eps1lon), [Zihan Chen](https://github.com/ZihanChen-MSFT), [Lorenzo Sciandra](https://github.com/kelset), [Mateusz Wit](https://github.com/MateWW), [Luna Wei](https://github.com/lunaleaps), and [Saad Najmi](https://github.com/saadnajmi).

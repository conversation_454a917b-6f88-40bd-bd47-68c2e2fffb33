{"dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/material-top-tabs": "^7.2.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@supabase/supabase-js": "^2.49.9", "expo-blur": "^14.1.4", "expo-constants": "^17.1.6", "expo-device": "^7.1.4", "expo-image-picker": "^16.1.4", "expo-location": "^18.1.5", "expo-notifications": "^0.31.2", "react-native-gesture-handler": "~2.24.0", "react-native-pager-view": "6.7.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-tab-view": "^4.1.1"}}
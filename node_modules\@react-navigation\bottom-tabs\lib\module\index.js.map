{"version": 3, "names": ["SceneStyleInterpolators", "TransitionPresets", "TransitionSpecs", "createBottomTabNavigator", "BottomTabBar", "BottomTabView", "BottomTabBarHeightCallbackContext", "BottomTabBarHeightContext", "useBottomTabBarHeight"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,uBAAuB,MAAM,gDAA6C;AACtF,OAAO,KAAKC,iBAAiB,MAAM,0CAAuC;AAC1E,OAAO,KAAKC,eAAe,MAAM,wCAAqC;;AAEtE;AACA;AACA;AACA,SAASF,uBAAuB,EAAEC,iBAAiB,EAAEC,eAAe;;AAEpE;AACA;AACA;AACA,SAASC,wBAAwB,QAAQ,0CAAuC;;AAEhF;AACA;AACA;AACA,SAASC,YAAY,QAAQ,yBAAsB;AACnD,SAASC,aAAa,QAAQ,0BAAuB;;AAErD;AACA;AACA;AACA,SAASC,iCAAiC,QAAQ,8CAA2C;AAC7F,SAASC,yBAAyB,QAAQ,sCAAmC;AAC7E,SAASC,qBAAqB,QAAQ,kCAA+B;;AAErE;AACA;AACA", "ignoreList": []}
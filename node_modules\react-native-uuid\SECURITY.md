# Security Policy

## Coordinated Vulnerability Disclosure Policy
To protect our users and reduce harm to the community, we ask security researchers to keep vulnerabilities and communications around vulnerability submissions private and confidential until a patch is developed. In addition to this, we ask that you:

- Allow us a reasonable amount of time to correct or address security vulnerabilities.
- Avoid exploiting any vulnerabilities that you discover.
- Demonstrate good faith by not disrupting or degrading react-native-uuid’s data or services.
- At present, we are most interested in bugs in our source code. Any bugs in services that we use (i.e. Github, Google Apps, or Netlify) should be reported directly to those services, and do not fall within the scope of our coordinated disclosure policy.

Please avoid filing security issues in our public repositories as this method of contact fully discloses security bugs to friends and adversaries alike, and makes it difficult for us to reduce harm for our users and community.

## Vulnerability Disclosure Process
Once we receive a vulnerability report, we will take these steps to address it:

- react-native-uuid will confirm receipt of the vulnerability report within 3 business days. The timing of our response may depend on when a report is submitted. Our daily operations are distributed in time zones across the globe, and response times may vary. If you have not received a response to a vulnerability report from us within 3 business days, we encourage you to follow up with us again for a response. We have a small team and we may not be able to meet the same response times of larger teams with more resources.
- react-native-uuid will investigate and validate the security issue submitted to us as quickly as we can, usually within 7 business days of receipt. Submitting a thorough report with clear steps to recreate the vulnerability and/or a proof-of-concept will move the process along in a timely manner.

If valid, react-native-uuid will acknowledge the bug, and make the necessary code changes to patch it. Some issues may require more time than others to patch, but we will strive to patch each vulnerability as quickly as our resources and development process allows.

react-native-uuid will publicly release the security patch for the vulnerability, and acknowledge the security fix in the release notes. Public release notes may reference the person or people who reported the vulnerability, unless the reporters choose to be anonymous.

## Supported Versions

We currently support only 2.0.x release.

| Version | Supported          |
| ------- | ------------------ |
| 2.0.x   | :white_check_mark: |
| < 2.0.0 | :x:                |

## Reporting a Vulnerability

Please [email us](mailto:<EMAIL>?subject=react-native-uuid%20security%20report) with your report.

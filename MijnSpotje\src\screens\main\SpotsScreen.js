import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Image,
  RefreshControl,
  TextInput,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useAuth } from '../../contexts/AuthContext';
import { spotService } from '../../services/spotService';
import { theme } from '../../config/theme';

const SpotsScreen = ({ navigation }) => {
  const [spots, setSpots] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredSpots, setFilteredSpots] = useState([]);
  const { user } = useAuth();

  useEffect(() => {
    loadSpots();
  }, []);

  useEffect(() => {
    filterSpots();
  }, [spots, searchQuery]);

  const loadSpots = async () => {
    try {
      // Using mock data for Expo Go compatibility
      const mockSpots = [
        {
          id: 1,
          title: 'Vondelpark',
          description: 'Prachtig park in het centrum van Amsterdam met veel groen en vijvers. Perfect voor een wandeling of picknick.',
          latitude: 52.3579,
          longitude: 4.8686,
          averageRating: 4.5,
          ratingCount: 23,
          users: { username: 'amsterdam_lover' },
          category: 'park',
          images: []
        },
        {
          id: 2,
          title: 'Anne Frank Huis',
          description: 'Historisch museum en monument waar Anne Frank ondergedoken zat tijdens de Tweede Wereldoorlog.',
          latitude: 52.3752,
          longitude: 4.8840,
          averageRating: 4.8,
          ratingCount: 156,
          users: { username: 'history_buff' },
          category: 'museum',
          images: []
        },
        {
          id: 3,
          title: 'Café de Reiger',
          description: 'Gezellig bruin café in de Jordaan met authentieke sfeer en goede bieren.',
          latitude: 52.3738,
          longitude: 4.8852,
          averageRating: 4.2,
          ratingCount: 45,
          users: { username: 'cafe_hopper' },
          category: 'café',
          images: []
        },
        {
          id: 4,
          title: 'Rijksmuseum',
          description: 'Nederlands nationaal museum met kunst en geschiedenis, inclusief werken van Rembrandt en Vermeer.',
          latitude: 52.3600,
          longitude: 4.8852,
          averageRating: 4.7,
          ratingCount: 89,
          users: { username: 'art_lover' },
          category: 'museum',
          images: []
        },
        {
          id: 5,
          title: 'Foodhallen',
          description: 'Indoor foodmarket met diverse eetstandjes en gezellige sfeer in de Oud-West.',
          latitude: 52.3676,
          longitude: 4.8632,
          averageRating: 4.3,
          ratingCount: 67,
          users: { username: 'foodie_amsterdam' },
          category: 'restaurant',
          images: []
        }
      ];
      setSpots(mockSpots);
    } catch (error) {
      console.error('Error loading spots:', error);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  const filterSpots = () => {
    if (!searchQuery.trim()) {
      setFilteredSpots(spots);
      return;
    }

    const filtered = spots.filter(spot =>
      spot.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      spot.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
      spot.category?.toLowerCase().includes(searchQuery.toLowerCase())
    );
    setFilteredSpots(filtered);
  };

  const onRefresh = () => {
    setRefreshing(true);
    loadSpots();
  };

  const renderSpotCard = ({ item: spot }) => (
    <TouchableOpacity
      style={styles.spotCard}
      onPress={() => navigation.navigate('SpotDetail', { spotId: spot.id })}
    >
      {spot.images && spot.images.length > 0 ? (
        <Image source={{ uri: spot.images[0].image_url }} style={styles.spotImage} />
      ) : (
        <View style={styles.placeholderImage}>
          <Ionicons name="image-outline" size={40} color={theme.colors.textSecondary} />
        </View>
      )}
      
      <View style={styles.spotContent}>
        <View style={styles.spotHeader}>
          <Text style={styles.spotTitle} numberOfLines={1}>
            {spot.title}
          </Text>
          <View style={styles.ratingContainer}>
            <Ionicons name="star" size={14} color="#F39C12" />
            <Text style={styles.ratingText}>
              {spot.averageRating > 0 ? spot.averageRating.toFixed(1) : 'N/A'}
            </Text>
          </View>
        </View>
        
        <Text style={styles.spotDescription} numberOfLines={2}>
          {spot.description}
        </Text>
        
        <View style={styles.spotFooter}>
          <View style={styles.authorContainer}>
            <Ionicons name="person-circle-outline" size={16} color={theme.colors.textSecondary} />
            <Text style={styles.authorText}>
              {spot.users?.username || 'Onbekend'}
            </Text>
          </View>
          
          <View style={styles.metaContainer}>
            <View style={styles.metaItem}>
              <Ionicons name="chatbubble-outline" size={14} color={theme.colors.textSecondary} />
              <Text style={styles.metaText}>{spot.ratingCount}</Text>
            </View>
            
            {spot.category && (
              <View style={[styles.categoryBadge, { backgroundColor: getCategoryColor(spot.category) }]}>
                <Text style={styles.categoryText}>{spot.category}</Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  const getCategoryColor = (category) => {
    const colors = {
      'restaurant': '#E74C3C',
      'cafe': '#F39C12',
      'park': '#27AE60',
      'bar': '#9B59B6',
      'museum': '#3498DB',
      'winkel': '#E67E22',
      'sport': '#1ABC9C',
      'natuur': '#2ECC71',
      'cultuur': '#8E44AD',
      'uitgaan': '#E91E63',
    };
    return colors[category?.toLowerCase()] || theme.colors.textSecondary;
  };

  const renderEmptyState = () => (
    <View style={styles.emptyState}>
      <Ionicons name="location-outline" size={80} color={theme.colors.textSecondary} />
      <Text style={styles.emptyTitle}>Geen spots gevonden</Text>
      <Text style={styles.emptySubtitle}>
        {searchQuery 
          ? 'Probeer een andere zoekterm'
          : 'Wees de eerste om een spot toe te voegen!'
        }
      </Text>
      {!searchQuery && (
        <TouchableOpacity
          style={styles.addButton}
          onPress={() => navigation.navigate('CreateSpot')}
        >
          <Text style={styles.addButtonText}>Voeg een spot toe</Text>
        </TouchableOpacity>
      )}
    </View>
  );

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <View style={styles.searchInputContainer}>
          <Ionicons name="search-outline" size={20} color={theme.colors.textSecondary} />
          <TextInput
            style={styles.searchInput}
            placeholder="Zoek spots..."
            placeholderTextColor={theme.colors.textSecondary}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
          {searchQuery.length > 0 && (
            <TouchableOpacity onPress={() => setSearchQuery('')}>
              <Ionicons name="close-circle" size={20} color={theme.colors.textSecondary} />
            </TouchableOpacity>
          )}
        </View>
      </View>

      {/* Spots List */}
      <FlatList
        data={filteredSpots}
        renderItem={renderSpotCard}
        keyExtractor={(item) => item.id.toString()}
        contentContainerStyle={styles.listContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListEmptyComponent={!loading ? renderEmptyState : null}
        showsVerticalScrollIndicator={false}
      />

      {/* Floating Add Button */}
      <TouchableOpacity
        style={styles.fab}
        onPress={() => navigation.navigate('CreateSpot')}
      >
        <Ionicons name="add" size={28} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  searchContainer: {
    padding: theme.spacing.md,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: theme.colors.border,
  },
  searchInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: theme.colors.surface,
    borderRadius: theme.borderRadius.md,
    paddingHorizontal: theme.spacing.md,
    height: 40,
  },
  searchInput: {
    flex: 1,
    marginLeft: theme.spacing.sm,
    fontSize: 16,
    color: theme.colors.text,
  },
  listContainer: {
    padding: theme.spacing.md,
    paddingBottom: 100,
  },
  spotCard: {
    backgroundColor: '#fff',
    borderRadius: theme.borderRadius.lg,
    marginBottom: theme.spacing.md,
    overflow: 'hidden',
    ...theme.shadows.medium,
  },
  spotImage: {
    width: '100%',
    height: 150,
    backgroundColor: theme.colors.surface,
  },
  placeholderImage: {
    width: '100%',
    height: 150,
    backgroundColor: theme.colors.surface,
    justifyContent: 'center',
    alignItems: 'center',
  },
  spotContent: {
    padding: theme.spacing.md,
  },
  spotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: theme.spacing.sm,
  },
  spotTitle: {
    flex: 1,
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginRight: theme.spacing.sm,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text,
    marginLeft: theme.spacing.xs,
  },
  spotDescription: {
    fontSize: 14,
    color: theme.colors.textSecondary,
    lineHeight: 20,
    marginBottom: theme.spacing.md,
  },
  spotFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  authorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  authorText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  metaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: theme.spacing.md,
  },
  metaText: {
    fontSize: 12,
    color: theme.colors.textSecondary,
    marginLeft: theme.spacing.xs,
  },
  categoryBadge: {
    paddingHorizontal: theme.spacing.sm,
    paddingVertical: theme.spacing.xs,
    borderRadius: theme.borderRadius.sm,
  },
  categoryText: {
    fontSize: 10,
    color: '#fff',
    fontWeight: '600',
    textTransform: 'uppercase',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: theme.spacing.xxl,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.text,
    marginTop: theme.spacing.lg,
    marginBottom: theme.spacing.sm,
  },
  emptySubtitle: {
    fontSize: 16,
    color: theme.colors.textSecondary,
    textAlign: 'center',
    marginBottom: theme.spacing.lg,
  },
  addButton: {
    backgroundColor: theme.colors.primary,
    paddingHorizontal: theme.spacing.lg,
    paddingVertical: theme.spacing.md,
    borderRadius: theme.borderRadius.md,
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  fab: {
    position: 'absolute',
    bottom: theme.spacing.lg,
    right: theme.spacing.lg,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: theme.colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    ...theme.shadows.large,
  },
});

export default SpotsScreen;

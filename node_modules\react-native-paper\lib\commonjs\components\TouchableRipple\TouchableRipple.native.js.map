{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_Pressable", "_utils", "_settings", "_theming", "_forwardRef", "_hasTouchHandler", "_interopRequireDefault", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "ANDROID_VERSION_LOLLIPOP", "ANDROID_VERSION_PIE", "TouchableRipple", "style", "background", "borderless", "disabled", "disabledProp", "rippleColor", "underlayColor", "children", "theme", "themeOverrides", "rest", "ref", "useInternalTheme", "rippleEffectEnabled", "useContext", "SettingsContext", "onPress", "onLongPress", "onPressIn", "onPressOut", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "calculatedRippleColor", "calculatedUnderlayColor", "getTouchableRippleColors", "useForeground", "Platform", "OS", "Version", "supported", "androidRipple", "color", "foreground", "undefined", "createElement", "Pressable", "styles", "overflowHidden", "android_ripple", "Children", "only", "pressed", "Fragment", "View", "testID", "underlay", "backgroundColor", "StyleSheet", "create", "overflow", "absoluteFillObject", "zIndex", "Component", "forwardRef", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/TouchableRipple/TouchableRipple.native.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAYA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAEA,IAAAM,WAAA,GAAAN,OAAA;AACA,IAAAO,gBAAA,GAAAC,sBAAA,CAAAR,OAAA;AAA0D,SAAAQ,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAV,wBAAAU,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAd,uBAAA,YAAAA,CAAAU,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAE1D,MAAMG,wBAAwB,GAAG,EAAE;AACnC,MAAMC,mBAAmB,GAAG,EAAE;AAkB9B,MAAMC,eAAe,GAAGA,CACtB;EACEC,KAAK;EACLC,UAAU;EACVC,UAAU,GAAG,KAAK;EAClBC,QAAQ,EAAEC,YAAY;EACtBC,WAAW;EACXC,aAAa;EACbC,QAAQ;EACRC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,EACRC,GAA6B,KAC1B;EACH,MAAMH,KAAK,GAAG,IAAAI,yBAAgB,EAACH,cAAc,CAAC;EAC9C,MAAM;IAAEI;EAAoB,CAAC,GAAGpD,KAAK,CAACqD,UAAU,CAAWC,yBAAe,CAAC;EAE3E,MAAM;IAAEC,OAAO;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGT,IAAI;EAE5D,MAAMU,qBAAqB,GAAG,IAAAC,wBAAe,EAAC;IAC5CL,OAAO;IACPC,WAAW;IACXC,SAAS;IACTC;EACF,CAAC,CAAC;EAEF,MAAMhB,QAAQ,GAAGC,YAAY,IAAI,CAACgB,qBAAqB;EAEvD,MAAM;IAAEE,qBAAqB;IAAEC;EAAwB,CAAC,GACtD,IAAAC,+BAAwB,EAAC;IACvBhB,KAAK;IACLH,WAAW;IACXC;EACF,CAAC,CAAC;;EAEJ;EACA;EACA,MAAMmB,aAAa,GACjBC,qBAAQ,CAACC,EAAE,KAAK,SAAS,IACzBD,qBAAQ,CAACE,OAAO,IAAI9B,mBAAmB,IACvCI,UAAU;EAEZ,IAAIH,eAAe,CAAC8B,SAAS,EAAE;IAC7B,MAAMC,aAAa,GAAGjB,mBAAmB,GACrCZ,UAAU,IAAI;MACZ8B,KAAK,EAAET,qBAAqB;MAC5BpB,UAAU;MACV8B,UAAU,EAAEP;IACd,CAAC,GACDQ,SAAS;IAEb,oBACExE,KAAA,CAAAyE,aAAA,CAACrE,UAAA,CAAAsE,SAAS,EAAA5C,QAAA,KACJmB,IAAI;MACRC,GAAG,EAAEA,GAAI;MACTR,QAAQ,EAAEA,QAAS;MACnBH,KAAK,EAAE,CAACE,UAAU,IAAIkC,MAAM,CAACC,cAAc,EAAErC,KAAK,CAAE;MACpDsC,cAAc,EAAER;IAAc,IAE7BrE,KAAK,CAAC8E,QAAQ,CAACC,IAAI,CAACjC,QAAQ,CACpB,CAAC;EAEhB;EAEA,oBACE9C,KAAA,CAAAyE,aAAA,CAACrE,UAAA,CAAAsE,SAAS,EAAA5C,QAAA,KACJmB,IAAI;IACRC,GAAG,EAAEA,GAAI;IACTR,QAAQ,EAAEA,QAAS;IACnBH,KAAK,EAAE,CAACE,UAAU,IAAIkC,MAAM,CAACC,cAAc,EAAErC,KAAK;EAAE,IAEnD,CAAC;IAAEyC;EAAQ,CAAC,kBACXhF,KAAA,CAAAyE,aAAA,CAAAzE,KAAA,CAAAiF,QAAA,QACGD,OAAO,IAAI5B,mBAAmB,iBAC7BpD,KAAA,CAAAyE,aAAA,CAACtE,YAAA,CAAA+E,IAAI;IACHC,MAAM,EAAC,2BAA2B;IAClC5C,KAAK,EAAE,CACLoC,MAAM,CAACS,QAAQ,EACf;MAAEC,eAAe,EAAEvB;IAAwB,CAAC;EAC5C,CACH,CACF,EACA9D,KAAK,CAAC8E,QAAQ,CAACC,IAAI,CAACjC,QAAQ,CAC7B,CAEK,CAAC;AAEhB,CAAC;AAEDR,eAAe,CAAC8B,SAAS,GACvBH,qBAAQ,CAACC,EAAE,KAAK,SAAS,IAAID,qBAAQ,CAACE,OAAO,IAAI/B,wBAAwB;AAE3E,MAAMuC,MAAM,GAAGW,uBAAU,CAACC,MAAM,CAAC;EAC/BX,cAAc,EAAE;IACdY,QAAQ,EAAE;EACZ,CAAC;EACDJ,QAAQ,EAAE;IACR,GAAGE,uBAAU,CAACG,kBAAkB;IAChCC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,MAAMC,SAAS,GAAG,IAAAC,sBAAU,EAACtD,eAAe,CAAC;AAAC,IAAAuD,QAAA,GAAAC,OAAA,CAAAjF,OAAA,GAE/B8E,SAAS", "ignoreList": []}
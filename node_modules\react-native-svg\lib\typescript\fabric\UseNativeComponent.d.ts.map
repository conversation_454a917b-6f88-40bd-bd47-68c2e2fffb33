{"version": 3, "file": "UseNativeComponent.d.ts", "sourceRoot": "", "sources": ["../../../src/fabric/UseNativeComponent.ts"], "names": [], "mappings": "AACA,OAAO,KAAK,EAAE,UAAU,EAAE,MAAM,cAAc,CAAC;AAC/C,OAAO,KAAK,EACV,KAAK,EACL,KAAK,EACL,WAAW,EACZ,MAAM,2CAA2C,CAAC;AACnD,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,SAAS,CAAC;AAEzC,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,gBAAgB,CAAC;AAClD,OAAO,EAAE,UAAU,EAAE,MAAM,sBAAsB,CAAC;AAElD,UAAU,kBAAkB;IAC1B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,OAAO,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IAClC,MAAM,CAAC,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC;IAC9B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,WAAW,CAAC,EAAE,MAAM,CAAC;IACrB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,SAAS,CAAC,EAAE,MAAM,CAAC;IACnB,QAAQ,CAAC,EAAE,MAAM,CAAC;IAClB,QAAQ,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACjC,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,OAAO,CAAC,EAAE,MAAM,CAAC;IACjB,aAAa,CAAC,EAAE,MAAM,CAAC;CACxB;AAED,KAAK,WAAW,GAAG,QAAQ,CAAC;IAC1B,IAAI,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9B,OAAO,CAAC,EAAE,UAAU,CAAC;IACrB,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB,CAAC,CAAC;AAEH,UAAU,wBAAwB;IAChC,KAAK,CAAC,EAAE,UAAU,CAAC;IACnB,IAAI,CAAC,EAAE,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,CAAC;IAC7C,WAAW,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACtC,QAAQ,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACjC,MAAM,CAAC,EAAE,WAAW,CAAC,UAAU,GAAG,WAAW,CAAC,CAAC;IAC/C,aAAa,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACxC,WAAW,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;IACtC,aAAa,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACtC,cAAc,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACvC,eAAe,CAAC,EAAE,WAAW,CAAC,aAAa,CAAC,UAAU,CAAC,GAAG,UAAU,CAAC,CAAC;IACtE,gBAAgB,CAAC,EAAE,KAAK,CAAC;IACzB,gBAAgB,CAAC,EAAE,KAAK,CAAC;IACzB,YAAY,CAAC,EAAE,WAAW,CAAC,KAAK,EAAE,CAAC,CAAC,CAAC;IACrC,QAAQ,CAAC,EAAE,aAAa,CAAC,MAAM,CAAC,CAAC;IACjC,MAAM,CAAC,EAAE,MAAM,CAAC;CACjB;AAED,UAAU,WACR,SAAQ,SAAS,EACf,kBAAkB,EAClB,wBAAwB;IAC1B,IAAI,CAAC,EAAE,MAAM,CAAC;IACd,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;IAC5B,CAAC,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;IAC5B,MAAM,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;IACjC,KAAK,CAAC,EAAE,WAAW,CAAC,UAAU,CAAC,CAAC;CACjC;;AAED,wBAEG"}
"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Icon", {
  enumerable: true,
  get: function () {
    return _AvatarIcon.default;
  }
});
Object.defineProperty(exports, "Image", {
  enumerable: true,
  get: function () {
    return _AvatarImage.default;
  }
});
Object.defineProperty(exports, "Text", {
  enumerable: true,
  get: function () {
    return _AvatarText.default;
  }
});
var _AvatarIcon = _interopRequireDefault(require("./AvatarIcon"));
var _AvatarImage = _interopRequireDefault(require("./AvatarImage"));
var _AvatarText = _interopRequireDefault(require("./AvatarText"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
//# sourceMappingURL=Avatar.js.map
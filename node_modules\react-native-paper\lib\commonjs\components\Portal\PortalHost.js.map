{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_PortalManager", "_interopRequireDefault", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "PortalContext", "exports", "createContext", "PortalHost", "Component", "displayName", "componentDidMount", "manager", "queue", "length", "action", "pop", "type", "mount", "key", "children", "update", "unmount", "setManager", "<PERSON><PERSON><PERSON>", "push", "op", "index", "findIndex", "render", "createElement", "Provider", "value", "View", "style", "styles", "container", "collapsable", "pointerEvents", "props", "ref", "StyleSheet", "create", "flex"], "sourceRoot": "../../../../src", "sources": ["components/Portal/PortalHost.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,cAAA,GAAAC,sBAAA,CAAAH,OAAA;AAA4C,SAAAG,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAT,uBAAA,YAAAA,CAAAK,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAiBrC,MAAMgB,aAAa,GAAAC,OAAA,CAAAD,aAAA,gBAAGzB,KAAK,CAAC2B,aAAa,CAAgB,IAAW,CAAC;;AAE5E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACe,MAAMC,UAAU,SAAS5B,KAAK,CAAC6B,SAAS,CAAQ;EAC7D,OAAOC,WAAW,GAAG,aAAa;EAElCC,iBAAiBA,CAAA,EAAG;IAClB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK;IAExB,OAAOA,KAAK,CAACC,MAAM,IAAIF,OAAO,EAAE;MAC9B,MAAMG,MAAM,GAAGF,KAAK,CAACG,GAAG,CAAC,CAAC;MAC1B,IAAID,MAAM,EAAE;QACV;QACA,QAAQA,MAAM,CAACE,IAAI;UACjB,KAAK,OAAO;YACVL,OAAO,CAACM,KAAK,CAACH,MAAM,CAACI,GAAG,EAAEJ,MAAM,CAACK,QAAQ,CAAC;YAC1C;UACF,KAAK,QAAQ;YACXR,OAAO,CAACS,MAAM,CAACN,MAAM,CAACI,GAAG,EAAEJ,MAAM,CAACK,QAAQ,CAAC;YAC3C;UACF,KAAK,SAAS;YACZR,OAAO,CAACU,OAAO,CAACP,MAAM,CAACI,GAAG,CAAC;YAC3B;QACJ;MACF;IACF;EACF;EAEQI,UAAU,GAAIX,OAAyC,IAAK;IAClE,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB,CAAC;EAEOM,KAAK,GAAIE,QAAyB,IAAK;IAC7C,MAAMD,GAAG,GAAG,IAAI,CAACK,OAAO,EAAE;IAE1B,IAAI,IAAI,CAACZ,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACM,KAAK,CAACC,GAAG,EAAEC,QAAQ,CAAC;IACnC,CAAC,MAAM;MACL,IAAI,CAACP,KAAK,CAACY,IAAI,CAAC;QAAER,IAAI,EAAE,OAAO;QAAEE,GAAG;QAAEC;MAAS,CAAC,CAAC;IACnD;IAEA,OAAOD,GAAG;EACZ,CAAC;EAEOE,MAAM,GAAGA,CAACF,GAAW,EAAEC,QAAyB,KAAK;IAC3D,IAAI,IAAI,CAACR,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACS,MAAM,CAACF,GAAG,EAAEC,QAAQ,CAAC;IACpC,CAAC,MAAM;MACL,MAAMM,EAAa,GAAG;QAAET,IAAI,EAAE,OAAO;QAAEE,GAAG;QAAEC;MAAS,CAAC;MACtD,MAAMO,KAAK,GAAG,IAAI,CAACd,KAAK,CAACe,SAAS,CAC/BnC,CAAC,IAAKA,CAAC,CAACwB,IAAI,KAAK,OAAO,IAAKxB,CAAC,CAACwB,IAAI,KAAK,QAAQ,IAAIxB,CAAC,CAAC0B,GAAG,KAAKA,GACjE,CAAC;MAED,IAAIQ,KAAK,GAAG,CAAC,CAAC,EAAE;QACd,IAAI,CAACd,KAAK,CAACc,KAAK,CAAC,GAAGD,EAAE;MACxB,CAAC,MAAM;QACL,IAAI,CAACb,KAAK,CAACY,IAAI,CAACC,EAAe,CAAC;MAClC;IACF;EACF,CAAC;EAEOJ,OAAO,GAAIH,GAAW,IAAK;IACjC,IAAI,IAAI,CAACP,OAAO,EAAE;MAChB,IAAI,CAACA,OAAO,CAACU,OAAO,CAACH,GAAG,CAAC;IAC3B,CAAC,MAAM;MACL,IAAI,CAACN,KAAK,CAACY,IAAI,CAAC;QAAER,IAAI,EAAE,SAAS;QAAEE;MAAI,CAAC,CAAC;IAC3C;EACF,CAAC;EAEOK,OAAO,GAAG,CAAC;EACXX,KAAK,GAAgB,EAAE;EAG/BgB,MAAMA,CAAA,EAAG;IACP,oBACEjD,KAAA,CAAAkD,aAAA,CAACzB,aAAa,CAAC0B,QAAQ;MACrBC,KAAK,EAAE;QACLd,KAAK,EAAE,IAAI,CAACA,KAAK;QACjBG,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBC,OAAO,EAAE,IAAI,CAACA;MAChB;IAAE,gBAGF1C,KAAA,CAAAkD,aAAA,CAAC/C,YAAA,CAAAkD,IAAI;MACHC,KAAK,EAAEC,MAAM,CAACC,SAAU;MACxBC,WAAW,EAAE,KAAM;MACnBC,aAAa,EAAC;IAAU,GAEvB,IAAI,CAACC,KAAK,CAACnB,QACR,CAAC,eACPxC,KAAA,CAAAkD,aAAA,CAAC9C,cAAA,CAAAI,OAAa;MAACoD,GAAG,EAAE,IAAI,CAACjB;IAAW,CAAE,CAChB,CAAC;EAE7B;AACF;AAACjB,OAAA,CAAAlB,OAAA,GAAAoB,UAAA;AAED,MAAM2B,MAAM,GAAGM,uBAAU,CAACC,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}
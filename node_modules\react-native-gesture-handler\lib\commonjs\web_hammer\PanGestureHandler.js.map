{"version": 3, "sources": ["PanGestureHandler.ts"], "names": ["PanGestureHandler", "DraggingGestureHandler", "name", "NativeGestureClass", "Hammer", "Pan", "getHammerConfig", "direction", "getDirection", "getState", "type", "nextState", "previousState", "State", "UNDETERMINED", "ACTIVE", "BEGAN", "config", "getConfig", "activeOffsetXStart", "activeOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "minDist", "directions", "horizontalDirections", "DIRECTION_ALL", "push", "DIRECTION_LEFT", "DIRECTION_RIGHT", "length", "DIRECTION_HORIZONTAL", "concat", "verticalDirections", "DIRECTION_UP", "DIRECTION_DOWN", "DIRECTION_VERTICAL", "DIRECTION_NONE", "hasCustomActivationCriteria", "minDistSq", "shouldFailUnderCustomCriteria", "deltaX", "deltaY", "criteria", "failOffsetXStart", "failOffsetXEnd", "failOffsetYStart", "failOffsetYEnd", "shouldActivateUnderCustomCriteria", "velocity", "x", "y", "minVelocityX", "minVelocityY", "minVelocitySq", "shouldMultiFingerPanFail", "pointer<PERSON><PERSON><PERSON>", "scale", "deltaRotation", "deltaScale", "Math", "abs", "absDeltaRotation", "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD", "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD", "updateHasCustomActivationCriteria", "isGestureEnabledForEvent", "props", "_recognizer", "inputData", "failed", "velocityX", "velocityY", "maxPointers", "success"], "mappings": ";;;;;;;AAAA;;AAEA;;AAKA;;AACA;;AACA;;;;AAGA,MAAMA,iBAAN,SAAgCC,+BAAhC,CAAuD;AAC7C,MAAJC,IAAI,GAAG;AACT,WAAO,KAAP;AACD;;AAEqB,MAAlBC,kBAAkB,GAAG;AACvB,WAAOC,kBAAOC,GAAd;AACD;;AAEDC,EAAAA,eAAe,GAAG;AAChB,WAAO,EACL,GAAG,MAAMA,eAAN,EADE;AAELC,MAAAA,SAAS,EAAE,KAAKC,YAAL;AAFN,KAAP;AAID;;AAEDC,EAAAA,QAAQ,CAACC,IAAD,EAA8B;AACpC,UAAMC,SAAS,GAAG,MAAMF,QAAN,CAAeC,IAAf,CAAlB,CADoC,CAEpC;;AACA,QACE,KAAKE,aAAL,KAAuBC,aAAMC,YAA7B,IACAH,SAAS,KAAKE,aAAME,MAFtB,EAGE;AACA,aAAOF,aAAMG,KAAb;AACD;;AACD,WAAOL,SAAP;AACD;;AAEDH,EAAAA,YAAY,GAAG;AACb,UAAMS,MAAM,GAAG,KAAKC,SAAL,EAAf;AACA,UAAM;AACJC,MAAAA,kBADI;AAEJC,MAAAA,gBAFI;AAGJC,MAAAA,kBAHI;AAIJC,MAAAA,gBAJI;AAKJC,MAAAA;AALI,QAMFN,MANJ;AAOA,QAAIO,UAAoB,GAAG,EAA3B;AACA,QAAIC,oBAAoB,GAAG,EAA3B;;AAEA,QAAI,CAAC,kBAAMF,OAAN,CAAL,EAAqB;AACnB,aAAOnB,kBAAOsB,aAAd;AACD;;AAED,QAAI,CAAC,kBAAMP,kBAAN,CAAL,EAAgC;AAC9BM,MAAAA,oBAAoB,CAACE,IAArB,CAA0BvB,kBAAOwB,cAAjC;AACD;;AACD,QAAI,CAAC,kBAAMR,gBAAN,CAAL,EAA8B;AAC5BK,MAAAA,oBAAoB,CAACE,IAArB,CAA0BvB,kBAAOyB,eAAjC;AACD;;AACD,QAAIJ,oBAAoB,CAACK,MAArB,KAAgC,CAApC,EAAuC;AACrCL,MAAAA,oBAAoB,GAAG,CAACrB,kBAAO2B,oBAAR,CAAvB;AACD;;AAEDP,IAAAA,UAAU,GAAGA,UAAU,CAACQ,MAAX,CAAkBP,oBAAlB,CAAb;AACA,QAAIQ,kBAAkB,GAAG,EAAzB;;AAEA,QAAI,CAAC,kBAAMZ,kBAAN,CAAL,EAAgC;AAC9BY,MAAAA,kBAAkB,CAACN,IAAnB,CAAwBvB,kBAAO8B,YAA/B;AACD;;AACD,QAAI,CAAC,kBAAMZ,gBAAN,CAAL,EAA8B;AAC5BW,MAAAA,kBAAkB,CAACN,IAAnB,CAAwBvB,kBAAO+B,cAA/B;AACD;;AAED,QAAIF,kBAAkB,CAACH,MAAnB,KAA8B,CAAlC,EAAqC;AACnCG,MAAAA,kBAAkB,GAAG,CAAC7B,kBAAOgC,kBAAR,CAArB;AACD;;AAEDZ,IAAAA,UAAU,GAAGA,UAAU,CAACQ,MAAX,CAAkBC,kBAAlB,CAAb;;AAEA,QAAI,CAACT,UAAU,CAACM,MAAhB,EAAwB;AACtB,aAAO1B,kBAAOiC,cAAd;AACD;;AACD,QACEb,UAAU,CAAC,CAAD,CAAV,KAAkBpB,kBAAO2B,oBAAzB,IACAP,UAAU,CAAC,CAAD,CAAV,KAAkBpB,kBAAOgC,kBAF3B,EAGE;AACA,aAAOhC,kBAAOsB,aAAd;AACD;;AACD,QAAID,oBAAoB,CAACK,MAArB,IAA+BG,kBAAkB,CAACH,MAAtD,EAA8D;AAC5D,aAAO1B,kBAAOsB,aAAd;AACD;;AAED,WAAOF,UAAU,CAAC,CAAD,CAAjB;AACD;;AAEDN,EAAAA,SAAS,GAAG;AACV,QAAI,CAAC,KAAKoB,2BAAV,EAAuC;AACrC;AACA;AACA,aAAO;AACLC,QAAAA,SAAS,EAAE;AADN,OAAP;AAGD;;AACD,WAAO,KAAKtB,MAAZ;AACD;;AAEDuB,EAAAA,6BAA6B,CAC3B;AAAEC,IAAAA,MAAF;AAAUC,IAAAA;AAAV,GAD2B,EAE3BC,QAF2B,EAG3B;AACA,WACG,CAAC,kBAAMA,QAAQ,CAACC,gBAAf,CAAD,IACCH,MAAM,GAAGE,QAAQ,CAACC,gBADpB,IAEC,CAAC,kBAAMD,QAAQ,CAACE,cAAf,CAAD,IAAmCJ,MAAM,GAAGE,QAAQ,CAACE,cAFtD,IAGC,CAAC,kBAAMF,QAAQ,CAACG,gBAAf,CAAD,IACCJ,MAAM,GAAGC,QAAQ,CAACG,gBAJpB,IAKC,CAAC,kBAAMH,QAAQ,CAACI,cAAf,CAAD,IAAmCL,MAAM,GAAGC,QAAQ,CAACI,cANxD;AAQD;;AAEDC,EAAAA,iCAAiC,CAC/B;AAAEP,IAAAA,MAAF;AAAUC,IAAAA,MAAV;AAAkBO,IAAAA;AAAlB,GAD+B,EAE/BN,QAF+B,EAG/B;AACA,WACG,CAAC,kBAAMA,QAAQ,CAACxB,kBAAf,CAAD,IACCsB,MAAM,GAAGE,QAAQ,CAACxB,kBADpB,IAEC,CAAC,kBAAMwB,QAAQ,CAACvB,gBAAf,CAAD,IACCqB,MAAM,GAAGE,QAAQ,CAACvB,gBAHpB,IAIC,CAAC,kBAAMuB,QAAQ,CAACtB,kBAAf,CAAD,IACCqB,MAAM,GAAGC,QAAQ,CAACtB,kBALpB,IAMC,CAAC,kBAAMsB,QAAQ,CAACrB,gBAAf,CAAD,IACCoB,MAAM,GAAGC,QAAQ,CAACrB,gBAPpB,IAQA,gCACE,uBAAW;AAAE4B,MAAAA,CAAC,EAAET,MAAL;AAAaU,MAAAA,CAAC,EAAET;AAAhB,KAAX,CADF,EAEEC,QAAQ,CAACJ,SAFX,CARA,IAYA,gCAAoBU,QAAQ,CAACC,CAA7B,EAAgCP,QAAQ,CAACS,YAAzC,CAZA,IAaA,gCAAoBH,QAAQ,CAACE,CAA7B,EAAgCR,QAAQ,CAACU,YAAzC,CAbA,IAcA,gCAAoB,uBAAWJ,QAAX,CAApB,EAA0CN,QAAQ,CAACW,aAAnD,CAfF;AAiBD;;AAEDC,EAAAA,wBAAwB,CAAC;AACvBC,IAAAA,aADuB;AAEvBC,IAAAA,KAFuB;AAGvBC,IAAAA;AAHuB,GAAD,EAQrB;AACD,QAAIF,aAAa,IAAI,CAArB,EAAwB;AACtB,aAAO,KAAP;AACD,KAHA,CAKD;;;AACA,UAAMG,UAAU,GAAGC,IAAI,CAACC,GAAL,CAASJ,KAAK,GAAG,CAAjB,CAAnB;AACA,UAAMK,gBAAgB,GAAGF,IAAI,CAACC,GAAL,CAASH,aAAT,CAAzB;;AACA,QAAIC,UAAU,GAAGI,+CAAjB,EAAuD;AACrD;AACA;AACA,aAAO,IAAP;AACD;;AACD,QAAID,gBAAgB,GAAGE,kDAAvB,EAAgE;AAC9D;AACA;AACA,aAAO,IAAP;AACD;;AAED,WAAO,KAAP;AACD;;AAEDC,EAAAA,iCAAiC,CAC/BtB,QAD+B,EAE/B;AACA,WACE,0BAAcA,QAAQ,CAACJ,SAAvB,KACA,0BAAcI,QAAQ,CAACS,YAAvB,CADA,IAEA,0BAAcT,QAAQ,CAACU,YAAvB,CAFA,IAGA,0BAAcV,QAAQ,CAACW,aAAvB,CAHA,IAIA,0BAAcX,QAAQ,CAACxB,kBAAvB,CAJA,IAKA,0BAAcwB,QAAQ,CAACvB,gBAAvB,CALA,IAMA,0BAAcuB,QAAQ,CAACtB,kBAAvB,CANA,IAOA,0BAAcsB,QAAQ,CAACrB,gBAAvB,CARF;AAUD;;AAED4C,EAAAA,wBAAwB,CACtBC,KADsB,EAEtBC,WAFsB,EAGtBC,SAHsB,EAItB;AACA,QAAI,KAAK7B,6BAAL,CAAmC6B,SAAnC,EAA8CF,KAA9C,CAAJ,EAA0D;AACxD,aAAO;AAAEG,QAAAA,MAAM,EAAE;AAAV,OAAP;AACD;;AAED,UAAMrB,QAAQ,GAAG;AAAEC,MAAAA,CAAC,EAAEmB,SAAS,CAACE,SAAf;AAA0BpB,MAAAA,CAAC,EAAEkB,SAAS,CAACG;AAAvC,KAAjB;;AACA,QACE,KAAKlC,2BAAL,IACA,KAAKU,iCAAL,CACE;AAAEP,MAAAA,MAAM,EAAE4B,SAAS,CAAC5B,MAApB;AAA4BC,MAAAA,MAAM,EAAE2B,SAAS,CAAC3B,MAA9C;AAAsDO,MAAAA;AAAtD,KADF,EAEEkB,KAFF,CAFF,EAME;AACA,UACE,KAAKZ,wBAAL,CAA8B;AAC5BC,QAAAA,aAAa,EAAEa,SAAS,CAACI,WADG;AAE5BhB,QAAAA,KAAK,EAAEY,SAAS,CAACZ,KAFW;AAG5BC,QAAAA,aAAa,EAAEW,SAAS,CAACX;AAHG,OAA9B,CADF,EAME;AACA,eAAO;AACLY,UAAAA,MAAM,EAAE;AADH,SAAP;AAGD;;AACD,aAAO;AAAEI,QAAAA,OAAO,EAAE;AAAX,OAAP;AACD;;AACD,WAAO;AAAEA,MAAAA,OAAO,EAAE;AAAX,KAAP;AACD;;AAlNoD;;eAqNxC1E,iB", "sourcesContent": ["import Hammer from '@egjs/hammerjs';\n\nimport {\n  EventMap,\n  MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD,\n  MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD,\n} from './constants';\nimport Dragging<PERSON><PERSON>ureHandler from './DraggingGestureHandler';\nimport { isValidNumber, isnan, TEST_MIN_IF_NOT_NAN, VEC_LEN_SQ } from './utils';\nimport { State } from '../State';\n\nimport { Config, HammerInputExt } from './GestureHandler';\nclass PanGestureHandler extends DraggingGestureHandler {\n  get name() {\n    return 'pan';\n  }\n\n  get NativeGestureClass() {\n    return Hammer.Pan;\n  }\n\n  getHammerConfig() {\n    return {\n      ...super.getHammerConfig(),\n      direction: this.getDirection(),\n    };\n  }\n\n  getState(type: keyof typeof EventMap) {\n    const nextState = super.getState(type);\n    // Ensure that the first state sent is `BEGAN` and not `ACTIVE`\n    if (\n      this.previousState === State.UNDETERMINED &&\n      nextState === State.ACTIVE\n    ) {\n      return State.BEGAN;\n    }\n    return nextState;\n  }\n\n  getDirection() {\n    const config = this.getConfig();\n    const {\n      activeOffsetXStart,\n      activeOffsetXEnd,\n      activeOffsetYStart,\n      activeOffsetYEnd,\n      minDist,\n    } = config;\n    let directions: number[] = [];\n    let horizontalDirections = [];\n\n    if (!isnan(minDist)) {\n      return Hammer.DIRECTION_ALL;\n    }\n\n    if (!isnan(activeOffsetXStart)) {\n      horizontalDirections.push(Hammer.DIRECTION_LEFT);\n    }\n    if (!isnan(activeOffsetXEnd)) {\n      horizontalDirections.push(Hammer.DIRECTION_RIGHT);\n    }\n    if (horizontalDirections.length === 2) {\n      horizontalDirections = [Hammer.DIRECTION_HORIZONTAL];\n    }\n\n    directions = directions.concat(horizontalDirections);\n    let verticalDirections = [];\n\n    if (!isnan(activeOffsetYStart)) {\n      verticalDirections.push(Hammer.DIRECTION_UP);\n    }\n    if (!isnan(activeOffsetYEnd)) {\n      verticalDirections.push(Hammer.DIRECTION_DOWN);\n    }\n\n    if (verticalDirections.length === 2) {\n      verticalDirections = [Hammer.DIRECTION_VERTICAL];\n    }\n\n    directions = directions.concat(verticalDirections);\n\n    if (!directions.length) {\n      return Hammer.DIRECTION_NONE;\n    }\n    if (\n      directions[0] === Hammer.DIRECTION_HORIZONTAL &&\n      directions[1] === Hammer.DIRECTION_VERTICAL\n    ) {\n      return Hammer.DIRECTION_ALL;\n    }\n    if (horizontalDirections.length && verticalDirections.length) {\n      return Hammer.DIRECTION_ALL;\n    }\n\n    return directions[0];\n  }\n\n  getConfig() {\n    if (!this.hasCustomActivationCriteria) {\n      // Default config\n      // If no params have been defined then this config should emulate the native gesture as closely as possible.\n      return {\n        minDistSq: 10,\n      };\n    }\n    return this.config;\n  }\n\n  shouldFailUnderCustomCriteria(\n    { deltaX, deltaY }: HammerInputExt,\n    criteria: any\n  ) {\n    return (\n      (!isnan(criteria.failOffsetXStart) &&\n        deltaX < criteria.failOffsetXStart) ||\n      (!isnan(criteria.failOffsetXEnd) && deltaX > criteria.failOffsetXEnd) ||\n      (!isnan(criteria.failOffsetYStart) &&\n        deltaY < criteria.failOffsetYStart) ||\n      (!isnan(criteria.failOffsetYEnd) && deltaY > criteria.failOffsetYEnd)\n    );\n  }\n\n  shouldActivateUnderCustomCriteria(\n    { deltaX, deltaY, velocity }: any,\n    criteria: any\n  ) {\n    return (\n      (!isnan(criteria.activeOffsetXStart) &&\n        deltaX < criteria.activeOffsetXStart) ||\n      (!isnan(criteria.activeOffsetXEnd) &&\n        deltaX > criteria.activeOffsetXEnd) ||\n      (!isnan(criteria.activeOffsetYStart) &&\n        deltaY < criteria.activeOffsetYStart) ||\n      (!isnan(criteria.activeOffsetYEnd) &&\n        deltaY > criteria.activeOffsetYEnd) ||\n      TEST_MIN_IF_NOT_NAN(\n        VEC_LEN_SQ({ x: deltaX, y: deltaY }),\n        criteria.minDistSq\n      ) ||\n      TEST_MIN_IF_NOT_NAN(velocity.x, criteria.minVelocityX) ||\n      TEST_MIN_IF_NOT_NAN(velocity.y, criteria.minVelocityY) ||\n      TEST_MIN_IF_NOT_NAN(VEC_LEN_SQ(velocity), criteria.minVelocitySq)\n    );\n  }\n\n  shouldMultiFingerPanFail({\n    pointerLength,\n    scale,\n    deltaRotation,\n  }: {\n    deltaRotation: number;\n    pointerLength: number;\n    scale: number;\n  }) {\n    if (pointerLength <= 1) {\n      return false;\n    }\n\n    // Test if the pan had too much pinching or rotating.\n    const deltaScale = Math.abs(scale - 1);\n    const absDeltaRotation = Math.abs(deltaRotation);\n    if (deltaScale > MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD) {\n      // > If the threshold doesn't seem right.\n      // You can log the value which it failed at here:\n      return true;\n    }\n    if (absDeltaRotation > MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD) {\n      // > If the threshold doesn't seem right.\n      // You can log the value which it failed at here:\n      return true;\n    }\n\n    return false;\n  }\n\n  updateHasCustomActivationCriteria(\n    criteria: Config & { minVelocityX?: number; minVelocityY?: number }\n  ) {\n    return (\n      isValidNumber(criteria.minDistSq) ||\n      isValidNumber(criteria.minVelocityX) ||\n      isValidNumber(criteria.minVelocityY) ||\n      isValidNumber(criteria.minVelocitySq) ||\n      isValidNumber(criteria.activeOffsetXStart) ||\n      isValidNumber(criteria.activeOffsetXEnd) ||\n      isValidNumber(criteria.activeOffsetYStart) ||\n      isValidNumber(criteria.activeOffsetYEnd)\n    );\n  }\n\n  isGestureEnabledForEvent(\n    props: any,\n    _recognizer: any,\n    inputData: HammerInputExt & { deltaRotation: number }\n  ) {\n    if (this.shouldFailUnderCustomCriteria(inputData, props)) {\n      return { failed: true };\n    }\n\n    const velocity = { x: inputData.velocityX, y: inputData.velocityY };\n    if (\n      this.hasCustomActivationCriteria &&\n      this.shouldActivateUnderCustomCriteria(\n        { deltaX: inputData.deltaX, deltaY: inputData.deltaY, velocity },\n        props\n      )\n    ) {\n      if (\n        this.shouldMultiFingerPanFail({\n          pointerLength: inputData.maxPointers,\n          scale: inputData.scale,\n          deltaRotation: inputData.deltaRotation,\n        })\n      ) {\n        return {\n          failed: true,\n        };\n      }\n      return { success: true };\n    }\n    return { success: false };\n  }\n}\n\nexport default PanGestureHandler;\n"]}
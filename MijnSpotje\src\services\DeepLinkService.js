import { Linking, Alert } from 'react-native';

class DeepLinkService {
  constructor() {
    this.scheme = 'mijnspotje';
    this.listeners = [];
    this.isInitialized = false;
  }

  // Initialize deep linking
  async initialize() {
    if (this.isInitialized) return null;

    try {
      // Handle app launch from deep link
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        console.log('🔗 App launched with deep link:', initialUrl);
        setTimeout(() => this.handleDeepLink(initialUrl), 1000); // Delay to ensure app is ready
      }

      // Listen for deep links while app is running
      const subscription = Linking.addEventListener('url', (event) => {
        if (event && event.url) {
          console.log('🔗 Deep link received:', event.url);
          this.handleDeepLink(event.url);
        }
      });

      this.isInitialized = true;
      console.log('✅ Deep linking initialized');

      return subscription;
    } catch (error) {
      console.error('❌ Error initializing deep linking:', error);
      return null;
    }
  }

  // Handle incoming deep links
  handleDeepLink(url) {
    try {
      if (!url || typeof url !== 'string') {
        console.warn('⚠️ Invalid deep link URL:', url);
        return;
      }

      const parsedUrl = this.parseDeepLink(url);

      if (parsedUrl) {
        console.log('📱 Parsed deep link:', parsedUrl);
        this.notifyListeners(parsedUrl);
      } else {
        console.warn('⚠️ Could not parse deep link:', url);
      }
    } catch (error) {
      console.error('❌ Error handling deep link:', error);
    }
  }

  // Parse deep link URL
  parseDeepLink(url) {
    try {
      // Skip Expo development URLs
      if (url.includes('exp://') || url.includes('exps://')) {
        console.log('🔧 Skipping Expo development URL:', url);
        return null;
      }

      // Handle both custom scheme and universal links
      const urlPattern = new RegExp(`${this.scheme}://(.+)`);
      const match = url.match(urlPattern);

      if (!match) {
        console.log('❌ Invalid deep link format:', url);
        return null;
      }

      const path = match[1];
      const [type, ...params] = path.split('/');

      switch (type) {
        case 'spot':
          return {
            type: 'spot',
            spotId: params[0],
            action: 'view'
          };
        
        case 'group':
          return {
            type: 'group',
            groupId: params[0],
            action: params[1] || 'view'
          };
        
        case 'invite':
          return {
            type: 'invite',
            code: params[0],
            action: 'join'
          };
        
        default:
          console.log('❌ Unknown deep link type:', type);
          return null;
      }
    } catch (error) {
      console.error('❌ Error parsing deep link:', error);
      return null;
    }
  }

  // Generate deep link for spot
  generateSpotLink(spot) {
    const deepLink = `${this.scheme}://spot/${spot.id}`;
    const webFallback = `https://mijnspotje.app/spot/${spot.id}`; // Future web version
    
    return {
      deepLink,
      webFallback,
      universalLink: webFallback // For future universal links
    };
  }

  // Generate deep link for group
  generateGroupLink(group) {
    const deepLink = `${this.scheme}://group/${group.id}`;
    const webFallback = `https://mijnspotje.app/group/${group.id}`;
    
    return {
      deepLink,
      webFallback,
      universalLink: webFallback
    };
  }

  // Generate invitation link
  generateInviteLink(inviteCode) {
    const deepLink = `${this.scheme}://invite/${inviteCode}`;
    const webFallback = `https://mijnspotje.app/invite/${inviteCode}`;
    
    return {
      deepLink,
      webFallback,
      universalLink: webFallback
    };
  }

  // Add listener for deep link events
  addListener(callback) {
    this.listeners.push(callback);
    console.log('🔗 Deep link listener added');
  }

  // Remove listener
  removeListener(callback) {
    this.listeners = this.listeners.filter(listener => listener !== callback);
    console.log('🔗 Deep link listener removed');
  }

  // Notify all listeners
  notifyListeners(parsedUrl) {
    this.listeners.forEach(callback => {
      try {
        callback(parsedUrl);
      } catch (error) {
        console.error('❌ Error in deep link listener:', error);
      }
    });
  }

  // Check if app can handle URL
  async canOpenURL(url) {
    try {
      return await Linking.canOpenURL(url);
    } catch (error) {
      console.error('❌ Error checking URL capability:', error);
      return false;
    }
  }

  // Open external URL
  async openURL(url) {
    try {
      const canOpen = await this.canOpenURL(url);
      
      if (canOpen) {
        await Linking.openURL(url);
        return true;
      } else {
        console.log('❌ Cannot open URL:', url);
        return false;
      }
    } catch (error) {
      console.error('❌ Error opening URL:', error);
      return false;
    }
  }

  // Open app store if app not installed
  async openAppStore() {
    try {
      const appStoreUrl = 'https://apps.apple.com/app/mijn-spotje/id123456789'; // Future App Store URL
      const playStoreUrl = 'https://play.google.com/store/apps/details?id=com.mijnspotje.app'; // Future Play Store URL
      
      // For now, show info dialog
      Alert.alert(
        'App Niet Geïnstalleerd',
        'Mijn Spotje is nog niet beschikbaar in de app stores. Deze functie wordt binnenkort toegevoegd.',
        [{ text: 'OK' }]
      );
    } catch (error) {
      console.error('❌ Error opening app store:', error);
    }
  }

  // Handle web fallback
  async handleWebFallback(url) {
    try {
      await Linking.openURL(url);
    } catch (error) {
      console.error('❌ Error opening web fallback:', error);
    }
  }

  // Test deep link (development only)
  async testDeepLink(type, id) {
    if (!__DEV__) return;
    
    let testUrl;
    switch (type) {
      case 'spot':
        testUrl = `${this.scheme}://spot/${id}`;
        break;
      case 'group':
        testUrl = `${this.scheme}://group/${id}`;
        break;
      case 'invite':
        testUrl = `${this.scheme}://invite/${id}`;
        break;
      default:
        console.log('❌ Invalid test type:', type);
        return;
    }
    
    console.log('🧪 Testing deep link:', testUrl);
    this.handleDeepLink(testUrl);
  }

  // Get scheme
  getScheme() {
    return this.scheme;
  }

  // Cleanup
  cleanup() {
    this.listeners = [];
    this.isInitialized = false;
    console.log('🔗 Deep linking cleaned up');
  }
}

// Export singleton instance
export default new DeepLinkService();

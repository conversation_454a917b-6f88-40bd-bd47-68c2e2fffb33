{"name": "@react-navigation/stack", "description": "Stack navigator component for iOS and Android with animated transitions and gestures", "version": "7.3.3", "keywords": ["react-native-component", "react-component", "react-native", "react-navigation", "ios", "android", "stack"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/stack"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/stack-navigator/", "source": "./src/index.tsx", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"@react-navigation/elements": "^2.4.3", "color": "^4.2.3"}, "devDependencies": {"@jest/globals": "^29.7.0", "@react-navigation/native": "^7.1.10", "@testing-library/react-native": "^13.2.0", "@types/color": "^4.2.0", "@types/react": "~19.0.10", "del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-builder-bob": "^0.40.9", "react-native-gesture-handler": "~2.25.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"@react-navigation/native": "^7.1.10", "react": ">= 18.2.0", "react-native": "*", "react-native-gesture-handler": ">= 2.0.0", "react-native-safe-area-context": ">= 4.0.0", "react-native-screens": ">= 4.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "52e5ec64745b399655adf7bd9773e31372d51e62"}
import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { useTranslation } from '../i18n/useTranslation';

const PasswordChangeModal = ({ visible, onClose, isDark }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  const validateForm = useCallback(() => {
    const newErrors = {};
    
    if (!formData.currentPassword.trim()) {
      newErrors.currentPassword = 'Current password is required';
    }
    
    if (!formData.newPassword.trim()) {
      newErrors.newPassword = 'New password is required';
    } else if (formData.newPassword.length < 8) {
      newErrors.newPassword = 'Password must be at least 8 characters';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.newPassword)) {
      newErrors.newPassword = 'Password must contain uppercase, lowercase, and number';
    }
    
    if (!formData.confirmPassword.trim()) {
      newErrors.confirmPassword = 'Please confirm your new password';
    } else if (formData.newPassword !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }
    
    if (formData.currentPassword === formData.newPassword) {
      newErrors.newPassword = 'New password must be different from current password';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleSave = useCallback(async () => {
    if (validateForm()) {
      setIsLoading(true);
      try {
        // Simulate API call for password verification and change
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        // Simulate current password verification
        if (formData.currentPassword !== 'demo123') {
          setErrors({ currentPassword: 'Current password is incorrect' });
          setIsLoading(false);
          return;
        }
        
        Alert.alert(
          t('common.success'),
          'Password changed successfully',
          [{ text: t('common.ok'), onPress: handleClose }]
        );
      } catch (error) {
        Alert.alert(t('common.error'), 'Failed to change password');
      } finally {
        setIsLoading(false);
      }
    }
  }, [formData, validateForm, handleClose, t]);

  const handleClose = useCallback(() => {
    setFormData({
      currentPassword: '',
      newPassword: '',
      confirmPassword: '',
    });
    setErrors({});
    setShowPasswords({
      current: false,
      new: false,
      confirm: false,
    });
    onClose();
  }, [onClose]);

  const togglePasswordVisibility = useCallback((field) => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field],
    }));
  }, []);

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, isDark && styles.containerDark]}>
        <View style={[styles.header, isDark && styles.headerDark]}>
          <TouchableOpacity onPress={handleClose} disabled={isLoading}>
            <Text style={[styles.button, styles.cancelButton]}>{t('common.cancel')}</Text>
          </TouchableOpacity>
          <Text style={[styles.title, isDark && styles.textDark]}>
            {t('settings.account.changePassword')}
          </Text>
          <TouchableOpacity onPress={handleSave} disabled={isLoading}>
            <Text style={[styles.button, styles.saveButton, isLoading && styles.buttonDisabled]}>
              {isLoading ? t('common.loading') : t('common.save')}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.infoBox}>
            <Text style={[styles.infoText, isDark && styles.infoTextDark]}>
              For security, please enter your current password and choose a strong new password.
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Current Password *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={[
                  styles.passwordInput,
                  isDark && styles.inputDark,
                  errors.currentPassword && styles.inputError
                ]}
                value={formData.currentPassword}
                onChangeText={(text) => setFormData(prev => ({ ...prev, currentPassword: text }))}
                placeholder="Enter current password"
                placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
                secureTextEntry={!showPasswords.current}
                editable={!isLoading}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => togglePasswordVisibility('current')}
              >
                <Text style={styles.eyeIcon}>{showPasswords.current ? '👁️' : '👁️‍🗨️'}</Text>
              </TouchableOpacity>
            </View>
            {errors.currentPassword && <Text style={styles.errorText}>{errors.currentPassword}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>New Password *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={[
                  styles.passwordInput,
                  isDark && styles.inputDark,
                  errors.newPassword && styles.inputError
                ]}
                value={formData.newPassword}
                onChangeText={(text) => setFormData(prev => ({ ...prev, newPassword: text }))}
                placeholder="Enter new password"
                placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
                secureTextEntry={!showPasswords.new}
                editable={!isLoading}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => togglePasswordVisibility('new')}
              >
                <Text style={styles.eyeIcon}>{showPasswords.new ? '👁️' : '👁️‍🗨️'}</Text>
              </TouchableOpacity>
            </View>
            {errors.newPassword && <Text style={styles.errorText}>{errors.newPassword}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Confirm New Password *</Text>
            <View style={styles.passwordContainer}>
              <TextInput
                style={[
                  styles.passwordInput,
                  isDark && styles.inputDark,
                  errors.confirmPassword && styles.inputError
                ]}
                value={formData.confirmPassword}
                onChangeText={(text) => setFormData(prev => ({ ...prev, confirmPassword: text }))}
                placeholder="Confirm new password"
                placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
                secureTextEntry={!showPasswords.confirm}
                editable={!isLoading}
              />
              <TouchableOpacity
                style={styles.eyeButton}
                onPress={() => togglePasswordVisibility('confirm')}
              >
                <Text style={styles.eyeIcon}>{showPasswords.confirm ? '👁️' : '👁️‍🗨️'}</Text>
              </TouchableOpacity>
            </View>
            {errors.confirmPassword && <Text style={styles.errorText}>{errors.confirmPassword}</Text>}
          </View>

          <View style={styles.requirementsBox}>
            <Text style={[styles.requirementsTitle, isDark && styles.textDark]}>
              Password Requirements:
            </Text>
            <Text style={[styles.requirementText, isDark && styles.textSecondaryDark]}>
              • At least 8 characters long
            </Text>
            <Text style={[styles.requirementText, isDark && styles.textSecondaryDark]}>
              • Contains uppercase and lowercase letters
            </Text>
            <Text style={[styles.requirementText, isDark && styles.textSecondaryDark]}>
              • Contains at least one number
            </Text>
          </View>

          <View style={styles.demoInfo}>
            <Text style={[styles.demoText, isDark && styles.textSecondaryDark]}>
              Demo: Current password is "demo123"
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  textDark: {
    color: '#ffffff',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  button: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    color: '#ff3b30',
  },
  saveButton: {
    color: '#007AFF',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  infoBox: {
    backgroundColor: '#f8f9fa',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  infoText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
  },
  infoTextDark: {
    color: '#8e8e93',
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  labelDark: {
    color: '#ffffff',
  },
  passwordContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  passwordInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
  },
  inputDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
    color: '#ffffff',
  },
  inputError: {
    borderColor: '#ff3b30',
  },
  eyeButton: {
    position: 'absolute',
    right: 12,
    padding: 4,
  },
  eyeIcon: {
    fontSize: 16,
  },
  errorText: {
    color: '#ff3b30',
    fontSize: 12,
    marginTop: 4,
  },
  requirementsBox: {
    backgroundColor: '#f0f8ff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 20,
  },
  requirementsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  requirementText: {
    fontSize: 12,
    color: '#666666',
    marginBottom: 4,
  },
  demoInfo: {
    backgroundColor: '#fff3cd',
    padding: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  demoText: {
    fontSize: 12,
    color: '#856404',
    fontStyle: 'italic',
    textAlign: 'center',
  },
});

export default PasswordChangeModal;

class PerformanceMonitor {
  constructor() {
    this.metrics = new Map();
    this.isEnabled = __DEV__; // Only enable in development
  }

  // Start timing an operation
  startTiming(label) {
    if (!this.isEnabled) return;
    
    this.metrics.set(label, {
      startTime: Date.now(),
      endTime: null,
      duration: null,
    });
  }

  // End timing an operation
  endTiming(label) {
    if (!this.isEnabled) return;
    
    const metric = this.metrics.get(label);
    if (metric) {
      metric.endTime = Date.now();
      metric.duration = metric.endTime - metric.startTime;
      
      console.log(`⏱️ Performance: ${label} took ${metric.duration}ms`);
      
      // Warn about slow operations
      if (metric.duration > 1000) {
        console.warn(`🐌 Slow operation detected: ${label} took ${metric.duration}ms`);
      }
    }
  }

  // Measure a function execution time
  measure(label, fn) {
    if (!this.isEnabled) {
      return fn();
    }

    this.startTiming(label);
    const result = fn();
    
    if (result && typeof result.then === 'function') {
      // Handle async functions
      return result.finally(() => {
        this.endTiming(label);
      });
    } else {
      // Handle sync functions
      this.endTiming(label);
      return result;
    }
  }

  // Get all metrics
  getMetrics() {
    if (!this.isEnabled) return {};
    
    const result = {};
    this.metrics.forEach((value, key) => {
      result[key] = value;
    });
    return result;
  }

  // Clear all metrics
  clearMetrics() {
    this.metrics.clear();
  }

  // Log memory usage (React Native specific)
  logMemoryUsage(label = 'Memory Usage') {
    if (!this.isEnabled) return;
    
    // Note: This is a simplified version. In production, you might want to use
    // more sophisticated memory monitoring tools
    console.log(`📊 ${label}: JS Heap usage monitoring would go here`);
  }

  // Monitor component render performance
  monitorRender(componentName, renderFn) {
    if (!this.isEnabled) {
      return renderFn();
    }

    const label = `Render: ${componentName}`;
    this.startTiming(label);
    
    try {
      const result = renderFn();
      this.endTiming(label);
      return result;
    } catch (error) {
      this.endTiming(label);
      throw error;
    }
  }

  // Monitor async operations
  async monitorAsync(label, asyncFn) {
    if (!this.isEnabled) {
      return await asyncFn();
    }

    this.startTiming(label);
    
    try {
      const result = await asyncFn();
      this.endTiming(label);
      return result;
    } catch (error) {
      this.endTiming(label);
      throw error;
    }
  }

  // Monitor FlatList performance
  monitorFlatListRender(itemCount, renderTime) {
    if (!this.isEnabled) return;
    
    const itemsPerSecond = itemCount / (renderTime / 1000);
    console.log(`📋 FlatList Performance: ${itemCount} items in ${renderTime}ms (${itemsPerSecond.toFixed(2)} items/sec)`);
    
    if (itemsPerSecond < 10) {
      console.warn(`🐌 Slow FlatList rendering detected: ${itemsPerSecond.toFixed(2)} items/sec`);
    }
  }

  // Monitor navigation performance
  monitorNavigation(fromScreen, toScreen, duration) {
    if (!this.isEnabled) return;
    
    console.log(`🧭 Navigation: ${fromScreen} → ${toScreen} took ${duration}ms`);
    
    if (duration > 500) {
      console.warn(`🐌 Slow navigation detected: ${fromScreen} → ${toScreen} took ${duration}ms`);
    }
  }

  // Monitor image loading performance
  monitorImageLoad(imageUri, loadTime) {
    if (!this.isEnabled) return;
    
    console.log(`🖼️ Image Load: ${imageUri} took ${loadTime}ms`);
    
    if (loadTime > 2000) {
      console.warn(`🐌 Slow image loading detected: ${imageUri} took ${loadTime}ms`);
    }
  }

  // Monitor storage operations
  monitorStorage(operation, duration, dataSize = null) {
    if (!this.isEnabled) return;
    
    const sizeInfo = dataSize ? ` (${dataSize} bytes)` : '';
    console.log(`💾 Storage ${operation}: ${duration}ms${sizeInfo}`);
    
    if (duration > 1000) {
      console.warn(`🐌 Slow storage operation detected: ${operation} took ${duration}ms${sizeInfo}`);
    }
  }

  // Generate performance report
  generateReport() {
    if (!this.isEnabled) return 'Performance monitoring disabled in production';
    
    const metrics = this.getMetrics();
    const report = Object.entries(metrics)
      .map(([label, metric]) => `${label}: ${metric.duration}ms`)
      .join('\n');
    
    return `📊 Performance Report:\n${report}`;
  }
}

// Export singleton instance
export default new PerformanceMonitor();

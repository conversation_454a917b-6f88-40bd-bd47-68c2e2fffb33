{"version": 3, "names": ["_reactNative", "require", "_color", "_interopRequireDefault", "_DarkTheme", "e", "__esModule", "default", "isAnimatedValue", "it", "Animated", "Value", "exports", "overlay", "elevation", "surfaceColor", "_MD2DarkTheme$colors", "MD2DarkTheme", "colors", "surface", "inputRange", "interpolate", "outputRange", "map", "calculateColor", "overlayTransparency", "elevationOverlayTransparency", "color", "mix", "hex"], "sourceRoot": "../../../src", "sources": ["styles/overlay.tsx"], "mappings": ";;;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,UAAA,GAAAH,OAAA;AAAqD,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE9C,MAAMG,eAAe,GAC1BC,EAAqE,IAC5CA,EAAE,YAAYC,qBAAQ,CAACC,KAAK;AAACC,OAAA,CAAAJ,eAAA,GAAAA,eAAA;AAEzC,SAASK,OAAOA,CAC7BC,SAAY,EACZC,YAAoB,IAAAC,oBAAA,KAAAA,oBAAA,GAAGC,uBAAY,CAACC,MAAM,cAAAF,oBAAA,uBAAnBA,oBAAA,CAAqBG,OAAO,KAC0B;EAC7E,IAAIX,eAAe,CAACM,SAAS,CAAC,EAAE;IAC9B,MAAMM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC;;IAEtC;IACA,OAAON,SAAS,CAACO,WAAW,CAAC;MAC3BD,UAAU;MACVE,WAAW,EAAEF,UAAU,CAACG,GAAG,CAAET,SAAS,IAAK;QACzC,OAAOU,cAAc,CAACT,YAAY,EAAED,SAAS,CAAC;MAChD,CAAC;IACH,CAAC,CAAC;EACJ;;EAEA;EACA,OAAOU,cAAc,CAACT,YAAY,EAAED,SAAS,CAAC;AAChD;AAEA,SAASU,cAAcA,CAACT,YAAoB,EAAED,SAAiB,GAAG,CAAC,EAAE;EACnE,IAAIW,mBAA2B;EAC/B,IAAIX,SAAS,IAAI,CAAC,IAAIA,SAAS,IAAI,EAAE,EAAE;IACrCW,mBAAmB,GAAGC,4BAA4B,CAACZ,SAAS,CAAC;EAC/D,CAAC,MAAM,IAAIA,SAAS,GAAG,EAAE,EAAE;IACzBW,mBAAmB,GAAGC,4BAA4B,CAAC,EAAE,CAAC;EACxD,CAAC,MAAM;IACLD,mBAAmB,GAAGC,4BAA4B,CAAC,CAAC,CAAC;EACvD;EACA,OAAO,IAAAC,cAAK,EAACZ,YAAY,CAAC,CACvBa,GAAG,CAAC,IAAAD,cAAK,EAAC,OAAO,CAAC,EAAEF,mBAAmB,GAAG,IAAI,CAAC,CAC/CI,GAAG,CAAC,CAAC;AACV;AAEA,MAAMH,4BAAoD,GAAG;EAC3D,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,CAAC;EACJ,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,IAAI;EACP,CAAC,EAAE,EAAE;EACL,CAAC,EAAE,IAAI;EACP,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,EAAE;EACN,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,IAAI;EACR,EAAE,EAAE,KAAK;EACT,EAAE,EAAE,KAAK;EACT,EAAE,EAAE;AACN,CAAC", "ignoreList": []}
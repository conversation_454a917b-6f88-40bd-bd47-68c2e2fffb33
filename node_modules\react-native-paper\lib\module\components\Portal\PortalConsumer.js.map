{"version": 3, "names": ["React", "PortalConsumer", "Component", "componentDidMount", "checkManager", "key", "props", "manager", "mount", "children", "componentDidUpdate", "update", "componentWillUnmount", "unmount", "Error", "render"], "sourceRoot": "../../../../src", "sources": ["components/Portal/PortalConsumer.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAS9B,eAAe,MAAMC,cAAc,SAASD,KAAK,CAACE,SAAS,CAAQ;EACjEC,iBAAiBA,CAAA,EAAG;IAClB,IAAI,CAACC,YAAY,CAAC,CAAC;IAEnB,IAAI,CAACC,GAAG,GAAG,IAAI,CAACC,KAAK,CAACC,OAAO,CAACC,KAAK,CAAC,IAAI,CAACF,KAAK,CAACG,QAAQ,CAAC;EAC1D;EAEAC,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACN,YAAY,CAAC,CAAC;IAEnB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACI,MAAM,CAAC,IAAI,CAACN,GAAG,EAAE,IAAI,CAACC,KAAK,CAACG,QAAQ,CAAC;EAC1D;EAEAG,oBAAoBA,CAAA,EAAG;IACrB,IAAI,CAACR,YAAY,CAAC,CAAC;IAEnB,IAAI,CAACE,KAAK,CAACC,OAAO,CAACM,OAAO,CAAC,IAAI,CAACR,GAAG,CAAC;EACtC;EAIQD,YAAYA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAACE,KAAK,CAACC,OAAO,EAAE;MACvB,MAAM,IAAIO,KAAK,CACb,4GAA4G,GAC1G,iGAAiG,GACjG,4EACJ,CAAC;IACH;EACF;EAEAC,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI;EACb;AACF", "ignoreList": []}
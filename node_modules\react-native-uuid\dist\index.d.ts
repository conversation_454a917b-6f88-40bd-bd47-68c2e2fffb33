export type { GenerateUUID } from './v35';
import { v4 } from './v4';
declare const _default: {
    parse: (s: string, buf?: Array<number>, offset?: number) => number[];
    unparse: (buf: Array<number>, offset?: number) => string;
    validate: (uuid: string) => boolean;
    version: (uuid: string) => number;
    v1: (options?: {
        node: number[];
        clockseq: number;
        msecs: number;
        nsecs: number;
        random: number[];
        rng: () => number[];
    }, buf?: Uint8Array, offset?: number) => string | Uint8Array;
    v3: import("./v35").GenerateUUID;
    v4: typeof v4;
    v5: import("./v35").GenerateUUID;
    NIL: string;
    DNS: string;
    URL: string;
    OID: string;
    X500: string;
};
export default _default;

{"version": 3, "sources": ["InteractionManager.ts"], "names": ["InteractionManager", "constructor", "Map", "configureInteractions", "handler", "config", "dropRelationsForHandlerWithTag", "handlerTag", "waitFor", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "push", "waitForRelations", "set", "simultaneousHandlers", "simultaneousRelations", "blocksHandlers", "blocksHandlersRelations", "shouldWaitForHandlerFailure", "get", "find", "tag", "undefined", "shouldRecognizeSimultaneously", "shouldRequireHandlerToWaitForFailure", "shouldHandlerBeCancelledBy", "_handler", "isNativeHandler", "name", "isActive", "state", "State", "ACTIVE", "isButton", "delete", "reset", "clear", "instance", "_instance"], "mappings": ";;;;;;;AACA;;;;AAGe,MAAMA,kBAAN,CAAyB;AAMtC;AACA;AACQC,EAAAA,WAAW,GAAG;AAAA,8CANqC,IAAIC,GAAJ,EAMrC;;AAAA,mDAL0C,IAAIA,GAAJ,EAK1C;;AAAA,qDAJ4C,IAAIA,GAAJ,EAI5C;AAAE;;AAEjBC,EAAAA,qBAAqB,CAACC,OAAD,EAA2BC,MAA3B,EAA2C;AACrE,SAAKC,8BAAL,CAAoCF,OAAO,CAACG,UAA5C;;AAEA,QAAIF,MAAM,CAACG,OAAX,EAAoB;AAClB,YAAMA,OAAiB,GAAG,EAA1B;AACAH,MAAAA,MAAM,CAACG,OAAP,CAAeC,OAAf,CAAwBC,YAAD,IAAiC;AACtD;AACA,YAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;AACpCF,UAAAA,OAAO,CAACG,IAAR,CAAaD,YAAb;AACD,SAFD,MAEO;AACL;AACAF,UAAAA,OAAO,CAACG,IAAR,CAAaD,YAAY,CAACH,UAA1B;AACD;AACF,OARD;AAUA,WAAKK,gBAAL,CAAsBC,GAAtB,CAA0BT,OAAO,CAACG,UAAlC,EAA8CC,OAA9C;AACD;;AAED,QAAIH,MAAM,CAACS,oBAAX,EAAiC;AAC/B,YAAMA,oBAA8B,GAAG,EAAvC;AACAT,MAAAA,MAAM,CAACS,oBAAP,CAA4BL,OAA5B,CAAqCC,YAAD,IAAiC;AACnE,YAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;AACpCI,UAAAA,oBAAoB,CAACH,IAArB,CAA0BD,YAA1B;AACD,SAFD,MAEO;AACLI,UAAAA,oBAAoB,CAACH,IAArB,CAA0BD,YAAY,CAACH,UAAvC;AACD;AACF,OAND;AAQA,WAAKQ,qBAAL,CAA2BF,GAA3B,CAA+BT,OAAO,CAACG,UAAvC,EAAmDO,oBAAnD;AACD;;AAED,QAAIT,MAAM,CAACW,cAAX,EAA2B;AACzB,YAAMA,cAAwB,GAAG,EAAjC;AACAX,MAAAA,MAAM,CAACW,cAAP,CAAsBP,OAAtB,CAA+BC,YAAD,IAAiC;AAC7D,YAAI,OAAOA,YAAP,KAAwB,QAA5B,EAAsC;AACpCM,UAAAA,cAAc,CAACL,IAAf,CAAoBD,YAApB;AACD,SAFD,MAEO;AACLM,UAAAA,cAAc,CAACL,IAAf,CAAoBD,YAAY,CAACH,UAAjC;AACD;AACF,OAND;AAQA,WAAKU,uBAAL,CAA6BJ,GAA7B,CAAiCT,OAAO,CAACG,UAAzC,EAAqDS,cAArD;AACD;AACF;;AAEME,EAAAA,2BAA2B,CAChCd,OADgC,EAEhCM,YAFgC,EAGvB;AACT,UAAMF,OAA6B,GAAG,KAAKI,gBAAL,CAAsBO,GAAtB,CACpCf,OAAO,CAACG,UAD4B,CAAtC;AAIA,WACE,CAAAC,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAEY,IAAT,CAAeC,GAAD,IAAiB;AAC7B,aAAOA,GAAG,KAAKX,YAAY,CAACH,UAA5B;AACD,KAFD,OAEOe,SAHT;AAKD;;AAEMC,EAAAA,6BAA6B,CAClCnB,OADkC,EAElCM,YAFkC,EAGzB;AACT,UAAMI,oBAA0C,GAC9C,KAAKC,qBAAL,CAA2BI,GAA3B,CAA+Bf,OAAO,CAACG,UAAvC,CADF;AAGA,WACE,CAAAO,oBAAoB,SAApB,IAAAA,oBAAoB,WAApB,YAAAA,oBAAoB,CAAEM,IAAtB,CAA4BC,GAAD,IAAiB;AAC1C,aAAOA,GAAG,KAAKX,YAAY,CAACH,UAA5B;AACD,KAFD,OAEOe,SAHT;AAKD;;AAEME,EAAAA,oCAAoC,CACzCpB,OADyC,EAEzCM,YAFyC,EAGhC;AACT,UAAMF,OAA6B,GAAG,KAAKS,uBAAL,CAA6BE,GAA7B,CACpCf,OAAO,CAACG,UAD4B,CAAtC;AAIA,WACE,CAAAC,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAEY,IAAT,CAAeC,GAAD,IAAiB;AAC7B,aAAOA,GAAG,KAAKX,YAAY,CAACH,UAA5B;AACD,KAFD,OAEOe,SAHT;AAKD;;AAEMG,EAAAA,0BAA0B,CAC/BC,QAD+B,EAE/BhB,YAF+B,EAGtB;AAAA;;AACT;AACA,UAAMiB,eAAe,GACnBjB,YAAY,CAACT,WAAb,CAAyB2B,IAAzB,KAAkC,0BADpC;AAEA,UAAMC,QAAQ,GAAGnB,YAAY,CAACoB,KAAb,KAAuBC,aAAMC,MAA9C;AACA,UAAMC,QAAQ,GAAG,0BAAAvB,YAAY,CAACuB,QAAb,qFAAAvB,YAAY,OAAkB,IAA/C;AAEA,WAAOiB,eAAe,IAAIE,QAAnB,IAA+B,CAACI,QAAvC;AACD;;AAEM3B,EAAAA,8BAA8B,CAACC,UAAD,EAA2B;AAC9D,SAAKK,gBAAL,CAAsBsB,MAAtB,CAA6B3B,UAA7B;AACA,SAAKQ,qBAAL,CAA2BmB,MAA3B,CAAkC3B,UAAlC;AACA,SAAKU,uBAAL,CAA6BiB,MAA7B,CAAoC3B,UAApC;AACD;;AAEM4B,EAAAA,KAAK,GAAG;AACb,SAAKvB,gBAAL,CAAsBwB,KAAtB;AACA,SAAKrB,qBAAL,CAA2BqB,KAA3B;AACA,SAAKnB,uBAAL,CAA6BmB,KAA7B;AACD;;AAEyB,aAARC,QAAQ,GAAuB;AAC/C,QAAI,CAAC,KAAKC,SAAV,EAAqB;AACnB,WAAKA,SAAL,GAAiB,IAAItC,kBAAJ,EAAjB;AACD;;AAED,WAAO,KAAKsC,SAAZ;AACD;;AAlIqC;;;;gBAAnBtC,kB", "sourcesContent": ["import type IGestureHandler from '../handlers/IGestureHandler';\nimport { State } from '../../State';\nimport { Config, Handler } from '../interfaces';\n\nexport default class InteractionManager {\n  private static _instance: InteractionManager;\n  private readonly waitForRelations: Map<number, number[]> = new Map();\n  private readonly simultaneousRelations: Map<number, number[]> = new Map();\n  private readonly blocksHandlersRelations: Map<number, number[]> = new Map();\n\n  // Private becaues of singleton\n  // eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function\n  private constructor() {}\n\n  public configureInteractions(handler: IGestureHandler, config: Config) {\n    this.dropRelationsForHandlerWithTag(handler.handlerTag);\n\n    if (config.waitFor) {\n      const waitFor: number[] = [];\n      config.waitFor.forEach((otherHandler: Handler): void => {\n        // New API reference\n        if (typeof otherHandler === 'number') {\n          waitFor.push(otherHandler);\n        } else {\n          // Old API reference\n          waitFor.push(otherHandler.handlerTag);\n        }\n      });\n\n      this.waitForRelations.set(handler.handlerTag, waitFor);\n    }\n\n    if (config.simultaneousHandlers) {\n      const simultaneousHandlers: number[] = [];\n      config.simultaneousHandlers.forEach((otherHandler: Handler): void => {\n        if (typeof otherHandler === 'number') {\n          simultaneousHandlers.push(otherHandler);\n        } else {\n          simultaneousHandlers.push(otherHandler.handlerTag);\n        }\n      });\n\n      this.simultaneousRelations.set(handler.handlerTag, simultaneousHandlers);\n    }\n\n    if (config.blocksHandlers) {\n      const blocksHandlers: number[] = [];\n      config.blocksHandlers.forEach((otherHandler: Handler): void => {\n        if (typeof otherHandler === 'number') {\n          blocksHandlers.push(otherHandler);\n        } else {\n          blocksHandlers.push(otherHandler.handlerTag);\n        }\n      });\n\n      this.blocksHandlersRelations.set(handler.handlerTag, blocksHandlers);\n    }\n  }\n\n  public shouldWaitForHandlerFailure(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    const waitFor: number[] | undefined = this.waitForRelations.get(\n      handler.handlerTag\n    );\n\n    return (\n      waitFor?.find((tag: number) => {\n        return tag === otherHandler.handlerTag;\n      }) !== undefined\n    );\n  }\n\n  public shouldRecognizeSimultaneously(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    const simultaneousHandlers: number[] | undefined =\n      this.simultaneousRelations.get(handler.handlerTag);\n\n    return (\n      simultaneousHandlers?.find((tag: number) => {\n        return tag === otherHandler.handlerTag;\n      }) !== undefined\n    );\n  }\n\n  public shouldRequireHandlerToWaitForFailure(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    const waitFor: number[] | undefined = this.blocksHandlersRelations.get(\n      handler.handlerTag\n    );\n\n    return (\n      waitFor?.find((tag: number) => {\n        return tag === otherHandler.handlerTag;\n      }) !== undefined\n    );\n  }\n\n  public shouldHandlerBeCancelledBy(\n    _handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    // We check constructor name instead of using `instanceof` in order do avoid circular dependencies\n    const isNativeHandler =\n      otherHandler.constructor.name === 'NativeViewGestureHandler';\n    const isActive = otherHandler.state === State.ACTIVE;\n    const isButton = otherHandler.isButton?.() === true;\n\n    return isNativeHandler && isActive && !isButton;\n  }\n\n  public dropRelationsForHandlerWithTag(handlerTag: number): void {\n    this.waitForRelations.delete(handlerTag);\n    this.simultaneousRelations.delete(handlerTag);\n    this.blocksHandlersRelations.delete(handlerTag);\n  }\n\n  public reset() {\n    this.waitForRelations.clear();\n    this.simultaneousRelations.clear();\n    this.blocksHandlersRelations.clear();\n  }\n\n  public static get instance(): InteractionManager {\n    if (!this._instance) {\n      this._instance = new InteractionManager();\n    }\n\n    return this._instance;\n  }\n}\n"]}
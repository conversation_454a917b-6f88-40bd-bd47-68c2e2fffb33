import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  ScrollView,
  Modal,
  Alert,
  Image,
} from 'react-native';
import * as ImagePicker from 'expo-image-picker';
import LocationService from '../services/LocationService';
import ImageManager from './ImageManager';

const EnhancedCreateSpotModal = ({ visible, onClose, onSave, isDark, t }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'restaurant',
    rating: 5,
    location: '',
    images: [],
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);
  const [currentLocation, setCurrentLocation] = useState(null);

  const categories = [
    { value: 'restaurant', label: 'Restaurant', icon: '🍽️' },
    { value: 'cafe', label: 'Café', icon: '☕' },
    { value: 'park', label: 'Park', icon: '🌳' },
    { value: 'museum', label: 'Museum', icon: '🏛️' },
    { value: 'shop', label: 'Winkel', icon: '🛍️' },
    { value: 'bar', label: 'Bar', icon: '🍺' },
    { value: 'attraction', label: 'Attractie', icon: '🎡' },
    { value: 'other', label: 'Overig', icon: '📍' },
  ];

  const validateForm = useCallback(() => {
    const newErrors = {};
    
    if (!formData.title.trim()) {
      newErrors.title = 'Titel is verplicht';
    }
    
    if (!formData.description.trim()) {
      newErrors.description = 'Beschrijving is verplicht';
    } else if (formData.description.length < 10) {
      newErrors.description = 'Beschrijving moet minimaal 10 karakters zijn';
    }
    
    if (!formData.location.trim()) {
      newErrors.location = 'Locatie is verplicht';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData]);

  const handleGetCurrentLocation = useCallback(async () => {
    try {
      setIsLoading(true);
      const location = await LocationService.getCurrentLocation();
      
      if (location) {
        setCurrentLocation(location);
        
        // Get address from coordinates
        const address = await LocationService.getAddressFromCoordinates(
          location.latitude,
          location.longitude
        );
        
        if (address) {
          setFormData(prev => ({
            ...prev,
            location: address.formattedAddress || `${location.latitude}, ${location.longitude}`
          }));
        } else {
          setFormData(prev => ({
            ...prev,
            location: `${location.latitude}, ${location.longitude}`
          }));
        }
        
        Alert.alert('Succes', 'Huidige locatie toegevoegd');
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Fout', 'Kon locatie niet ophalen');
    } finally {
      setIsLoading(false);
    }
  }, []);



  const handleSave = useCallback(async () => {
    if (!validateForm()) return;
    
    try {
      setIsLoading(true);
      
      const spotData = {
        ...formData,
        rating: parseFloat(formData.rating),
        latitude: currentLocation?.latitude || null,
        longitude: currentLocation?.longitude || null,
      };
      
      await onSave(spotData);
      
      // Reset form
      setFormData({
        title: '',
        description: '',
        category: 'restaurant',
        rating: 5,
        location: '',
        images: [],
      });
      setErrors({});
      setCurrentLocation(null);
      onClose();
    } catch (error) {
      console.error('Error saving spot:', error);
      Alert.alert('Fout', 'Kon spot niet opslaan');
    } finally {
      setIsLoading(false);
    }
  }, [formData, currentLocation, validateForm, onSave, onClose]);

  const handleClose = useCallback(() => {
    setFormData({
      title: '',
      description: '',
      category: 'restaurant',
      rating: 5,
      location: '',
      images: [],
    });
    setErrors({});
    setCurrentLocation(null);
    onClose();
  }, [onClose]);

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, isDark && styles.containerDark]}>
        <View style={[styles.header, isDark && styles.headerDark]}>
          <TouchableOpacity onPress={handleClose}>
            <Text style={[styles.button, styles.cancelButton]}>Annuleren</Text>
          </TouchableOpacity>
          <Text style={[styles.title, isDark && styles.textDark]}>Nieuwe Spot</Text>
          <TouchableOpacity 
            onPress={handleSave}
            disabled={isLoading}
            style={[styles.saveButtonContainer, isLoading && styles.saveButtonDisabled]}
          >
            <Text style={[
              styles.button, 
              styles.saveButton,
              isLoading && styles.saveButtonTextDisabled
            ]}>
              {isLoading ? 'Opslaan...' : 'Opslaan'}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Title Input */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Titel *</Text>
            <TextInput
              style={[styles.input, isDark && styles.inputDark, errors.title && styles.inputError]}
              value={formData.title}
              onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
              placeholder="Naam van de spot"
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              editable={!isLoading}
            />
            {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
          </View>

          {/* Category Selection */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Categorie</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
              {categories.map((category) => (
                <TouchableOpacity
                  key={category.value}
                  style={[
                    styles.categoryChip,
                    formData.category === category.value && styles.categoryChipSelected,
                    isDark && styles.categoryChipDark,
                    isDark && formData.category === category.value && styles.categoryChipSelectedDark,
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, category: category.value }))}
                  disabled={isLoading}
                >
                  <Text style={styles.categoryIcon}>{category.icon}</Text>
                  <Text style={[
                    styles.categoryText,
                    formData.category === category.value && styles.categoryTextSelected,
                    isDark && styles.categoryTextDark,
                  ]}>
                    {category.label}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          {/* Description Input */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Beschrijving *</Text>
            <TextInput
              style={[styles.textArea, isDark && styles.inputDark, errors.description && styles.inputError]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Beschrijf deze spot..."
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
              editable={!isLoading}
            />
            {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
          </View>

          {/* Location Input */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Locatie *</Text>
            <View style={styles.locationContainer}>
              <TextInput
                style={[styles.locationInput, isDark && styles.inputDark, errors.location && styles.inputError]}
                value={formData.location}
                onChangeText={(text) => setFormData(prev => ({ ...prev, location: text }))}
                placeholder="Adres of locatie"
                placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
                editable={!isLoading}
              />
              <TouchableOpacity
                style={[styles.locationButton, isDark && styles.locationButtonDark]}
                onPress={handleGetCurrentLocation}
                disabled={isLoading}
              >
                <Text style={styles.locationButtonText}>📍</Text>
              </TouchableOpacity>
            </View>
            {errors.location && <Text style={styles.errorText}>{errors.location}</Text>}
          </View>

          {/* Images Section */}
          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Foto's</Text>
            <ImageManager
              images={formData.images}
              onImagesChange={(images) => setFormData(prev => ({ ...prev, images }))}
              isDark={isDark}
              maxImages={5}
              editable={!isLoading}
            />
          </View>

          <View style={styles.bottomPadding} />
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
  },
  textDark: {
    color: '#ffffff',
  },
  button: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    color: '#ff3b30',
  },
  saveButtonContainer: {
    minWidth: 60,
  },
  saveButton: {
    color: '#007AFF',
  },
  saveButtonDisabled: {
    opacity: 0.5,
  },
  saveButtonTextDisabled: {
    color: '#8e8e93',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  labelDark: {
    color: '#ffffff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
  },
  inputDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
    color: '#ffffff',
  },
  inputError: {
    borderColor: '#ff3b30',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
    height: 100,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#ff3b30',
    fontSize: 12,
    marginTop: 4,
  },
  categoryScroll: {
    marginTop: 8,
  },
  categoryChip: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 8,
    flexDirection: 'row',
    alignItems: 'center',
  },
  categoryChipDark: {
    backgroundColor: '#2c2c2e',
  },
  categoryChipSelected: {
    backgroundColor: '#007AFF',
  },
  categoryChipSelectedDark: {
    backgroundColor: '#0A84FF',
  },
  categoryIcon: {
    fontSize: 16,
    marginRight: 4,
  },
  categoryText: {
    fontSize: 14,
    color: '#000000',
  },
  categoryTextDark: {
    color: '#ffffff',
  },
  categoryTextSelected: {
    color: '#ffffff',
  },
  locationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
    marginRight: 8,
  },
  locationButton: {
    backgroundColor: '#007AFF',
    borderRadius: 8,
    padding: 12,
    minWidth: 44,
    alignItems: 'center',
  },
  locationButtonDark: {
    backgroundColor: '#0A84FF',
  },
  locationButtonText: {
    fontSize: 18,
  },
  imagesScroll: {
    marginBottom: 12,
  },
  imageContainer: {
    position: 'relative',
    marginRight: 12,
  },
  image: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  removeImageButton: {
    position: 'absolute',
    top: -8,
    right: -8,
    backgroundColor: '#ff3b30',
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeImageText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  imageButtons: {
    flexDirection: 'row',
    gap: 12,
  },
  imageButton: {
    flex: 1,
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 16,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  imageButtonDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
  },
  imageButtonIcon: {
    fontSize: 24,
    marginBottom: 4,
  },
  imageButtonText: {
    fontSize: 12,
    color: '#000000',
  },
  bottomPadding: {
    height: 50,
  },
});

export default EnhancedCreateSpotModal;

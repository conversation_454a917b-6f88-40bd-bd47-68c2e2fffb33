{"version": 3, "sources": ["Pressable.tsx"], "names": ["React", "useCallback", "useMemo", "useRef", "useState", "GestureObjects", "Gesture", "GestureDetector", "Platform", "processColor", "NativeButton", "numberAsInset", "gestureToPressableEvent", "isTouchWithinInset", "gestureTouchToPressableEvent", "addInsets", "PressabilityDebugView", "INT32_MAX", "isF<PERSON><PERSON>", "isTestEnv", "DEFAULT_LONG_PRESS_DURATION", "IS_TEST_ENV", "IS_FABRIC", "Pressable", "props", "testOnly_pressed", "hitSlop", "pressRetentionOffset", "delayHoverIn", "onHoverIn", "delayHoverOut", "onHoverOut", "delayLongPress", "unstable_pressDelay", "onPress", "onPressIn", "onPressOut", "onLongPress", "style", "children", "android_disableSound", "android_ripple", "disabled", "remainingProps", "pressedState", "setPressedState", "pressableRef", "isPressCallbackEnabled", "hasPassedBoundsChecks", "shouldPreventNativeEffects", "normalizedHitSlop", "normalizedPressRetentionOffset", "hoverInTimeout", "hoverOutTimeout", "hoverGesture", "Hover", "manualActivation", "cancelsTouchesInView", "onBegin", "event", "current", "clearTimeout", "setTimeout", "onFinalize", "pressDelayTimeoutRef", "isTouchPropagationAllowed", "deferredEventPayload", "pressInHandler", "handlingOnTouchesDown", "pressOutHandler", "nativeEvent", "touches", "length", "changedTouches", "longPressTimeoutRef", "onEndHandlingTouchesDown", "cancelledMidPress", "activateLongPress", "longPressMinDuration", "pressAndTouchGesture", "Long<PERSON>ress", "minDuration", "maxDistance", "onTouchesDown", "measure", "_x", "_y", "width", "height", "at", "onTouchesUp", "onTouchesCancelled", "allTouches", "buttonGesture", "Native", "OS", "onStart", "appliedHitSlop", "isPressableEnabled", "gestures", "gesture", "enabled", "runOnJS", "shouldCancelWhenOutside", "Simultaneous", "pointerStyle", "cursor", "styleProp", "pressed", "childrenProp", "rippleColor", "defaultRippleColor", "undefined", "unprocessedRippleColor", "color", "radius", "__DEV__"], "mappings": ";;AAAA,OAAOA,KAAP,IAAgBC,WAAhB,EAA6BC,OAA7B,EAAsCC,MAAtC,EAA8CC,QAA9C,QAA8D,OAA9D;AACA,SAASC,cAAc,IAAIC,OAA3B,QAA0C,wCAA1C;AACA,SAASC,eAAT,QAAgC,yCAAhC;AAEA,SAEEC,QAFF,EAMEC,YANF,QAOO,cAPP;AAQA,OAAOC,YAAP,MAAyB,yBAAzB;AACA,SACEC,aADF,EAEEC,uBAFF,EAGEC,kBAHF,EAIEC,4BAJF,EAKEC,SALF,QAMO,SANP;AAOA,SAASC,qBAAT,QAAsC,sCAAtC;AAEA,SAASC,SAAT,EAAoBC,QAApB,EAA8BC,SAA9B,QAA+C,aAA/C;AAEA,MAAMC,2BAA2B,GAAG,GAApC;AACA,MAAMC,WAAW,GAAGF,SAAS,EAA7B;AAEA,IAAIG,SAAyB,GAAG,IAAhC;AAEA,eAAe,SAASC,SAAT,CAAmBC,KAAnB,EAA0C;AAAA;;AACvD,QAAM;AACJC,IAAAA,gBADI;AAEJC,IAAAA,OAFI;AAGJC,IAAAA,oBAHI;AAIJC,IAAAA,YAJI;AAKJC,IAAAA,SALI;AAMJC,IAAAA,aANI;AAOJC,IAAAA,UAPI;AAQJC,IAAAA,cARI;AASJC,IAAAA,mBATI;AAUJC,IAAAA,OAVI;AAWJC,IAAAA,SAXI;AAYJC,IAAAA,UAZI;AAaJC,IAAAA,WAbI;AAcJC,IAAAA,KAdI;AAeJC,IAAAA,QAfI;AAgBJC,IAAAA,oBAhBI;AAiBJC,IAAAA,cAjBI;AAkBJC,IAAAA,QAlBI;AAmBJ,OAAGC;AAnBC,MAoBFnB,KApBJ;AAsBA,QAAM,CAACoB,YAAD,EAAeC,eAAf,IAAkCzC,QAAQ,CAACqB,gBAAD,aAACA,gBAAD,cAACA,gBAAD,GAAqB,KAArB,CAAhD;AAEA,QAAMqB,YAAY,GAAG3C,MAAM,CAAO,IAAP,CAA3B,CAzBuD,CA2BvD;;AACA,QAAM4C,sBAAsB,GAAG5C,MAAM,CAAU,IAAV,CAArC;AACA,QAAM6C,qBAAqB,GAAG7C,MAAM,CAAU,KAAV,CAApC;AACA,QAAM8C,0BAA0B,GAAG9C,MAAM,CAAU,KAAV,CAAzC;AAEA,QAAM+C,iBAAyB,GAAGhD,OAAO,CACvC,MACE,OAAOwB,OAAP,KAAmB,QAAnB,GAA8Bf,aAAa,CAACe,OAAD,CAA3C,GAAwDA,OAAxD,aAAwDA,OAAxD,cAAwDA,OAAxD,GAAmE,EAF9B,EAGvC,CAACA,OAAD,CAHuC,CAAzC;AAMA,QAAMyB,8BAAsC,GAAGjD,OAAO,CACpD,MACE,OAAOyB,oBAAP,KAAgC,QAAhC,GACIhB,aAAa,CAACgB,oBAAD,CADjB,GAEKA,oBAFL,aAEKA,oBAFL,cAEKA,oBAFL,GAE6B,EAJqB,EAKpD,CAACA,oBAAD,CALoD,CAAtD;AAQA,QAAMyB,cAAc,GAAGjD,MAAM,CAAgB,IAAhB,CAA7B;AACA,QAAMkD,eAAe,GAAGlD,MAAM,CAAgB,IAAhB,CAA9B;AAEA,QAAMmD,YAAY,GAAGpD,OAAO,CAC1B,MACEI,OAAO,CAACiD,KAAR,GACGC,gBADH,CACoB,IADpB,EAC0B;AAD1B,GAEGC,oBAFH,CAEwB,KAFxB,EAGGC,OAHH,CAGYC,KAAD,IAAW;AAClB,QAAIN,eAAe,CAACO,OAApB,EAA6B;AAC3BC,MAAAA,YAAY,CAACR,eAAe,CAACO,OAAjB,CAAZ;AACD;;AACD,QAAIhC,YAAJ,EAAkB;AAChBwB,MAAAA,cAAc,CAACQ,OAAf,GAAyBE,UAAU,CACjC,MAAMjC,SAAN,aAAMA,SAAN,uBAAMA,SAAS,CAAGjB,uBAAuB,CAAC+C,KAAD,CAA1B,CADkB,EAEjC/B,YAFiC,CAAnC;AAIA;AACD;;AACDC,IAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAGjB,uBAAuB,CAAC+C,KAAD,CAA1B,CAAT;AACD,GAfH,EAgBGI,UAhBH,CAgBeJ,KAAD,IAAW;AACrB,QAAIP,cAAc,CAACQ,OAAnB,EAA4B;AAC1BC,MAAAA,YAAY,CAACT,cAAc,CAACQ,OAAhB,CAAZ;AACD;;AACD,QAAI9B,aAAJ,EAAmB;AACjBuB,MAAAA,eAAe,CAACO,OAAhB,GAA0BE,UAAU,CAClC,MAAM/B,UAAN,aAAMA,UAAN,uBAAMA,UAAU,CAAGnB,uBAAuB,CAAC+C,KAAD,CAA1B,CADkB,EAElC7B,aAFkC,CAApC;AAIA;AACD;;AACDC,IAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAGnB,uBAAuB,CAAC+C,KAAD,CAA1B,CAAV;AACD,GA5BH,CAFwB,EA+B1B,CAAC/B,YAAD,EAAeE,aAAf,EAA8BD,SAA9B,EAAyCE,UAAzC,CA/B0B,CAA5B;AAkCA,QAAMiC,oBAAoB,GAAG7D,MAAM,CAAgB,IAAhB,CAAnC;AACA,QAAM8D,yBAAyB,GAAG9D,MAAM,CAAU,KAAV,CAAxC,CApFuD,CAsFvD;;AACA,QAAM+D,oBAAoB,GAAG/D,MAAM,CAAwB,IAAxB,CAAnC;AAEA,QAAMgE,cAAc,GAAGlE,WAAW,CAC/B0D,KAAD,IAA2B;AACzB,QAAIS,qBAAqB,CAACR,OAA1B,EAAmC;AACjCM,MAAAA,oBAAoB,CAACN,OAArB,GAA+BD,KAA/B;AACD;;AAED,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtC;AACD;;AAEDM,IAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AAEAzB,IAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAGwB,KAAH,CAAT;AACAZ,IAAAA,sBAAsB,CAACa,OAAvB,GAAiC,IAAjC;AACAI,IAAAA,oBAAoB,CAACJ,OAArB,GAA+B,IAA/B;AACAf,IAAAA,eAAe,CAAC,IAAD,CAAf;AACD,GAhB+B,EAiBhC,CAACV,SAAD,CAjBgC,CAAlC;AAoBA,QAAMkC,eAAe,GAAGpE,WAAW,CAChC0D,KAAD,IAA2B;AACzB,QACE,CAACX,qBAAqB,CAACY,OAAvB,IACAD,KAAK,CAACW,WAAN,CAAkBC,OAAlB,CAA0BC,MAA1B,GACEb,KAAK,CAACW,WAAN,CAAkBG,cAAlB,CAAiCD,MAHrC,EAIE;AACA;AACD;;AAED,QAAIvC,mBAAmB,IAAI+B,oBAAoB,CAACJ,OAArB,KAAiC,IAA5D,EAAkE;AAChE;AACA;AACA;AACAC,MAAAA,YAAY,CAACG,oBAAoB,CAACJ,OAAtB,CAAZ;AACAO,MAAAA,cAAc,CAACR,KAAD,CAAd;AACD;;AAED,QAAIO,oBAAoB,CAACN,OAAzB,EAAkC;AAChCzB,MAAAA,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAG+B,oBAAoB,CAACN,OAAxB,CAAT;AACAM,MAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AACD;;AAEDxB,IAAAA,UAAU,SAAV,IAAAA,UAAU,WAAV,YAAAA,UAAU,CAAGuB,KAAH,CAAV;;AAEA,QAAIZ,sBAAsB,CAACa,OAA3B,EAAoC;AAClC1B,MAAAA,OAAO,SAAP,IAAAA,OAAO,WAAP,YAAAA,OAAO,CAAGyB,KAAH,CAAP;AACD;;AAED,QAAIe,mBAAmB,CAACd,OAAxB,EAAiC;AAC/BC,MAAAA,YAAY,CAACa,mBAAmB,CAACd,OAArB,CAAZ;AACAc,MAAAA,mBAAmB,CAACd,OAApB,GAA8B,IAA9B;AACD;;AAEDK,IAAAA,yBAAyB,CAACL,OAA1B,GAAoC,KAApC;AACAZ,IAAAA,qBAAqB,CAACY,OAAtB,GAAgC,KAAhC;AACAb,IAAAA,sBAAsB,CAACa,OAAvB,GAAiC,IAAjC;AACAf,IAAAA,eAAe,CAAC,KAAD,CAAf;AACD,GAtCgC,EAuCjC,CAACX,OAAD,EAAUC,SAAV,EAAqBC,UAArB,EAAiC+B,cAAjC,EAAiDlC,mBAAjD,CAvCiC,CAAnC;AA0CA,QAAMmC,qBAAqB,GAAGjE,MAAM,CAAU,KAAV,CAApC;AACA,QAAMwE,wBAAwB,GAAGxE,MAAM,CAAsB,IAAtB,CAAvC;AACA,QAAMyE,iBAAiB,GAAGzE,MAAM,CAAU,KAAV,CAAhC;AAEA,QAAM0E,iBAAiB,GAAG5E,WAAW,CAClC0D,KAAD,IAA8B;AAC5B,QAAI,CAACM,yBAAyB,CAACL,OAA/B,EAAwC;AACtC;AACD;;AAED,QAAIZ,qBAAqB,CAACY,OAA1B,EAAmC;AACjCvB,MAAAA,WAAW,SAAX,IAAAA,WAAW,WAAX,YAAAA,WAAW,CAAGvB,4BAA4B,CAAC6C,KAAD,CAA/B,CAAX;AACAZ,MAAAA,sBAAsB,CAACa,OAAvB,GAAiC,KAAjC;AACD;;AAED,QAAIc,mBAAmB,CAACd,OAAxB,EAAiC;AAC/BC,MAAAA,YAAY,CAACa,mBAAmB,CAACd,OAArB,CAAZ;AACAc,MAAAA,mBAAmB,CAACd,OAApB,GAA8B,IAA9B;AACD;AACF,GAfkC,EAgBnC,CAACvB,WAAD,CAhBmC,CAArC;AAmBA,QAAMqC,mBAAmB,GAAGvE,MAAM,CAAgB,IAAhB,CAAlC;AACA,QAAM2E,oBAAoB,GACxB,CAAC9C,cAAD,aAACA,cAAD,cAACA,cAAD,GAAmBZ,2BAAnB,KACCa,mBADD,aACCA,mBADD,cACCA,mBADD,GACwB,CADxB,CADF;AAIA,QAAM8C,oBAAoB,GAAG7E,OAAO,CAClC,MACEI,OAAO,CAAC0E,SAAR,GACGC,WADH,CACehE,SADf,EAC0B;AAD1B,GAEGiE,WAFH,CAEejE,SAFf,EAE0B;AAF1B,GAGGwC,oBAHH,CAGwB,KAHxB,EAIG0B,aAJH,CAIkBxB,KAAD,IAAW;AAAA;;AACxBS,IAAAA,qBAAqB,CAACR,OAAtB,GAAgC,IAAhC;AACA,6BAAAd,YAAY,CAACc,OAAb,gFAAsBwB,OAAtB,CAA8B,CAACC,EAAD,EAAKC,EAAL,EAASC,KAAT,EAAgBC,MAAhB,KAA2B;AAAA;;AACvD,UACE,CAAC3E,kBAAkB,CACjB;AACE0E,QAAAA,KADF;AAEEC,QAAAA;AAFF,OADiB,EAKjBtC,iBALiB,EAMjBS,KAAK,CAACc,cAAN,CAAqBgB,EAArB,CAAwB,CAAC,CAAzB,CANiB,CAAnB,IAQAzC,qBAAqB,CAACY,OARtB,IASAgB,iBAAiB,CAAChB,OAVpB,EAWE;AACAgB,QAAAA,iBAAiB,CAAChB,OAAlB,GAA4B,KAA5B;AACAe,QAAAA,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC;AACAQ,QAAAA,qBAAqB,CAACR,OAAtB,GAAgC,KAAhC;AACA;AACD;;AAEDZ,MAAAA,qBAAqB,CAACY,OAAtB,GAAgC,IAAhC,CAnBuD,CAqBvD;;AACA,UAAIc,mBAAmB,CAACd,OAApB,KAAgC,IAApC,EAA0C;AACxC;AACAc,QAAAA,mBAAmB,CAACd,OAApB,GAA8BE,UAAU,CACtC,MAAMe,iBAAiB,CAAClB,KAAD,CADe,EAEtCmB,oBAFsC,CAAxC;AAID;;AAED,UAAI7C,mBAAJ,EAAyB;AACvB+B,QAAAA,oBAAoB,CAACJ,OAArB,GAA+BE,UAAU,CAAC,MAAM;AAC9CK,UAAAA,cAAc,CAACrD,4BAA4B,CAAC6C,KAAD,CAA7B,CAAd;AACD,SAFwC,EAEtC1B,mBAFsC,CAAzC;AAGD,OAJD,MAIO;AACLkC,QAAAA,cAAc,CAACrD,4BAA4B,CAAC6C,KAAD,CAA7B,CAAd;AACD;;AAED,+BAAAgB,wBAAwB,CAACf,OAAzB,qFAAAe,wBAAwB;AACxBA,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,IAAnC;AACAQ,MAAAA,qBAAqB,CAACR,OAAtB,GAAgC,KAAhC;AACD,KAzCD;AA0CD,GAhDH,EAiDG8B,WAjDH,CAiDgB/B,KAAD,IAAW;AACtB,QAAIS,qBAAqB,CAACR,OAA1B,EAAmC;AACjCe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,MACjCS,eAAe,CAACvD,4BAA4B,CAAC6C,KAAD,CAA7B,CADjB;;AAEA;AACD,KALqB,CAMtB;AACA;;;AACA,QAAIO,oBAAoB,CAACN,OAArB,KAAiC,IAArC,EAA2C;AACzCX,MAAAA,0BAA0B,CAACW,OAA3B,GAAqC,IAArC;AACD;;AACDS,IAAAA,eAAe,CAACvD,4BAA4B,CAAC6C,KAAD,CAA7B,CAAf;AACD,GA7DH,EA8DGgC,kBA9DH,CA8DuBhC,KAAD,IAAW;AAC7BZ,IAAAA,sBAAsB,CAACa,OAAvB,GAAiC,KAAjC;;AAEA,QAAIQ,qBAAqB,CAACR,OAA1B,EAAmC;AACjCgB,MAAAA,iBAAiB,CAAChB,OAAlB,GAA4B,IAA5B;;AACAe,MAAAA,wBAAwB,CAACf,OAAzB,GAAmC,MACjCS,eAAe,CAACvD,4BAA4B,CAAC6C,KAAD,CAA7B,CADjB;;AAEA;AACD;;AAED,QACE,CAACX,qBAAqB,CAACY,OAAvB,IACAD,KAAK,CAACiC,UAAN,CAAiBpB,MAAjB,GAA0Bb,KAAK,CAACc,cAAN,CAAqBD,MAFjD,EAGE;AACA;AACD;;AAEDH,IAAAA,eAAe,CAACvD,4BAA4B,CAAC6C,KAAD,CAA7B,CAAf;AACD,GAhFH,CAFgC,EAmFlC,CACEkB,iBADF,EAEEC,oBAFF,EAGE5B,iBAHF,EAIEiB,cAJF,EAKEE,eALF,EAMEpC,mBANF,CAnFkC,CAApC,CAnLuD,CAgRvD;;AACA,QAAM4D,aAAa,GAAG3F,OAAO,CAC3B,MACEI,OAAO,CAACwF,MAAR,GACGpC,OADH,CACW,MAAM;AACb;AACA,QAAIlD,QAAQ,CAACuF,EAAT,KAAgB,SAAhB,IAA6BvF,QAAQ,CAACuF,EAAT,KAAgB,OAAjD,EAA0D;AACxD9B,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD;AACF,GANH,EAOGoC,OAPH,CAOW,MAAM;AACb,QAAIxF,QAAQ,CAACuF,EAAT,KAAgB,KAApB,EAA2B;AACzB9B,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD,KAHY,CAKb;;;AACA,QAAIpD,QAAQ,CAACuF,EAAT,KAAgB,KAApB,EAA2B;AACzB;AACD;;AAED,QAAI7B,oBAAoB,CAACN,OAAzB,EAAkC;AAChCK,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;;AAEA,UAAIZ,qBAAqB,CAACY,OAA1B,EAAmC;AACjCO,QAAAA,cAAc,CAACD,oBAAoB,CAACN,OAAtB,CAAd;AACAM,QAAAA,oBAAoB,CAACN,OAArB,GAA+B,IAA/B;AACD,OAHD,MAGO;AACLS,QAAAA,eAAe,CAACH,oBAAoB,CAACN,OAAtB,CAAf;AACAK,QAAAA,yBAAyB,CAACL,OAA1B,GAAoC,KAApC;AACD;;AAED;AACD;;AAED,QAAIZ,qBAAqB,CAACY,OAA1B,EAAmC;AACjCK,MAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACA;AACD;;AAED,QAAIX,0BAA0B,CAACW,OAA/B,EAAwC;AACtCX,MAAAA,0BAA0B,CAACW,OAA3B,GAAqC,KAArC;AACA;AACD;;AAEDK,IAAAA,yBAAyB,CAACL,OAA1B,GAAoC,IAApC;AACD,GA1CH,CAFyB,EA6C3B,CAACO,cAAD,EAAiBE,eAAjB,CA7C2B,CAA7B;AAgDA,QAAM4B,cAAc,GAAGlF,SAAS,CAC9BmC,iBAD8B,EAE9BC,8BAF8B,CAAhC;AAKA,QAAM+C,kBAAkB,GAAGxD,QAAQ,KAAK,IAAxC;AAEA,QAAMyD,QAAQ,GAAG,CAACN,aAAD,EAAgBd,oBAAhB,EAAsCzB,YAAtC,CAAjB;;AAEA,OAAK,MAAM8C,OAAX,IAAsBD,QAAtB,EAAgC;AAC9BC,IAAAA,OAAO,CAACC,OAAR,CAAgBH,kBAAhB;AACAE,IAAAA,OAAO,CAACE,OAAR,CAAgB,IAAhB;AACAF,IAAAA,OAAO,CAAC1E,OAAR,CAAgBuE,cAAhB;AACAG,IAAAA,OAAO,CAACG,uBAAR,CAAgC/F,QAAQ,CAACuF,EAAT,KAAgB,KAAhB,GAAwB,KAAxB,GAAgC,IAAhE;AACD,GA/UsD,CAiVvD;;;AACAF,EAAAA,aAAa,CAACnE,OAAd,CAAsBwB,iBAAtB;AAEA,QAAMkD,OAAO,GAAG9F,OAAO,CAACkG,YAAR,CAAqB,GAAGL,QAAxB,CAAhB,CApVuD,CAsVvD;;AACA,QAAMM,YAAkC,GACtCjG,QAAQ,CAACuF,EAAT,KAAgB,KAAhB,GAAwB;AAAEW,IAAAA,MAAM,EAAE;AAAV,GAAxB,GAAgD,EADlD;AAGA,QAAMC,SAAS,GACb,OAAOrE,KAAP,KAAiB,UAAjB,GAA8BA,KAAK,CAAC;AAAEsE,IAAAA,OAAO,EAAEhE;AAAX,GAAD,CAAnC,GAAiEN,KADnE;AAGA,QAAMuE,YAAY,GAChB,OAAOtE,QAAP,KAAoB,UAApB,GACIA,QAAQ,CAAC;AAAEqE,IAAAA,OAAO,EAAEhE;AAAX,GAAD,CADZ,GAEIL,QAHN;AAKA,QAAMuE,WAAW,GAAG5G,OAAO,CAAC,MAAM;AAAA;;AAChC,QAAIoB,SAAS,KAAK,IAAlB,EAAwB;AACtBA,MAAAA,SAAS,GAAGJ,QAAQ,EAApB;AACD;;AAED,UAAM6F,kBAAkB,GAAGtE,cAAc,GAAGuE,SAAH,GAAe,aAAxD;AACA,UAAMC,sBAAsB,4BAAGxE,cAAH,aAAGA,cAAH,uBAAGA,cAAc,CAAEyE,KAAnB,yEAA4BH,kBAAxD;AACA,WAAOzF,SAAS,GACZ2F,sBADY,GAEZxG,YAAY,CAACwG,sBAAD,CAFhB;AAGD,GAV0B,EAUxB,CAACxE,cAAD,CAVwB,CAA3B;AAYA,sBACE,oBAAC,eAAD;AAAiB,IAAA,OAAO,EAAE2D;AAA1B,kBACE,oBAAC,YAAD,eACMzD,cADN;AAEE,IAAA,GAAG,EAAEG,YAFP;AAGE,IAAA,OAAO,EAAEmD,cAHX;AAIE,IAAA,OAAO,EAAEC,kBAJX;AAKE,IAAA,kBAAkB,EAAE1D,oBAAF,aAAEA,oBAAF,cAAEA,oBAAF,GAA0BwE,SAL9C;AAME,IAAA,WAAW,EAAEF,WANf;AAOE,IAAA,YAAY,2BAAErE,cAAF,aAAEA,cAAF,uBAAEA,cAAc,CAAE0E,MAAlB,yEAA4BH,SAP1C;AAQE,IAAA,KAAK,EAAE,CAACP,YAAD,EAAeE,SAAf,CART;AASE,IAAA,gBAAgB,EAAEtF,WAAW,GAAGa,OAAH,GAAa8E,SAT5C;AAUE,IAAA,kBAAkB,EAAE3F,WAAW,GAAGc,SAAH,GAAe6E,SAVhD;AAWE,IAAA,mBAAmB,EAAE3F,WAAW,GAAGe,UAAH,GAAgB4E,SAXlD;AAYE,IAAA,oBAAoB,EAAE3F,WAAW,GAAGgB,WAAH,GAAiB2E;AAZpD,MAaGH,YAbH,EAcGO,OAAO,gBACN,oBAAC,qBAAD;AAAuB,IAAA,KAAK,EAAC,KAA7B;AAAmC,IAAA,OAAO,EAAElE;AAA5C,IADM,GAEJ,IAhBN,CADF,CADF;AAsBD", "sourcesContent": ["import React, { useCallback, useMemo, useRef, useState } from 'react';\nimport { GestureObjects as Gesture } from '../../handlers/gestures/gestureObjects';\nimport { GestureDetector } from '../../handlers/gestures/GestureDetector';\nimport { PressableEvent, PressableProps } from './PressableProps';\nimport {\n  Insets,\n  Platform,\n  StyleProp,\n  View,\n  ViewStyle,\n  processColor,\n} from 'react-native';\nimport NativeButton from '../GestureHandlerButton';\nimport {\n  numberAsInset,\n  gestureToPressableEvent,\n  isTouchWithinInset,\n  gestureTouchToPressableEvent,\n  addInsets,\n} from './utils';\nimport { PressabilityDebugView } from '../../handlers/PressabilityDebugView';\nimport { GestureTouchEvent } from '../../handlers/gestureHandlerCommon';\nimport { INT32_MAX, isFabric, isTestEnv } from '../../utils';\n\nconst DEFAULT_LONG_PRESS_DURATION = 500;\nconst IS_TEST_ENV = isTestEnv();\n\nlet IS_FABRIC: null | boolean = null;\n\nexport default function Pressable(props: PressableProps) {\n  const {\n    testOnly_pressed,\n    hitSlop,\n    pressRetentionOffset,\n    delayHoverIn,\n    onHoverIn,\n    delayHoverOut,\n    onHoverOut,\n    delayLongPress,\n    unstable_pressDelay,\n    onPress,\n    onPressIn,\n    onPressOut,\n    onLongPress,\n    style,\n    children,\n    android_disableSound,\n    android_ripple,\n    disabled,\n    ...remainingProps\n  } = props;\n\n  const [pressedState, setPressedState] = useState(testOnly_pressed ?? false);\n\n  const pressableRef = useRef<View>(null);\n\n  // Disabled when onLongPress has been called\n  const isPressCallbackEnabled = useRef<boolean>(true);\n  const hasPassedBoundsChecks = useRef<boolean>(false);\n  const shouldPreventNativeEffects = useRef<boolean>(false);\n\n  const normalizedHitSlop: Insets = useMemo(\n    () =>\n      typeof hitSlop === 'number' ? numberAsInset(hitSlop) : (hitSlop ?? {}),\n    [hitSlop]\n  );\n\n  const normalizedPressRetentionOffset: Insets = useMemo(\n    () =>\n      typeof pressRetentionOffset === 'number'\n        ? numberAsInset(pressRetentionOffset)\n        : (pressRetentionOffset ?? {}),\n    [pressRetentionOffset]\n  );\n\n  const hoverInTimeout = useRef<number | null>(null);\n  const hoverOutTimeout = useRef<number | null>(null);\n\n  const hoverGesture = useMemo(\n    () =>\n      Gesture.Hover()\n        .manualActivation(true) // Stops Hover from blocking Native gesture activation on web\n        .cancelsTouchesInView(false)\n        .onBegin((event) => {\n          if (hoverOutTimeout.current) {\n            clearTimeout(hoverOutTimeout.current);\n          }\n          if (delayHoverIn) {\n            hoverInTimeout.current = setTimeout(\n              () => onHoverIn?.(gestureToPressableEvent(event)),\n              delayHoverIn\n            );\n            return;\n          }\n          onHoverIn?.(gestureToPressableEvent(event));\n        })\n        .onFinalize((event) => {\n          if (hoverInTimeout.current) {\n            clearTimeout(hoverInTimeout.current);\n          }\n          if (delayHoverOut) {\n            hoverOutTimeout.current = setTimeout(\n              () => onHoverOut?.(gestureToPressableEvent(event)),\n              delayHoverOut\n            );\n            return;\n          }\n          onHoverOut?.(gestureToPressableEvent(event));\n        }),\n    [delayHoverIn, delayHoverOut, onHoverIn, onHoverOut]\n  );\n\n  const pressDelayTimeoutRef = useRef<number | null>(null);\n  const isTouchPropagationAllowed = useRef<boolean>(false);\n\n  // iOS only: due to varying flow of gestures, events sometimes have to be saved for later use\n  const deferredEventPayload = useRef<PressableEvent | null>(null);\n\n  const pressInHandler = useCallback(\n    (event: PressableEvent) => {\n      if (handlingOnTouchesDown.current) {\n        deferredEventPayload.current = event;\n      }\n\n      if (!isTouchPropagationAllowed.current) {\n        return;\n      }\n\n      deferredEventPayload.current = null;\n\n      onPressIn?.(event);\n      isPressCallbackEnabled.current = true;\n      pressDelayTimeoutRef.current = null;\n      setPressedState(true);\n    },\n    [onPressIn]\n  );\n\n  const pressOutHandler = useCallback(\n    (event: PressableEvent) => {\n      if (\n        !hasPassedBoundsChecks.current ||\n        event.nativeEvent.touches.length >\n          event.nativeEvent.changedTouches.length\n      ) {\n        return;\n      }\n\n      if (unstable_pressDelay && pressDelayTimeoutRef.current !== null) {\n        // When delay is preemptively finished by lifting touches,\n        // we want to immediately activate it's effects - pressInHandler,\n        // even though we are located at the pressOutHandler\n        clearTimeout(pressDelayTimeoutRef.current);\n        pressInHandler(event);\n      }\n\n      if (deferredEventPayload.current) {\n        onPressIn?.(deferredEventPayload.current);\n        deferredEventPayload.current = null;\n      }\n\n      onPressOut?.(event);\n\n      if (isPressCallbackEnabled.current) {\n        onPress?.(event);\n      }\n\n      if (longPressTimeoutRef.current) {\n        clearTimeout(longPressTimeoutRef.current);\n        longPressTimeoutRef.current = null;\n      }\n\n      isTouchPropagationAllowed.current = false;\n      hasPassedBoundsChecks.current = false;\n      isPressCallbackEnabled.current = true;\n      setPressedState(false);\n    },\n    [onPress, onPressIn, onPressOut, pressInHandler, unstable_pressDelay]\n  );\n\n  const handlingOnTouchesDown = useRef<boolean>(false);\n  const onEndHandlingTouchesDown = useRef<(() => void) | null>(null);\n  const cancelledMidPress = useRef<boolean>(false);\n\n  const activateLongPress = useCallback(\n    (event: GestureTouchEvent) => {\n      if (!isTouchPropagationAllowed.current) {\n        return;\n      }\n\n      if (hasPassedBoundsChecks.current) {\n        onLongPress?.(gestureTouchToPressableEvent(event));\n        isPressCallbackEnabled.current = false;\n      }\n\n      if (longPressTimeoutRef.current) {\n        clearTimeout(longPressTimeoutRef.current);\n        longPressTimeoutRef.current = null;\n      }\n    },\n    [onLongPress]\n  );\n\n  const longPressTimeoutRef = useRef<number | null>(null);\n  const longPressMinDuration =\n    (delayLongPress ?? DEFAULT_LONG_PRESS_DURATION) +\n    (unstable_pressDelay ?? 0);\n\n  const pressAndTouchGesture = useMemo(\n    () =>\n      Gesture.LongPress()\n        .minDuration(INT32_MAX) // Stops long press from blocking native gesture\n        .maxDistance(INT32_MAX) // Stops long press from cancelling after set distance\n        .cancelsTouchesInView(false)\n        .onTouchesDown((event) => {\n          handlingOnTouchesDown.current = true;\n          pressableRef.current?.measure((_x, _y, width, height) => {\n            if (\n              !isTouchWithinInset(\n                {\n                  width,\n                  height,\n                },\n                normalizedHitSlop,\n                event.changedTouches.at(-1)\n              ) ||\n              hasPassedBoundsChecks.current ||\n              cancelledMidPress.current\n            ) {\n              cancelledMidPress.current = false;\n              onEndHandlingTouchesDown.current = null;\n              handlingOnTouchesDown.current = false;\n              return;\n            }\n\n            hasPassedBoundsChecks.current = true;\n\n            // In case of multiple touches, the first one starts long press gesture\n            if (longPressTimeoutRef.current === null) {\n              // Start long press gesture timer\n              longPressTimeoutRef.current = setTimeout(\n                () => activateLongPress(event),\n                longPressMinDuration\n              );\n            }\n\n            if (unstable_pressDelay) {\n              pressDelayTimeoutRef.current = setTimeout(() => {\n                pressInHandler(gestureTouchToPressableEvent(event));\n              }, unstable_pressDelay);\n            } else {\n              pressInHandler(gestureTouchToPressableEvent(event));\n            }\n\n            onEndHandlingTouchesDown.current?.();\n            onEndHandlingTouchesDown.current = null;\n            handlingOnTouchesDown.current = false;\n          });\n        })\n        .onTouchesUp((event) => {\n          if (handlingOnTouchesDown.current) {\n            onEndHandlingTouchesDown.current = () =>\n              pressOutHandler(gestureTouchToPressableEvent(event));\n            return;\n          }\n          // On iOS, short taps will make LongPress gesture call onTouchesUp before Native gesture calls onStart\n          // This variable ensures that onStart isn't detected as the first gesture since Pressable is pressed.\n          if (deferredEventPayload.current !== null) {\n            shouldPreventNativeEffects.current = true;\n          }\n          pressOutHandler(gestureTouchToPressableEvent(event));\n        })\n        .onTouchesCancelled((event) => {\n          isPressCallbackEnabled.current = false;\n\n          if (handlingOnTouchesDown.current) {\n            cancelledMidPress.current = true;\n            onEndHandlingTouchesDown.current = () =>\n              pressOutHandler(gestureTouchToPressableEvent(event));\n            return;\n          }\n\n          if (\n            !hasPassedBoundsChecks.current ||\n            event.allTouches.length > event.changedTouches.length\n          ) {\n            return;\n          }\n\n          pressOutHandler(gestureTouchToPressableEvent(event));\n        }),\n    [\n      activateLongPress,\n      longPressMinDuration,\n      normalizedHitSlop,\n      pressInHandler,\n      pressOutHandler,\n      unstable_pressDelay,\n    ]\n  );\n\n  // RNButton is placed inside ButtonGesture to enable Android's ripple and to capture non-propagating events\n  const buttonGesture = useMemo(\n    () =>\n      Gesture.Native()\n        .onBegin(() => {\n          // Android sets BEGAN state on press down\n          if (Platform.OS === 'android' || Platform.OS === 'macos') {\n            isTouchPropagationAllowed.current = true;\n          }\n        })\n        .onStart(() => {\n          if (Platform.OS === 'web') {\n            isTouchPropagationAllowed.current = true;\n          }\n\n          // iOS sets ACTIVE state on press down\n          if (Platform.OS !== 'ios') {\n            return;\n          }\n\n          if (deferredEventPayload.current) {\n            isTouchPropagationAllowed.current = true;\n\n            if (hasPassedBoundsChecks.current) {\n              pressInHandler(deferredEventPayload.current);\n              deferredEventPayload.current = null;\n            } else {\n              pressOutHandler(deferredEventPayload.current);\n              isTouchPropagationAllowed.current = false;\n            }\n\n            return;\n          }\n\n          if (hasPassedBoundsChecks.current) {\n            isTouchPropagationAllowed.current = true;\n            return;\n          }\n\n          if (shouldPreventNativeEffects.current) {\n            shouldPreventNativeEffects.current = false;\n            return;\n          }\n\n          isTouchPropagationAllowed.current = true;\n        }),\n    [pressInHandler, pressOutHandler]\n  );\n\n  const appliedHitSlop = addInsets(\n    normalizedHitSlop,\n    normalizedPressRetentionOffset\n  );\n\n  const isPressableEnabled = disabled !== true;\n\n  const gestures = [buttonGesture, pressAndTouchGesture, hoverGesture];\n\n  for (const gesture of gestures) {\n    gesture.enabled(isPressableEnabled);\n    gesture.runOnJS(true);\n    gesture.hitSlop(appliedHitSlop);\n    gesture.shouldCancelWhenOutside(Platform.OS === 'web' ? false : true);\n  }\n\n  // Uses different hitSlop, to activate on hitSlop area instead of pressRetentionOffset area\n  buttonGesture.hitSlop(normalizedHitSlop);\n\n  const gesture = Gesture.Simultaneous(...gestures);\n\n  // `cursor: 'pointer'` on `RNButton` crashes iOS\n  const pointerStyle: StyleProp<ViewStyle> =\n    Platform.OS === 'web' ? { cursor: 'pointer' } : {};\n\n  const styleProp =\n    typeof style === 'function' ? style({ pressed: pressedState }) : style;\n\n  const childrenProp =\n    typeof children === 'function'\n      ? children({ pressed: pressedState })\n      : children;\n\n  const rippleColor = useMemo(() => {\n    if (IS_FABRIC === null) {\n      IS_FABRIC = isFabric();\n    }\n\n    const defaultRippleColor = android_ripple ? undefined : 'transparent';\n    const unprocessedRippleColor = android_ripple?.color ?? defaultRippleColor;\n    return IS_FABRIC\n      ? unprocessedRippleColor\n      : processColor(unprocessedRippleColor);\n  }, [android_ripple]);\n\n  return (\n    <GestureDetector gesture={gesture}>\n      <NativeButton\n        {...remainingProps}\n        ref={pressableRef}\n        hitSlop={appliedHitSlop}\n        enabled={isPressableEnabled}\n        touchSoundDisabled={android_disableSound ?? undefined}\n        rippleColor={rippleColor}\n        rippleRadius={android_ripple?.radius ?? undefined}\n        style={[pointerStyle, styleProp]}\n        testOnly_onPress={IS_TEST_ENV ? onPress : undefined}\n        testOnly_onPressIn={IS_TEST_ENV ? onPressIn : undefined}\n        testOnly_onPressOut={IS_TEST_ENV ? onPressOut : undefined}\n        testOnly_onLongPress={IS_TEST_ENV ? onLongPress : undefined}>\n        {childrenProp}\n        {__DEV__ ? (\n          <PressabilityDebugView color=\"red\" hitSlop={normalizedHitSlop} />\n        ) : null}\n      </NativeButton>\n    </GestureDetector>\n  );\n}\n"]}
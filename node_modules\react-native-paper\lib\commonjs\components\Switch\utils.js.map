{"version": 3, "names": ["_reactNative", "require", "_color", "_interopRequireDefault", "_colors", "e", "__esModule", "default", "getCheckedColor", "theme", "color", "isV3", "colors", "primary", "accent", "getThumbTintColor", "disabled", "value", "checkedColor", "isIOS", "Platform", "OS", "undefined", "dark", "grey800", "grey400", "grey50", "getOnTintColor", "setColor", "white", "alpha", "rgb", "string", "black", "grey700", "getSwitchColor", "onTintColor", "thumbTintColor", "exports"], "sourceRoot": "../../../../src", "sources": ["components/Switch/utils.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAC,sBAAA,CAAAF,OAAA;AAEA,IAAAG,OAAA,GAAAH,OAAA;AAOuC,SAAAE,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AASvC,MAAMG,eAAe,GAAGA,CAAC;EACvBC,KAAK;EACLC;AAIF,CAAC,KAAK;EACJ,IAAIA,KAAK,EAAE;IACT,OAAOA,KAAK;EACd;EAEA,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACC,OAAO;EAC7B;EAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,MAAM;AAC5B,CAAC;AAED,MAAMC,iBAAiB,GAAGA,CAAC;EACzBN,KAAK;EACLO,QAAQ;EACRC,KAAK;EACLC;AACoC,CAAC,KAAK;EAC1C,MAAMC,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;EAEnC,IAAIF,KAAK,EAAE;IACT,OAAOG,SAAS;EAClB;EAEA,IAAIN,QAAQ,EAAE;IACZ,IAAIP,KAAK,CAACc,IAAI,EAAE;MACd,OAAOC,eAAO;IAChB;IACA,OAAOC,eAAO;EAChB;EAEA,IAAIR,KAAK,EAAE;IACT,OAAOC,YAAY;EACrB;EAEA,IAAIT,KAAK,CAACc,IAAI,EAAE;IACd,OAAOE,eAAO;EAChB;EACA,OAAOC,cAAM;AACf,CAAC;AAED,MAAMC,cAAc,GAAGA,CAAC;EACtBlB,KAAK;EACLO,QAAQ;EACRC,KAAK;EACLC;AACoC,CAAC,KAAK;EAC1C,MAAMC,KAAK,GAAGC,qBAAQ,CAACC,EAAE,KAAK,KAAK;EAEnC,IAAIF,KAAK,EAAE;IACT,OAAOD,YAAY;EACrB;EAEA,IAAIF,QAAQ,EAAE;IACZ,IAAIP,KAAK,CAACc,IAAI,EAAE;MACd,IAAId,KAAK,CAACE,IAAI,EAAE;QACd,OAAO,IAAAiB,cAAQ,EAACC,aAAK,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;MACnD;MACA,OAAO,IAAAJ,cAAQ,EAACC,aAAK,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IAClD;IACA,OAAO,IAAAJ,cAAQ,EAACK,aAAK,CAAC,CAACH,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACnD;EAEA,IAAIf,KAAK,EAAE;IACT,OAAO,IAAAW,cAAQ,EAACV,YAAY,CAAC,CAACY,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACzD;EAEA,IAAIvB,KAAK,CAACc,IAAI,EAAE;IACd,OAAOW,eAAO;EAChB;EACA,OAAO,oBAAoB;AAC7B,CAAC;AAEM,MAAMC,cAAc,GAAGA,CAAC;EAC7B1B,KAAK;EACLO,QAAQ;EACRC,KAAK;EACLP;AAC8B,CAAC,KAAK;EACpC,MAAMQ,YAAY,GAAGV,eAAe,CAAC;IAAEC,KAAK;IAAEC;EAAM,CAAC,CAAC;EAEtD,OAAO;IACL0B,WAAW,EAAET,cAAc,CAAC;MAAElB,KAAK;MAAEO,QAAQ;MAAEC,KAAK;MAAEC;IAAa,CAAC,CAAC;IACrEmB,cAAc,EAAEtB,iBAAiB,CAAC;MAAEN,KAAK;MAAEO,QAAQ;MAAEC,KAAK;MAAEC;IAAa,CAAC,CAAC;IAC3EA;EACF,CAAC;AACH,CAAC;AAACoB,OAAA,CAAAH,cAAA,GAAAA,cAAA", "ignoreList": []}
import { createClient } from '@supabase/supabase-js';

// Replace these with your actual Supabase project URL and anon key
const supabaseUrl = 'YOUR_SUPABASE_URL';
const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';

export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});

// Database table names
export const TABLES = {
  USERS: 'users',
  SPOTS: 'spots',
  SPOT_IMAGES: 'spot_images',
  RATINGS: 'ratings',
  COMMENTS: 'comments',
  GROUPS: 'groups',
  GROUP_MEMBERS: 'group_members',
  USER_ACHIEVEMENTS: 'user_achievements',
  ACHIEVEMENTS: 'achievements',
  FAVORITES: 'favorites',
};

// Storage bucket names
export const BUCKETS = {
  SPOT_IMAGES: 'spot-images',
  PROFILE_IMAGES: 'profile-images',
};

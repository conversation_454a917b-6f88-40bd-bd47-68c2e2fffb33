{"version": 3, "names": ["_<PERSON>r<PERSON><PERSON><PERSON>", "require", "_usePagerView", "Object", "keys", "for<PERSON>ach", "key", "prototype", "hasOwnProperty", "call", "_exportNames", "exports", "defineProperty", "enumerable", "get", "_default", "default", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "sources": ["index.tsx"], "sourcesContent": ["import type * as ReactNative from 'react-native';\nimport { PagerView } from './PagerView';\nexport default PagerView;\nexport * from './usePagerView';\n\nimport type {\n  OnPageScrollEventData as PagerViewOnPageScrollEventData,\n  OnPageSelectedEventData as PagerViewOnPageSelectedEventData,\n  OnPageScrollStateChangedEventData as PageScrollStateChangedNativeEventData,\n  NativeProps,\n} from './PagerViewNativeComponent';\n\nexport type {\n  PagerViewOnPageScrollEventData,\n  PagerViewOnPageSelectedEventData,\n  PageScrollStateChangedNativeEventData,\n  NativeProps as PagerViewProps,\n};\n\nexport type PagerViewOnPageScrollEvent =\n  ReactNative.NativeSyntheticEvent<PagerViewOnPageScrollEventData>;\n\nexport type PagerViewOnPageSelectedEvent =\n  ReactNative.NativeSyntheticEvent<PagerViewOnPageSelectedEventData>;\n\nexport type PageScrollStateChangedNativeEvent =\n  ReactNative.NativeSyntheticEvent<PageScrollStateChangedNativeEventData>;\n"], "mappings": ";;;;;;;AACA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,aAAA,GAAAD,OAAA;AAAAE,MAAA,CAAAC,IAAA,CAAAF,aAAA,EAAAG,OAAA,WAAAC,GAAA;EAAA,IAAAA,GAAA,kBAAAA,GAAA;EAAA,IAAAH,MAAA,CAAAI,SAAA,CAAAC,cAAA,CAAAC,IAAA,CAAAC,YAAA,EAAAJ,GAAA;EAAA,IAAAA,GAAA,IAAAK,OAAA,IAAAA,OAAA,CAAAL,GAAA,MAAAJ,aAAA,CAAAI,GAAA;EAAAH,MAAA,CAAAS,cAAA,CAAAD,OAAA,EAAAL,GAAA;IAAAO,UAAA;IAAAC,GAAA,WAAAA,CAAA;MAAA,OAAAZ,aAAA,CAAAI,GAAA;IAAA;EAAA;AAAA;AAA+B,IAAAS,QAAA,GAAAJ,OAAA,CAAAK,OAAA,GADhBC,oBAAS", "ignoreList": []}
{"version": 3, "names": ["_Checkbox", "_interopRequireDefault", "require", "_CheckboxAndroid", "_CheckboxIOS", "_CheckboxItem", "e", "__esModule", "default", "Checkbox", "Object", "assign", "CheckboxComponent", "<PERSON><PERSON>", "CheckboxItem", "Android", "CheckboxAndroid", "IOS", "CheckboxIOS", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/Checkbox/index.ts"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,gBAAA,GAAAF,sBAAA,CAAAC,OAAA;AACA,IAAAE,YAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,aAAA,GAAAJ,sBAAA,CAAAC,OAAA;AAA0C,SAAAD,uBAAAK,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE1C,MAAMG,QAAQ,GAAGC,MAAM,CAACC,MAAM;AAC5B;AACAC,iBAAiB,EACjB;EACE;EACAC,IAAI,EAAEC,qBAAY;EAClB;EACAC,OAAO,EAAEC,wBAAe;EACxB;EACAC,GAAG,EAAEC;AACP,CACF,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAZ,OAAA,GAEaC,QAAQ", "ignoreList": []}
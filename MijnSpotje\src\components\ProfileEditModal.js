import React, { useState, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  TextInput,
  Alert,
} from 'react-native';
import { useTranslation } from '../i18n/useTranslation';

const ProfileEditModal = ({ visible, onClose, onSave, isDark, currentProfile }) => {
  const { t } = useTranslation();
  const [formData, setFormData] = useState({
    name: currentProfile?.name || '',
    email: currentProfile?.email || '',
    bio: currentProfile?.bio || '',
  });
  const [errors, setErrors] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = useCallback(() => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = t('common.required');
    } else if (formData.name.length < 2) {
      newErrors.name = 'Name must be at least 2 characters';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = t('common.required');
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address';
    }
    
    if (formData.bio.length > 150) {
      newErrors.bio = 'Bio can be maximum 150 characters';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, t]);

  const handleSave = useCallback(async () => {
    if (validateForm()) {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        onSave({
          ...currentProfile,
          ...formData,
          updatedAt: new Date().toISOString(),
        });
        
        Alert.alert(t('common.success'), 'Profile updated successfully');
        onClose();
      } catch (error) {
        Alert.alert(t('common.error'), 'Failed to update profile');
      } finally {
        setIsLoading(false);
      }
    }
  }, [formData, validateForm, onSave, onClose, currentProfile, t]);

  const handleClose = useCallback(() => {
    setFormData({
      name: currentProfile?.name || '',
      email: currentProfile?.email || '',
      bio: currentProfile?.bio || '',
    });
    setErrors({});
    onClose();
  }, [onClose, currentProfile]);

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, isDark && styles.containerDark]}>
        <View style={[styles.header, isDark && styles.headerDark]}>
          <TouchableOpacity onPress={handleClose} disabled={isLoading}>
            <Text style={[styles.button, styles.cancelButton]}>{t('common.cancel')}</Text>
          </TouchableOpacity>
          <Text style={[styles.title, isDark && styles.textDark]}>
            {t('settings.account.editProfile')}
          </Text>
          <TouchableOpacity onPress={handleSave} disabled={isLoading}>
            <Text style={[styles.button, styles.saveButton, isLoading && styles.buttonDisabled]}>
              {isLoading ? t('common.loading') : t('common.save')}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.avatarSection}>
            <View style={[styles.avatar, isDark && styles.avatarDark]}>
              <Text style={styles.avatarText}>👤</Text>
            </View>
            <TouchableOpacity style={styles.changeAvatarButton}>
              <Text style={[styles.changeAvatarText, isDark && styles.textSecondaryDark]}>
                Change Avatar
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Name *</Text>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                errors.name && styles.inputError
              ]}
              value={formData.name}
              onChangeText={(text) => setFormData(prev => ({ ...prev, name: text }))}
              placeholder="Enter your name"
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              editable={!isLoading}
            />
            {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Email *</Text>
            <TextInput
              style={[
                styles.input,
                isDark && styles.inputDark,
                errors.email && styles.inputError
              ]}
              value={formData.email}
              onChangeText={(text) => setFormData(prev => ({ ...prev, email: text }))}
              placeholder="Enter your email"
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              keyboardType="email-address"
              autoCapitalize="none"
              editable={!isLoading}
            />
            {errors.email && <Text style={styles.errorText}>{errors.email}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Bio</Text>
            <TextInput
              style={[
                styles.textArea,
                isDark && styles.inputDark,
                errors.bio && styles.inputError
              ]}
              value={formData.bio}
              onChangeText={(text) => setFormData(prev => ({ ...prev, bio: text }))}
              placeholder="Tell us about yourself..."
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              multiline
              numberOfLines={4}
              maxLength={150}
              editable={!isLoading}
            />
            {errors.bio && <Text style={styles.errorText}>{errors.bio}</Text>}
            <Text style={[styles.charCount, isDark && styles.charCountDark]}>
              {formData.bio.length}/150
            </Text>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  textDark: {
    color: '#ffffff',
  },
  button: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    color: '#ff3b30',
  },
  saveButton: {
    color: '#007AFF',
  },
  buttonDisabled: {
    opacity: 0.5,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  avatarSection: {
    alignItems: 'center',
    marginBottom: 30,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatarDark: {
    backgroundColor: '#2c2c2e',
  },
  avatarText: {
    fontSize: 40,
  },
  changeAvatarButton: {
    padding: 8,
  },
  changeAvatarText: {
    fontSize: 14,
    color: '#007AFF',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  labelDark: {
    color: '#ffffff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
  },
  inputDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
    color: '#ffffff',
  },
  inputError: {
    borderColor: '#ff3b30',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
    height: 80,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#ff3b30',
    fontSize: 12,
    marginTop: 4,
  },
  charCount: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
    marginTop: 4,
  },
  charCountDark: {
    color: '#8e8e93',
  },
});

export default ProfileEditModal;

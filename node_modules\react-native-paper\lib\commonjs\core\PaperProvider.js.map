{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_SafeAreaProviderCompat", "_interopRequireDefault", "_settings", "_theming", "_MaterialCommunityIcon", "_PortalHost", "_addEventListener", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "PaperProvider", "props", "isOnlyVersionInTheme", "theme", "keys", "length", "version", "colorSchemeName", "Appearance", "getColorScheme", "reduceMotionEnabled", "setReduceMotionEnabled", "useState", "colorScheme", "setColorScheme", "handleAppearanceChange", "preferences", "useEffect", "subscription", "addEventListener", "AccessibilityInfo", "_subscription", "remove", "appearanceSubscription", "addChangeListener", "removeChangeListener", "useMemo", "_props$theme", "_props$theme2", "themeVersion", "scheme", "defaultThemeBase", "defaultThemesByVersion", "extendedThemeBase", "animation", "scale", "isV3", "children", "settings", "settingsValue", "icon", "MaterialCommunityIcon", "rippleEffectEnabled", "createElement", "Provider", "value", "ThemeProvider", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["core/PaperProvider.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAOA,IAAAE,uBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,SAAA,GAAAJ,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AACA,IAAAM,sBAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,WAAA,GAAAJ,sBAAA,CAAAH,OAAA;AAEA,IAAAQ,iBAAA,GAAAR,OAAA;AAA6D,SAAAG,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAV,wBAAAU,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAd,uBAAA,YAAAA,CAAAU,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAQ7D,MAAMgB,aAAa,GAAIC,KAAY,IAAK;EACtC,MAAMC,oBAAoB,GACxBD,KAAK,CAACE,KAAK,IAAIN,MAAM,CAACO,IAAI,CAACH,KAAK,CAACE,KAAK,CAAC,CAACE,MAAM,KAAK,CAAC,IAAIJ,KAAK,CAACE,KAAK,CAACG,OAAO;EAE7E,MAAMC,eAAe,GAClB,CAAC,CAACN,KAAK,CAACE,KAAK,IAAID,oBAAoB,MAAKM,uBAAU,aAAVA,uBAAU,uBAAVA,uBAAU,CAAEC,cAAc,CAAC,CAAC,KACvE,OAAO;EAET,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GACjDzC,KAAK,CAAC0C,QAAQ,CAAU,KAAK,CAAC;EAChC,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GACjC5C,KAAK,CAAC0C,QAAQ,CAAkBL,eAAe,CAAC;EAElD,MAAMQ,sBAAsB,GAC1BC,WAA6C,IAC1C;IACH,MAAM;MAAEH;IAAY,CAAC,GAAGG,WAAW;IACnCF,cAAc,CAACD,WAAW,CAAC;EAC7B,CAAC;EAED3C,KAAK,CAAC+C,SAAS,CAAC,MAAM;IACpB,IAAIC,YAAiD;IAErD,IAAI,CAACjB,KAAK,CAACE,KAAK,EAAE;MAChBe,YAAY,GAAG,IAAAC,kCAAgB,EAC7BC,8BAAiB,EACjB,qBAAqB,EACrBT,sBACF,CAAC;IACH;IACA,OAAO,MAAM;MACX,IAAI,CAACV,KAAK,CAACE,KAAK,EAAE;QAAA,IAAAkB,aAAA;QAChB,CAAAA,aAAA,GAAAH,YAAY,cAAAG,aAAA,eAAZA,aAAA,CAAcC,MAAM,CAAC,CAAC;MACxB;IACF,CAAC;EACH,CAAC,EAAE,CAACrB,KAAK,CAACE,KAAK,CAAC,CAAC;EAEjBjC,KAAK,CAAC+C,SAAS,CAAC,MAAM;IACpB,IAAIM,sBAA2D;IAC/D,IAAI,CAACtB,KAAK,CAACE,KAAK,IAAID,oBAAoB,EAAE;MACxCqB,sBAAsB,GAAGf,uBAAU,aAAVA,uBAAU,uBAAVA,uBAAU,CAAEgB,iBAAiB,CACpDT,sBACF,CAAwC;IAC1C;IACA,OAAO,MAAM;MACX,IAAI,CAACd,KAAK,CAACE,KAAK,IAAID,oBAAoB,EAAE;QACxC,IAAIqB,sBAAsB,EAAE;UAC1BA,sBAAsB,CAACD,MAAM,CAAC,CAAC;QACjC,CAAC,MAAM;UACL;UACAd,uBAAU,aAAVA,uBAAU,eAAVA,uBAAU,CAAEiB,oBAAoB,CAACV,sBAAsB,CAAC;QAC1D;MACF;IACF,CAAC;EACH,CAAC,EAAE,CAACd,KAAK,CAACE,KAAK,EAAED,oBAAoB,CAAC,CAAC;EAEvC,MAAMC,KAAK,GAAGjC,KAAK,CAACwD,OAAO,CAAC,MAAM;IAAA,IAAAC,YAAA,EAAAC,aAAA;IAChC,MAAMC,YAAY,GAAG,EAAAF,YAAA,GAAA1B,KAAK,CAACE,KAAK,cAAAwB,YAAA,uBAAXA,YAAA,CAAarB,OAAO,KAAI,CAAC;IAC9C,MAAMwB,MAAM,GAAGjB,WAAW,IAAI,OAAO;IACrC,MAAMkB,gBAAgB,GAAGC,+BAAsB,CAACH,YAAY,CAAC,CAACC,MAAM,CAAC;IAErE,MAAMG,iBAAiB,GAAG;MACxB,GAAGF,gBAAgB;MACnB,GAAG9B,KAAK,CAACE,KAAK;MACdG,OAAO,EAAEuB,YAAY;MACrBK,SAAS,EAAE;QACT,KAAAN,aAAA,GAAG3B,KAAK,CAACE,KAAK,cAAAyB,aAAA,uBAAXA,aAAA,CAAaM,SAAS;QACzBC,KAAK,EAAEzB,mBAAmB,GAAG,CAAC,GAAG;MACnC;IACF,CAAC;IAED,OAAO;MACL,GAAGuB,iBAAiB;MACpBG,IAAI,EAAEH,iBAAiB,CAAC3B,OAAO,KAAK;IACtC,CAAC;EACH,CAAC,EAAE,CAACO,WAAW,EAAEZ,KAAK,CAACE,KAAK,EAAEO,mBAAmB,CAAC,CAAC;EAEnD,MAAM;IAAE2B,QAAQ;IAAEC;EAAS,CAAC,GAAGrC,KAAK;EAEpC,MAAMsC,aAAa,GAAGrE,KAAK,CAACwD,OAAO,CACjC,OAAO;IACLc,IAAI,EAAEC,8BAAqB;IAC3BC,mBAAmB,EAAE,IAAI;IACzB,GAAGJ;EACL,CAAC,CAAC,EACF,CAACA,QAAQ,CACX,CAAC;EAED,oBACEpE,KAAA,CAAAyE,aAAA,CAACrE,uBAAA,CAAAS,OAAsB,qBACrBb,KAAA,CAAAyE,aAAA,CAAChE,WAAA,CAAAI,OAAU,qBACTb,KAAA,CAAAyE,aAAA,CAACnE,SAAA,CAAAoE,QAAgB;IAACC,KAAK,EAAEN;EAAc,gBACrCrE,KAAA,CAAAyE,aAAA,CAAClE,QAAA,CAAAqE,aAAa;IAAC3C,KAAK,EAAEA;EAAM,GAAEkC,QAAwB,CACtC,CACR,CACU,CAAC;AAE7B,CAAC;AAAC,IAAAU,QAAA,GAAAC,OAAA,CAAAjE,OAAA,GAEaiB,aAAa", "ignoreList": []}
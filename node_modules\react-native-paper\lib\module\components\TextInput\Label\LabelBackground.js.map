{"version": 3, "names": ["React", "Animated", "StyleSheet", "AnimatedText", "LabelBackground", "labeled", "labelLayoutWidth", "labelLayoutHeight", "placeholder<PERSON><PERSON><PERSON>", "baseLabelTranslateX", "topPosition", "backgroundColor", "roundness", "labelStyle", "maxFontSizeMultiplier", "testID", "opacity", "interpolate", "inputRange", "outputRange", "labelTranslationX", "translateX", "labelTextScaleY", "scaleY", "labelTextTransform", "transform", "isRounded", "roundedEdgeCover", "createElement", "View", "key", "pointerEvents", "style", "absoluteFill", "styles", "view", "maxHeight", "Math", "max", "bottom", "<PERSON><PERSON><PERSON><PERSON>", "top", "width", "paddingHorizontal", "height", "numberOfLines", "create", "position", "left", "color"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Label/LabelBackground.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,EAAEC,UAAU,QAAQ,cAAc;AAEnD,OAAOC,YAAY,MAAM,+BAA+B;AAGxD,MAAMC,eAAe,GAAGA,CAAC;EACvBC,OAAO;EACPC,gBAAgB;EAChBC,iBAAiB;EACjBC,gBAAgB;EAChBC,mBAAmB;EACnBC,WAAW;EACXC,eAAe;EACfC,SAAS;EACTC,UAAU;EACVC,qBAAqB;EACrBC;AACoB,CAAC,KAAK;EAC1B,MAAMC,OAAO,GAAGX,OAAO,CAACY,WAAW,CAAC;IAClCC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,CAAC;IACpBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC;EAEF,MAAMC,iBAAiB,GAAG;IACxBC,UAAU,EAAEhB,OAAO,CAACY,WAAW,CAAC;MAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,CAACV,mBAAmB,EAAE,CAAC;IACvC,CAAC;EACH,CAAC;EAED,MAAMa,eAAe,GAAG;IACtBC,MAAM,EAAElB,OAAO,CAACY,WAAW,CAAC;MAC1BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;IACtB,CAAC;EACH,CAAC;EAED,MAAMK,kBAAkB,GAAG,CAAC,GAAGX,UAAU,CAACY,SAAS,EAAEH,eAAe,CAAC;EAErE,MAAMI,SAAS,GAAGd,SAAS,GAAG,CAAC;EAC/B,MAAMe,gBAAgB,GAAGD,SAAS,gBAChC1B,KAAA,CAAA4B,aAAA,CAAC3B,QAAQ,CAAC4B,IAAI;IACZC,GAAG,EAAC,sBAAsB;IAC1BC,aAAa,EAAC,MAAM;IACpBC,KAAK,EAAE,CACL9B,UAAU,CAAC+B,YAAY,EACvBC,MAAM,CAACC,IAAI,EACX;MACExB,eAAe;MACfyB,SAAS,EAAEC,IAAI,CAACC,GAAG,CAAC1B,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC;MACrC2B,MAAM,EAAEF,IAAI,CAACC,GAAG,CAAC1B,SAAS,EAAE,CAAC,CAAC;MAC9Ba,SAAS,EAAE,CAACL,iBAAiB,CAAC;MAC9BJ;IACF,CAAC;EACD,CACH,CAAC,GACA,IAAI;EAER,OAAO,CACLW,gBAAgB,eAChB3B,KAAA,CAAA4B,aAAA,CAACzB,YAAY;IACX2B,GAAG,EAAC,sBAAsB;IAC1Bf,MAAM,EAAE,GAAGA,MAAM,mBAAoB;IACrCiB,KAAK,EAAE,CACLxB,gBAAgB,EAChBK,UAAU,EACVqB,MAAM,CAACM,aAAa,EACpB;MACEC,GAAG,EAAE/B,WAAW,GAAG,CAAC;MACpBgC,KAAK,EAAEpC,gBAAgB,GAAGE,gBAAgB,CAACmC,iBAAiB;MAC5DC,MAAM,EAAErC,iBAAiB;MACzBI,eAAe;MACfK,OAAO;MACPS,SAAS,EAAED;IACb,CAAC,CACD;IACFqB,aAAa,EAAE,CAAE;IACjB/B,qBAAqB,EAAEA;EAAsB,CAC9C,CAAC,CACH;AACH,CAAC;AAED,eAAeV,eAAe;AAE9B,MAAM8B,MAAM,GAAGhC,UAAU,CAAC4C,MAAM,CAAC;EAC/BX,IAAI,EAAE;IACJY,QAAQ,EAAE,UAAU;IACpBN,GAAG,EAAE,CAAC;IACNO,IAAI,EAAE,EAAE;IACRN,KAAK,EAAE;EACT,CAAC;EACD;EACAF,aAAa,EAAE;IACbO,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPL,iBAAiB,EAAE,CAAC;IACpBM,KAAK,EAAE;EACT;AACF,CAAC,CAAC", "ignoreList": []}
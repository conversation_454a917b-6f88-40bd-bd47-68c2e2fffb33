{"version": 3, "sources": ["RNGestureHandlerModule.windows.ts"], "names": ["PanGestureHandler", "TapGestureHandler", "LongPressGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "NativeViewGestureHandler", "ManualGestureHandler", "Gestures", "handleSetJSResponder", "_tag", "_blockNativeResponder", "handleClearJSResponder", "createGestureHandler", "_handler<PERSON>ame", "_handlerTag", "_config", "attachGestureHandler", "_new<PERSON>iew", "_actionType", "_propsRef", "updateGestureHandler", "_newConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "mappings": "AAIA;AACA,OAAOA,iBAAP,MAA8B,kCAA9B;AACA,OAAOC,iBAAP,MAA8B,kCAA9B;AACA,OAAOC,uBAAP,MAAoC,wCAApC;AACA,OAAOC,mBAAP,MAAgC,oCAAhC;AACA,OAAOC,sBAAP,MAAmC,uCAAnC;AACA,OAAOC,mBAAP,MAAgC,oCAAhC;AACA,OAAOC,wBAAP,MAAqC,yCAArC;AACA,OAAOC,oBAAP,MAAiC,qCAAjC;AAGA,OAAO,MAAMC,QAAQ,GAAG;AACtBF,EAAAA,wBADsB;AAEtBN,EAAAA,iBAFsB;AAGtBC,EAAAA,iBAHsB;AAItBC,EAAAA,uBAJsB;AAKtBC,EAAAA,mBALsB;AAMtBC,EAAAA,sBANsB;AAOtBC,EAAAA,mBAPsB;AAQtBE,EAAAA;AARsB,CAAjB;AAWP,eAAe;AACbE,EAAAA,oBAAoB,CAACC,IAAD,EAAeC,qBAAf,EAA+C,CACjE;AACD,GAHY;;AAIbC,EAAAA,sBAAsB,GAAG,CACvB;AACD,GANY;;AAObC,EAAAA,oBAAoB,CAClBC,YADkB,EAElBC,WAFkB,EAGlBC,OAHkB,EAIlB,CACA;AACD,GAbY;;AAcbC,EAAAA,oBAAoB,CAClBF,WADkB,EAElB;AACAG,EAAAA,QAHkB,EAIlBC,WAJkB,EAKlBC,SALkB,EAMlB,CACA;AACD,GAtBY;;AAuBbC,EAAAA,oBAAoB,CAACN,WAAD,EAAsBO,UAAtB,EAA0C,CAC5D;AACD,GAzBY;;AA0BbC,EAAAA,qBAAqB,CAACR,WAAD,EAAsB,CACzC;AACD,GA5BY;;AA6BbS,EAAAA,kBAAkB,CAACT,WAAD,EAAsB,CACtC;AACD,GA/BY;;AAgCbU,EAAAA,eAAe,GAAG,CAChB;AACD;;AAlCY,CAAf", "sourcesContent": ["import React from 'react';\n\nimport { ActionType } from './ActionType';\n\n// GestureHandlers\nimport PanGestureHandler from './web/handlers/PanGestureHandler';\nimport TapGestureHandler from './web/handlers/TapGestureHandler';\nimport LongPressGestureHandler from './web/handlers/LongPressGestureHandler';\nimport PinchGestureHandler from './web/handlers/PinchGestureHandler';\nimport RotationGestureHandler from './web/handlers/RotationGestureHandler';\nimport FlingGestureHandler from './web/handlers/FlingGestureHandler';\nimport NativeViewGestureHandler from './web/handlers/NativeViewGestureHandler';\nimport ManualGestureHandler from './web/handlers/ManualGestureHandler';\nimport { Config } from './web/interfaces';\n\nexport const Gestures = {\n  NativeViewGestureHandler,\n  PanGestureHandler,\n  <PERSON>p<PERSON><PERSON>ure<PERSON>and<PERSON>,\n  <PERSON><PERSON>ress<PERSON>esture<PERSON><PERSON><PERSON>,\n  <PERSON>nchGesture<PERSON><PERSON><PERSON>,\n  Rotation<PERSON><PERSON>ure<PERSON><PERSON><PERSON>,\n  <PERSON>ling<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  ManualGestureHandler,\n};\n\nexport default {\n  handleSetJSResponder(_tag: number, _blockNativeResponder: boolean) {\n    // NO-OP\n  },\n  handleClearJSResponder() {\n    // NO-OP\n  },\n  createGestureHandler<T>(\n    _handlerName: keyof typeof Gestures,\n    _handlerTag: number,\n    _config: T\n  ) {\n    // NO-OP\n  },\n  attachGestureHandler(\n    _handlerTag: number,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    _newView: any,\n    _actionType: ActionType,\n    _propsRef: React.RefObject<unknown>\n  ) {\n    // NO-OP\n  },\n  updateGestureHandler(_handlerTag: number, _newConfig: Config) {\n    // NO-OP\n  },\n  getGestureHandlerNode(_handlerTag: number) {\n    // NO-OP\n  },\n  dropGestureHandler(_handlerTag: number) {\n    // NO-OP\n  },\n  flushOperations() {\n    // NO-OP\n  },\n};\n"]}
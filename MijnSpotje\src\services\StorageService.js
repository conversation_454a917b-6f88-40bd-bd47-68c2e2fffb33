import AsyncStorage from '@react-native-async-storage/async-storage';
import PerformanceMonitor from '../utils/PerformanceMonitor';

class StorageService {
  constructor() {
    this.cache = new Map();
    this.pendingWrites = new Map();
    this.batchTimeout = null;
  }

  // Storage keys
  static KEYS = {
    USER_SETTINGS: 'user_settings',
    SPOTS: 'spots',
    USER_PROFILE: 'user_profile',
    THEME_MODE: 'theme_mode',
    LANGUAGE: 'language',
    ONBOARDING_COMPLETED: 'onboarding_completed',
    LAST_LOCATION: 'last_location',
    FAVORITES: 'favorites',
    RECENT_SEARCHES: 'recent_searches',
  };

  // Optimized storage methods with caching and batching
  async setItem(key, value) {
    try {
      PerformanceMonitor.startTiming(`Storage.setItem.${key}`);

      const jsonValue = JSON.stringify(value);

      // Update cache immediately
      this.cache.set(key, value);

      // <PERSON><PERSON> writes for better performance
      this.pendingWrites.set(key, jsonValue);
      this.scheduleBatchWrite();

      PerformanceMonitor.endTiming(`Storage.setItem.${key}`);
      return true;
    } catch (error) {
      console.error(`Error saving ${key}:`, error);
      PerformanceMonitor.endTiming(`Storage.setItem.${key}`);
      return false;
    }
  }

  async getItem(key, defaultValue = null) {
    try {
      PerformanceMonitor.startTiming(`Storage.getItem.${key}`);

      // Check cache first
      if (this.cache.has(key)) {
        PerformanceMonitor.endTiming(`Storage.getItem.${key}`);
        return this.cache.get(key);
      }

      const jsonValue = await AsyncStorage.getItem(key);
      const value = jsonValue != null ? JSON.parse(jsonValue) : defaultValue;

      // Cache the result
      this.cache.set(key, value);

      PerformanceMonitor.endTiming(`Storage.getItem.${key}`);
      return value;
    } catch (error) {
      console.error(`Error loading ${key}:`, error);
      PerformanceMonitor.endTiming(`Storage.getItem.${key}`);
      return defaultValue;
    }
  }

  // Batch write operations for better performance
  scheduleBatchWrite() {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
    }

    this.batchTimeout = setTimeout(async () => {
      await this.executeBatchWrite();
    }, 100); // Batch writes every 100ms
  }

  async executeBatchWrite() {
    if (this.pendingWrites.size === 0) return;

    try {
      PerformanceMonitor.startTiming('Storage.batchWrite');

      const writes = Array.from(this.pendingWrites.entries());
      this.pendingWrites.clear();

      // Use multiSet for better performance
      await AsyncStorage.multiSet(writes);

      PerformanceMonitor.endTiming('Storage.batchWrite');
      console.log(`💾 Batch wrote ${writes.length} items to storage`);
    } catch (error) {
      console.error('❌ Error in batch write:', error);
      PerformanceMonitor.endTiming('Storage.batchWrite');

      // Retry individual writes if batch fails
      try {
        console.log('🔄 Retrying individual writes...');
        for (const [key, value] of writes) {
          await AsyncStorage.setItem(key, value);
        }
        console.log('✅ Individual writes completed');
      } catch (retryError) {
        console.error('❌ Individual write retry failed:', retryError);
      }
    }
  }

  async removeItem(key) {
    try {
      await AsyncStorage.removeItem(key);
      return true;
    } catch (error) {
      console.error(`Error removing ${key}:`, error);
      return false;
    }
  }

  async clear() {
    try {
      await AsyncStorage.clear();
      return true;
    } catch (error) {
      console.error('Error clearing storage:', error);
      return false;
    }
  }

  // User Settings
  async saveUserSettings(settings) {
    return await this.setItem(StorageService.KEYS.USER_SETTINGS, settings);
  }

  async getUserSettings() {
    return await this.getItem(StorageService.KEYS.USER_SETTINGS, {
      notifications: true,
      locationSharing: true,
      autoLoadImages: true,
      backgroundSync: true,
      highQualityImages: false,
      dataUsage: 'normal',
    });
  }

  // Spots
  async saveSpots(spots) {
    return await this.setItem(StorageService.KEYS.SPOTS, spots);
  }

  async getSpots() {
    return await this.getItem(StorageService.KEYS.SPOTS, []);
  }

  async addSpot(spot) {
    try {
      const spots = await this.getSpots();
      const newSpot = {
        ...spot,
        id: spot.id || Date.now().toString(),
        createdAt: spot.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      
      spots.push(newSpot);
      await this.saveSpots(spots);
      return newSpot;
    } catch (error) {
      console.error('Error adding spot:', error);
      return null;
    }
  }

  async updateSpot(spotId, updates) {
    try {
      const spots = await this.getSpots();
      const spotIndex = spots.findIndex(spot => spot.id === spotId);
      
      if (spotIndex !== -1) {
        spots[spotIndex] = {
          ...spots[spotIndex],
          ...updates,
          updatedAt: new Date().toISOString(),
        };
        await this.saveSpots(spots);
        return spots[spotIndex];
      }
      
      return null;
    } catch (error) {
      console.error('Error updating spot:', error);
      return null;
    }
  }

  async deleteSpot(spotId) {
    try {
      const spots = await this.getSpots();
      const filteredSpots = spots.filter(spot => spot.id !== spotId);
      await this.saveSpots(filteredSpots);
      return true;
    } catch (error) {
      console.error('Error deleting spot:', error);
      return false;
    }
  }

  // User Profile
  async saveUserProfile(profile) {
    return await this.setItem(StorageService.KEYS.USER_PROFILE, profile);
  }

  async getUserProfile() {
    return await this.getItem(StorageService.KEYS.USER_PROFILE, {
      name: 'Jan de Vries',
      email: '<EMAIL>',
      avatar: null,
      bio: '',
      location: 'Amsterdam, Nederland',
      joinedDate: new Date().toISOString(),
    });
  }

  // Theme and Language
  async saveThemeMode(themeMode) {
    return await this.setItem(StorageService.KEYS.THEME_MODE, themeMode);
  }

  async getThemeMode() {
    return await this.getItem(StorageService.KEYS.THEME_MODE, 'system');
  }

  async saveLanguage(language) {
    return await this.setItem(StorageService.KEYS.LANGUAGE, language);
  }

  async getLanguage() {
    return await this.getItem(StorageService.KEYS.LANGUAGE, 'nl');
  }

  // Onboarding
  async setOnboardingCompleted(completed = true) {
    return await this.setItem(StorageService.KEYS.ONBOARDING_COMPLETED, completed);
  }

  async isOnboardingCompleted() {
    return await this.getItem(StorageService.KEYS.ONBOARDING_COMPLETED, false);
  }

  // Location
  async saveLastLocation(location) {
    return await this.setItem(StorageService.KEYS.LAST_LOCATION, location);
  }

  async getLastLocation() {
    return await this.getItem(StorageService.KEYS.LAST_LOCATION, null);
  }

  // Favorites
  async saveFavorites(favorites) {
    return await this.setItem(StorageService.KEYS.FAVORITES, favorites);
  }

  async getFavorites() {
    return await this.getItem(StorageService.KEYS.FAVORITES, []);
  }

  async addToFavorites(spotId) {
    try {
      const favorites = await this.getFavorites();
      if (!favorites.includes(spotId)) {
        favorites.push(spotId);
        await this.saveFavorites(favorites);
      }
      return true;
    } catch (error) {
      console.error('Error adding to favorites:', error);
      return false;
    }
  }

  async removeFromFavorites(spotId) {
    try {
      const favorites = await this.getFavorites();
      const filteredFavorites = favorites.filter(id => id !== spotId);
      await this.saveFavorites(filteredFavorites);
      return true;
    } catch (error) {
      console.error('Error removing from favorites:', error);
      return false;
    }
  }

  async isFavorite(spotId) {
    try {
      const favorites = await this.getFavorites();
      return favorites.includes(spotId);
    } catch (error) {
      console.error('Error checking favorite:', error);
      return false;
    }
  }

  // Recent Searches
  async saveRecentSearches(searches) {
    return await this.setItem(StorageService.KEYS.RECENT_SEARCHES, searches);
  }

  async getRecentSearches() {
    return await this.getItem(StorageService.KEYS.RECENT_SEARCHES, []);
  }

  async addRecentSearch(searchTerm) {
    try {
      const searches = await this.getRecentSearches();
      const filteredSearches = searches.filter(term => term !== searchTerm);
      filteredSearches.unshift(searchTerm);
      
      // Keep only last 10 searches
      const limitedSearches = filteredSearches.slice(0, 10);
      await this.saveRecentSearches(limitedSearches);
      return true;
    } catch (error) {
      console.error('Error adding recent search:', error);
      return false;
    }
  }

  // Utility methods
  async getStorageSize() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      let totalSize = 0;
      
      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          totalSize += value.length;
        }
      }
      
      return {
        keys: keys.length,
        sizeBytes: totalSize,
        sizeMB: (totalSize / (1024 * 1024)).toFixed(2),
      };
    } catch (error) {
      console.error('Error calculating storage size:', error);
      return { keys: 0, sizeBytes: 0, sizeMB: '0.00' };
    }
  }

  async exportData() {
    try {
      const keys = await AsyncStorage.getAllKeys();
      const data = {};

      for (const key of keys) {
        const value = await AsyncStorage.getItem(key);
        if (value) {
          data[key] = JSON.parse(value);
        }
      }

      return data;
    } catch (error) {
      console.error('Error exporting data:', error);
      return {};
    }
  }

  // Clear demo/sample data while preserving user-created content
  async clearDemoData() {
    try {
      console.log('🗑️ Clearing demo data from storage...');

      // Get current spots
      const spots = await this.getSpots();

      // Filter out demo spots (spots with IDs 1-5 or specific demo titles)
      const demoSpotIds = [1, 2, 3, 4, 5];
      const demoTitles = [
        'Vondelpark',
        'Anne Frank Huis',
        'Café de Reiger',
        'Rijksmuseum',
        'Foodhallen'
      ];

      const userSpots = spots.filter(spot =>
        !demoSpotIds.includes(spot.id) &&
        !demoTitles.includes(spot.title)
      );

      // Save only user-created spots
      await this.saveSpots(userSpots);

      console.log(`✅ Demo data cleared. Kept ${userSpots.length} user-created spots.`);
      return userSpots;
    } catch (error) {
      console.error('Error clearing demo data:', error);
      return [];
    }
  }

  // Clear all storage
  async clearAll() {
    try {
      await AsyncStorage.clear();
      this.cache.clear();
      this.pendingWrites.clear();
      if (this.batchTimeout) {
        clearTimeout(this.batchTimeout);
        this.batchTimeout = null;
      }
      console.log('🗑️ All storage cleared');
      return true;
    } catch (error) {
      console.error('Error clearing storage:', error);
      return false;
    }
  }

  // Cleanup method to prevent memory leaks
  cleanup() {
    if (this.batchTimeout) {
      clearTimeout(this.batchTimeout);
      this.batchTimeout = null;
    }

    // Clear cache if it gets too large
    if (this.cache.size > 100) {
      console.log('🧹 Clearing storage cache to prevent memory issues');
      this.cache.clear();
    }

    // Execute any pending writes before cleanup
    if (this.pendingWrites.size > 0) {
      this.executeBatchWrite();
    }
  }

  // Get cache statistics
  getCacheStats() {
    return {
      cacheSize: this.cache.size,
      pendingWrites: this.pendingWrites.size,
      hasBatchTimeout: !!this.batchTimeout,
    };
  }
}

// Export singleton instance
export default new StorageService();

# Mijn Spotje - Social Location Sharing App

Een React Native app voor het delen en ontdekken van leuke plekken met vrienden. Gebruikers kunnen spots uploaden met foto's en locaties, deze beoordelen, en delen binnen groepen.

**🎯 Deze versie is volledig geoptimaliseerd voor Expo Go - geen custom native builds vereist!**

## 🚀 Features

### ✅ Volledig Functioneel (Expo Go Compatible)
- **Authenticatie**: Werkende login/registratie met mock backend
- **Navigatie**: Complete app navigatie met tabs en stacks
- **UI/UX**: Modern design met consistent theme systeem
- **Spots Overzicht**: Lijst met zoekfunctie en mock data
- **Kaart Placeholder**: Kaart interface met spots lijst
- **Groepen**: Groepsoverzicht met mock data
- **Profiel**: Gebruikersprofiel met statistieken
- **Detail Screens**: Spot details met rating en comments
- **Mock Data**: Realistische demo data voor alle features

### 🔄 Klaar voor Uitbreiding
- **Google Maps integratie**: Vervang placeholder met echte kaart
- **Supabase Backend**: Vervang mock data met echte database
- **Foto upload**: Camera en galerij integratie
- **Real-time features**: Push notificaties en live updates
- **Gamification**: Levels, badges en achievements systeem

## 📱 Screenshots

*Screenshots worden toegevoegd zodra de app volledig functioneel is*

## 🛠 Technische Stack

- **Frontend**: React Native met Expo (Expo Go compatible)
- **Authenticatie**: Mock authenticatie met AsyncStorage
- **Data**: Mock data voor demo doeleinden
- **Navigatie**: React Navigation v6
- **UI**: Custom theme systeem met Ionicons
- **Storage**: AsyncStorage voor lokale data

**Klaar voor uitbreiding naar:**
- **Backend**: Supabase (PostgreSQL database)
- **Maps**: Google Maps SDK
- **Storage**: Supabase Storage voor foto's
- **Push Notifications**: Expo Notifications

## 📋 Vereisten

- Node.js (v16 of hoger)
- npm of yarn
- Expo Go app op je telefoon

## 🚀 Snelle Start

1. **Clone de repository**
   ```bash
   git clone <repository-url>
   cd MijnSpotje
   ```

2. **Installeer dependencies**
   ```bash
   npm install
   ```

3. **Start de app**
   ```bash
   npm start
   ```

4. **Test direct op je telefoon**
   - Download Expo Go app op je telefoon
   - Scan de QR code die verschijnt in je terminal
   - De app start direct op - geen configuratie vereist!

## 🎮 Demo Functionaliteit

De app werkt volledig met mock data, dus je kunt direct:

- **Inloggen**: Gebruik elk e-mailadres en wachtwoord
- **Spots bekijken**: 5 demo spots uit Amsterdam
- **Zoeken**: Zoek in spots op titel, beschrijving of categorie
- **Kaart**: Bekijk spots in lijst format met locatie info
- **Groepen**: 3 demo groepen met verschillende rollen
- **Profiel**: Bekijk gebruikersprofiel met statistieken
- **Details**: Tap op spots voor gedetailleerde informatie
- **Rating**: Geef ratings aan spots (lokaal opgeslagen)
- **Favorieten**: Voeg spots toe aan favorieten

## 📁 Project Structuur

```
MijnSpotje/
├── src/
│   ├── config/          # Configuratie bestanden
│   ├── contexts/        # React contexts (Auth, etc.)
│   ├── navigation/      # Navigatie setup
│   ├── screens/         # App screens
│   │   ├── auth/        # Login, register screens
│   │   ├── main/        # Hoofdschermen (Map, Spots, etc.)
│   │   ├── details/     # Detail screens
│   │   ├── create/      # Create screens
│   │   └── profile/     # Profiel gerelateerde screens
│   └── services/        # API services
├── assets/              # Afbeeldingen, fonts, etc.
├── database/            # Database schema en migrations
└── README.md
```

## 🗄 Database Schema

De app gebruikt de volgende hoofdtabellen:
- `users` - Gebruikersprofielen
- `spots` - Locatie spots
- `spot_images` - Foto's van spots
- `ratings` - Beoordelingen van spots
- `comments` - Reacties op spots
- `groups` - Gebruikersgroepen
- `group_members` - Groepslidmaatschappen
- `achievements` - Beschikbare prestaties
- `user_achievements` - Behaalde prestaties
- `favorites` - Favoriete spots

Zie `database/schema.sql` voor de complete database structuur.

## 🔧 Ontwikkeling

### Nieuwe features toevoegen

1. **Screens**: Voeg nieuwe screens toe in de juiste map onder `src/screens/`
2. **Services**: API calls gaan via services in `src/services/`
3. **Navigatie**: Update `src/navigation/AppNavigator.js` voor nieuwe routes
4. **Styling**: Gebruik het theme systeem in `src/config/theme.js`

### Code conventies

- Gebruik functionele components met hooks
- Volg de bestaande folder structuur
- Gebruik TypeScript voor type safety (toekomstige update)
- Schrijf tests voor kritieke functionaliteit

## 🚀 Deployment

### Expo Build
```bash
# Android
expo build:android

# iOS (vereist Apple Developer account)
expo build:ios
```

### App Stores
- Volg Expo's documentatie voor app store deployment
- Configureer app signing en certificates

## 🤝 Bijdragen

1. Fork het project
2. Maak een feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit je changes (`git commit -m 'Add some AmazingFeature'`)
4. Push naar de branch (`git push origin feature/AmazingFeature`)
5. Open een Pull Request

## 📝 Licentie

Dit project is gelicenseerd onder de MIT License - zie het [LICENSE](LICENSE) bestand voor details.

## 📞 Contact

Voor vragen of suggesties, open een issue op GitHub.

## 🙏 Acknowledgments

- Expo team voor het geweldige development platform
- Supabase voor de backend infrastructure
- React Native community voor de uitstekende libraries

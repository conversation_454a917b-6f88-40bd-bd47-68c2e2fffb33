# Mijn Spotje - Social Location Sharing App

Een React Native app voor het delen en ontdekken van leuke plekken met vrienden. Gebruikers kunnen spots uploaden met foto's en locaties, deze beoordelen, en delen binnen groepen.

## 🚀 Features

### ✅ Geïmplementeerd
- **Authenticatie**: Login, registratie en wachtwoord reset
- **Navigatie**: Complete app navigatie met tabs en stacks
- **UI/UX**: Modern design met dark/light mode support
- **Basis structuur**: Alle screens en services opgezet

### 🔄 In ontwikkeling
- **Spot Management**: Spots aanmaken, bewerken en verwijderen
- **Google Maps integratie**: <PERSON>art weergave en locatie selectie
- **Foto upload**: Camera en galerij integratie
- **Rating systeem**: 5-sterren beoordelingen
- **Groepen**: A<PERSON><PERSON>ken, joinen en beheren van groepen
- **Gamification**: Levels, badges en achievements
- **Push notificaties**: Real-time updates

## 📱 Screenshots

*Screenshots worden toegevoegd zodra de app volledig functioneel is*

## 🛠 Technische Stack

- **Frontend**: React Native met Expo
- **Backend**: Supabase (PostgreSQL database)
- **Maps**: Google Maps SDK
- **Authenticatie**: Supabase Auth
- **Storage**: Supabase Storage voor foto's
- **Push Notifications**: Expo Notifications

## 📋 Vereisten

- Node.js (v16 of hoger)
- npm of yarn
- Expo CLI
- Expo Go app op je telefoon (voor testen)

## 🚀 Installatie

1. **Clone de repository**
   ```bash
   git clone <repository-url>
   cd MijnSpotje
   ```

2. **Installeer dependencies**
   ```bash
   npm install
   ```

3. **Configureer Supabase**
   - Maak een account aan op [supabase.com](https://supabase.com)
   - Maak een nieuw project aan
   - Kopieer je project URL en anon key
   - Update `src/config/supabase.js` met je credentials:
   ```javascript
   const supabaseUrl = 'YOUR_SUPABASE_URL';
   const supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
   ```

4. **Database setup**
   - Voer de SQL queries uit in `database/schema.sql` in je Supabase dashboard
   - Configureer Row Level Security (RLS) policies

5. **Google Maps API (optioneel voor nu)**
   - Verkrijg een Google Maps API key
   - Voeg toe aan app.json onder `expo.android.config.googleMaps.apiKey`

6. **Start de app**
   ```bash
   npm start
   ```

7. **Test op je telefoon**
   - Download Expo Go app
   - Scan de QR code die verschijnt in je terminal

## 📁 Project Structuur

```
MijnSpotje/
├── src/
│   ├── config/          # Configuratie bestanden
│   ├── contexts/        # React contexts (Auth, etc.)
│   ├── navigation/      # Navigatie setup
│   ├── screens/         # App screens
│   │   ├── auth/        # Login, register screens
│   │   ├── main/        # Hoofdschermen (Map, Spots, etc.)
│   │   ├── details/     # Detail screens
│   │   ├── create/      # Create screens
│   │   └── profile/     # Profiel gerelateerde screens
│   └── services/        # API services
├── assets/              # Afbeeldingen, fonts, etc.
├── database/            # Database schema en migrations
└── README.md
```

## 🗄 Database Schema

De app gebruikt de volgende hoofdtabellen:
- `users` - Gebruikersprofielen
- `spots` - Locatie spots
- `spot_images` - Foto's van spots
- `ratings` - Beoordelingen van spots
- `comments` - Reacties op spots
- `groups` - Gebruikersgroepen
- `group_members` - Groepslidmaatschappen
- `achievements` - Beschikbare prestaties
- `user_achievements` - Behaalde prestaties
- `favorites` - Favoriete spots

Zie `database/schema.sql` voor de complete database structuur.

## 🔧 Ontwikkeling

### Nieuwe features toevoegen

1. **Screens**: Voeg nieuwe screens toe in de juiste map onder `src/screens/`
2. **Services**: API calls gaan via services in `src/services/`
3. **Navigatie**: Update `src/navigation/AppNavigator.js` voor nieuwe routes
4. **Styling**: Gebruik het theme systeem in `src/config/theme.js`

### Code conventies

- Gebruik functionele components met hooks
- Volg de bestaande folder structuur
- Gebruik TypeScript voor type safety (toekomstige update)
- Schrijf tests voor kritieke functionaliteit

## 🚀 Deployment

### Expo Build
```bash
# Android
expo build:android

# iOS (vereist Apple Developer account)
expo build:ios
```

### App Stores
- Volg Expo's documentatie voor app store deployment
- Configureer app signing en certificates

## 🤝 Bijdragen

1. Fork het project
2. Maak een feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit je changes (`git commit -m 'Add some AmazingFeature'`)
4. Push naar de branch (`git push origin feature/AmazingFeature`)
5. Open een Pull Request

## 📝 Licentie

Dit project is gelicenseerd onder de MIT License - zie het [LICENSE](LICENSE) bestand voor details.

## 📞 Contact

Voor vragen of suggesties, open een issue op GitHub.

## 🙏 Acknowledgments

- Expo team voor het geweldige development platform
- Supabase voor de backend infrastructure
- React Native community voor de uitstekende libraries

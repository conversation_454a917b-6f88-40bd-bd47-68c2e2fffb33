{"version": 3, "names": ["React", "StyleSheet", "View", "Outline", "isV3", "label", "activeColor", "backgroundColor", "hasActiveOutline", "focused", "outlineColor", "roundness", "style", "createElement", "testID", "pointerEvents", "styles", "outline", "noLabelOutline", "borderRadius", "borderWidth", "borderColor", "create", "position", "left", "right", "top", "bottom"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Addons/Outline.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,UAAU,EAGVC,IAAI,QAEC,cAAc;AAgBrB,OAAO,MAAMC,OAAO,GAAGA,CAAC;EACtBC,IAAI;EACJC,KAAK;EACLC,WAAW;EACXC,eAAe;EACfC,gBAAgB;EAChBC,OAAO;EACPC,YAAY;EACZC,SAAS;EACTC;AACY,CAAC,kBACbZ,KAAA,CAAAa,aAAA,CAACX,IAAI;EACHY,MAAM,EAAC,oBAAoB;EAC3BC,aAAa,EAAC,MAAM;EACpBH,KAAK,EAAE,CACLI,MAAM,CAACC,OAAO,EACd,CAACZ,KAAK,IAAIW,MAAM,CAACE,cAAc;EAC/B;EACA;IACEX,eAAe;IACfY,YAAY,EAAER,SAAS;IACvBS,WAAW,EAAE,CAAChB,IAAI,GAAGI,gBAAgB,GAAGC,OAAO,IAAI,CAAC,GAAG,CAAC;IACxDY,WAAW,EAAEb,gBAAgB,GAAGF,WAAW,GAAGI;EAChD,CAAC,EACDE,KAAK;AACL,CACH,CACF;AAED,MAAMI,MAAM,GAAGf,UAAU,CAACqB,MAAM,CAAC;EAC/BL,OAAO,EAAE;IACPM,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE;EACV,CAAC;EACDT,cAAc,EAAE;IACdQ,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}
import AsyncStorage from '@react-native-async-storage/async-storage';

class GroupsService {
  // Storage keys
  static KEYS = {
    GROUPS: 'groups',
    GROUP_SPOTS: 'group_spots',
    GROUP_INVITES: 'group_invites',
    USER_GROUPS: 'user_groups',
  };

  // Generate unique group ID
  generateGroupId() {
    return `group_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Generate invitation code
  generateInviteCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  }

  // Create new group
  async createGroup(groupData, creatorId = 'current_user') {
    try {
      const groupId = this.generateGroupId();
      const inviteCode = this.generateInviteCode();
      
      const newGroup = {
        id: groupId,
        name: groupData.name,
        description: groupData.description || '',
        avatar: groupData.avatar || null,
        createdBy: creatorId,
        createdAt: new Date().toISOString(),
        members: [
          {
            id: creatorId,
            role: 'admin',
            joinedAt: new Date().toISOString(),
            name: 'Jij', // Current user
          }
        ],
        inviteCode: inviteCode,
        inviteExpiry: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
        settings: {
          allowMemberInvites: true,
          autoApproveJoins: true,
          spotSharingEnabled: true,
        },
        stats: {
          totalSpots: 0,
          totalMembers: 1,
        }
      };

      // Save group
      const groups = await this.getGroups();
      groups.push(newGroup);
      await this.saveGroups(groups);

      // Initialize group spots
      await this.saveGroupSpots(groupId, []);

      console.log('✅ Group created:', newGroup.name);
      return newGroup;
    } catch (error) {
      console.error('❌ Error creating group:', error);
      throw error;
    }
  }

  // Get all groups
  async getGroups() {
    try {
      const groups = await AsyncStorage.getItem(GroupsService.KEYS.GROUPS);
      return groups ? JSON.parse(groups) : [];
    } catch (error) {
      console.error('❌ Error getting groups:', error);
      return [];
    }
  }

  // Save groups
  async saveGroups(groups) {
    try {
      await AsyncStorage.setItem(GroupsService.KEYS.GROUPS, JSON.stringify(groups));
      return true;
    } catch (error) {
      console.error('❌ Error saving groups:', error);
      return false;
    }
  }

  // Get group by ID
  async getGroupById(groupId) {
    try {
      const groups = await this.getGroups();
      return groups.find(group => group.id === groupId) || null;
    } catch (error) {
      console.error('❌ Error getting group by ID:', error);
      return null;
    }
  }

  // Update group
  async updateGroup(groupId, updates) {
    try {
      const groups = await this.getGroups();
      const groupIndex = groups.findIndex(group => group.id === groupId);
      
      if (groupIndex !== -1) {
        groups[groupIndex] = {
          ...groups[groupIndex],
          ...updates,
          updatedAt: new Date().toISOString(),
        };
        
        await this.saveGroups(groups);
        return groups[groupIndex];
      }
      
      return null;
    } catch (error) {
      console.error('❌ Error updating group:', error);
      return null;
    }
  }

  // Delete group
  async deleteGroup(groupId) {
    try {
      const groups = await this.getGroups();
      const filteredGroups = groups.filter(group => group.id !== groupId);
      
      await this.saveGroups(filteredGroups);
      
      // Clean up group spots
      await AsyncStorage.removeItem(`${GroupsService.KEYS.GROUP_SPOTS}_${groupId}`);
      
      console.log('✅ Group deleted:', groupId);
      return true;
    } catch (error) {
      console.error('❌ Error deleting group:', error);
      return false;
    }
  }

  // Join group by invite code
  async joinGroupByCode(inviteCode, userId = 'current_user', userName = 'Nieuwe Gebruiker') {
    try {
      const groups = await this.getGroups();
      const group = groups.find(g => g.inviteCode === inviteCode);
      
      if (!group) {
        throw new Error('Ongeldige uitnodigingscode');
      }
      
      // Check if invite is expired
      if (new Date() > new Date(group.inviteExpiry)) {
        throw new Error('Uitnodigingscode is verlopen');
      }
      
      // Check if user is already a member
      if (group.members.some(member => member.id === userId)) {
        throw new Error('Je bent al lid van deze groep');
      }
      
      // Add user to group
      group.members.push({
        id: userId,
        role: 'member',
        joinedAt: new Date().toISOString(),
        name: userName,
      });
      
      group.stats.totalMembers = group.members.length;
      
      await this.updateGroup(group.id, group);
      
      console.log('✅ User joined group:', group.name);
      return group;
    } catch (error) {
      console.error('❌ Error joining group:', error);
      throw error;
    }
  }

  // Leave group
  async leaveGroup(groupId, userId = 'current_user') {
    try {
      const group = await this.getGroupById(groupId);
      if (!group) {
        throw new Error('Groep niet gevonden');
      }
      
      // Remove user from members
      group.members = group.members.filter(member => member.id !== userId);
      group.stats.totalMembers = group.members.length;
      
      // If no members left, delete group
      if (group.members.length === 0) {
        await this.deleteGroup(groupId);
        return { deleted: true };
      }
      
      // If leaving user was admin and there are other members, promote first member to admin
      const hasAdmin = group.members.some(member => member.role === 'admin');
      if (!hasAdmin && group.members.length > 0) {
        group.members[0].role = 'admin';
      }
      
      await this.updateGroup(groupId, group);
      
      console.log('✅ User left group:', group.name);
      return { deleted: false, group };
    } catch (error) {
      console.error('❌ Error leaving group:', error);
      throw error;
    }
  }

  // Regenerate invite code
  async regenerateInviteCode(groupId) {
    try {
      const newCode = this.generateInviteCode();
      const newExpiry = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString();
      
      const updatedGroup = await this.updateGroup(groupId, {
        inviteCode: newCode,
        inviteExpiry: newExpiry,
      });
      
      console.log('✅ Invite code regenerated for group:', groupId);
      return updatedGroup;
    } catch (error) {
      console.error('❌ Error regenerating invite code:', error);
      throw error;
    }
  }

  // Group spots management
  async getGroupSpots(groupId) {
    try {
      const spots = await AsyncStorage.getItem(`${GroupsService.KEYS.GROUP_SPOTS}_${groupId}`);
      return spots ? JSON.parse(spots) : [];
    } catch (error) {
      console.error('❌ Error getting group spots:', error);
      return [];
    }
  }

  async saveGroupSpots(groupId, spots) {
    try {
      await AsyncStorage.setItem(`${GroupsService.KEYS.GROUP_SPOTS}_${groupId}`, JSON.stringify(spots));
      return true;
    } catch (error) {
      console.error('❌ Error saving group spots:', error);
      return false;
    }
  }

  async addSpotToGroup(groupId, spot, sharedBy = 'current_user') {
    try {
      const groupSpots = await this.getGroupSpots(groupId);
      
      const groupSpot = {
        ...spot,
        groupId,
        sharedBy,
        sharedAt: new Date().toISOString(),
        groupSpotId: `${groupId}_${spot.id}_${Date.now()}`,
      };
      
      groupSpots.push(groupSpot);
      await this.saveGroupSpots(groupId, groupSpots);
      
      // Update group stats
      const group = await this.getGroupById(groupId);
      if (group) {
        group.stats.totalSpots = groupSpots.length;
        await this.updateGroup(groupId, group);
      }
      
      console.log('✅ Spot added to group:', spot.title);
      return groupSpot;
    } catch (error) {
      console.error('❌ Error adding spot to group:', error);
      throw error;
    }
  }

  async removeSpotFromGroup(groupId, groupSpotId) {
    try {
      const groupSpots = await this.getGroupSpots(groupId);
      const filteredSpots = groupSpots.filter(spot => spot.groupSpotId !== groupSpotId);
      
      await this.saveGroupSpots(groupId, filteredSpots);
      
      // Update group stats
      const group = await this.getGroupById(groupId);
      if (group) {
        group.stats.totalSpots = filteredSpots.length;
        await this.updateGroup(groupId, group);
      }
      
      console.log('✅ Spot removed from group');
      return true;
    } catch (error) {
      console.error('❌ Error removing spot from group:', error);
      return false;
    }
  }

  // Get user's groups
  async getUserGroups(userId = 'current_user') {
    try {
      const allGroups = await this.getGroups();
      return allGroups.filter(group => 
        group.members.some(member => member.id === userId)
      );
    } catch (error) {
      console.error('❌ Error getting user groups:', error);
      return [];
    }
  }

  // Check if user is admin of group
  isGroupAdmin(group, userId = 'current_user') {
    const member = group.members.find(m => m.id === userId);
    return member && member.role === 'admin';
  }

  // Get group member by ID
  getGroupMember(group, userId) {
    return group.members.find(m => m.id === userId) || null;
  }
}

// Export singleton instance
export default new GroupsService();

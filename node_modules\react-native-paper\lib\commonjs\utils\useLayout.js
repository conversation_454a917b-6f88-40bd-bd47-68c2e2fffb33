"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = useLayout;
var React = _interopRequireWildcard(require("react"));
function _interopRequireWildcard(e, t) { if ("function" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function (e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || "object" != typeof e && "function" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (const t in e) "default" !== t && {}.hasOwnProperty.call(e, t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, t)) && (i.get || i.set) ? o(f, t, i) : f[t] = e[t]); return f; })(e, t); }
function useLayout() {
  const [layout, setLayout] = React.useState({
    height: 0,
    width: 0,
    measured: false
  });
  const onLayout = React.useCallback(e => {
    const {
      height,
      width
    } = e.nativeEvent.layout;
    if (height === layout.height && width === layout.width) {
      return;
    }
    setLayout({
      height,
      width,
      measured: true
    });
  }, [layout.height, layout.width]);
  return [layout, onLayout];
}
//# sourceMappingURL=useLayout.js.map
import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  Linking,
} from 'react-native';
import { useTranslation } from '../i18n/useTranslation';

const PrivacyPolicyModal = ({ visible, onClose, isDark }) => {
  const { t } = useTranslation();

  const handleEmailPress = () => {
    Linking.openURL('mailto:<EMAIL>');
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, isDark && styles.containerDark]}>
        <View style={[styles.header, isDark && styles.headerDark]}>
          <TouchableOpacity onPress={onClose}>
            <Text style={[styles.button, styles.closeButton]}>{t('common.close')}</Text>
          </TouchableOpacity>
          <Text style={[styles.title, isDark && styles.textDark]}>{t('legal.privacy.title')}</Text>
          <View style={styles.placeholder} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={true}>
          <View style={styles.documentHeader}>
            <Text style={[styles.documentTitle, isDark && styles.textDark]}>
              {t('legal.privacy.documentTitle')}
            </Text>
            <Text style={[styles.lastUpdated, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.lastUpdated')}: 15 december 2024
            </Text>
            <Text style={[styles.version, isDark && styles.textSecondaryDark]}>
              Versie 1.0
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              1. {t('legal.privacy.dataCollection.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.dataCollection.intro')}
            </Text>
            <View style={styles.subsection}>
              <Text style={[styles.subsectionTitle, isDark && styles.textDark]}>
                {t('legal.privacy.dataCollection.personalData')}
              </Text>
              <View style={styles.bulletPoints}>
                <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                  • {t('legal.privacy.dataCollection.point1')}
                </Text>
                <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                  • {t('legal.privacy.dataCollection.point2')}
                </Text>
                <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                  • {t('legal.privacy.dataCollection.point3')}
                </Text>
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              2. {t('legal.privacy.locationData.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.locationData.content')}
            </Text>
            <View style={[styles.importantNote, isDark && styles.importantNoteDark]}>
              <Text style={[styles.importantNoteText, isDark && styles.textDark]}>
                {t('legal.privacy.locationData.important')}
              </Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              3. {t('legal.privacy.dataUsage.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.dataUsage.intro')}
            </Text>
            <View style={styles.bulletPoints}>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.dataUsage.point1')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.dataUsage.point2')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.dataUsage.point3')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.dataUsage.point4')}
              </Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              4. {t('legal.privacy.thirdParty.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.thirdParty.content')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              5. {t('legal.privacy.dataRetention.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.dataRetention.content')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              6. {t('legal.privacy.userRights.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.userRights.intro')}
            </Text>
            <View style={styles.bulletPoints}>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.userRights.access')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.userRights.rectification')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.userRights.erasure')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.userRights.portability')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.privacy.userRights.objection')}
              </Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              7. {t('legal.privacy.security.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.security.content')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              8. {t('legal.privacy.contact.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.privacy.contact.content')}
            </Text>
            <TouchableOpacity onPress={handleEmailPress} style={styles.contactInfo}>
              <Text style={[styles.contactText, isDark && styles.textDark]}>
                Mijn Spotje B.V.{'\n'}
                Data Protection Officer{'\n'}
                Herengracht 123{'\n'}
                1015 BG Amsterdam{'\n'}
                Nederland{'\n\n'}
                E-mail: <EMAIL>{'\n'}
                Telefoon: +31 20 123 4567
              </Text>
            </TouchableOpacity>
          </View>

          <View style={styles.bottomPadding} />
        </ScrollView>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
  },
  textDark: {
    color: '#ffffff',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  button: {
    fontSize: 16,
    fontWeight: '600',
  },
  closeButton: {
    color: '#007AFF',
  },
  placeholder: {
    width: 60,
  },
  content: {
    flex: 1,
    padding: 20,
  },
  documentHeader: {
    marginBottom: 30,
    alignItems: 'center',
  },
  documentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  version: {
    fontSize: 12,
    color: '#666666',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 12,
  },
  sectionText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 8,
  },
  subsection: {
    marginTop: 12,
    marginLeft: 16,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  bulletPoints: {
    marginLeft: 16,
  },
  bulletPoint: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 4,
  },
  importantNote: {
    backgroundColor: '#fff3cd',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#ffc107',
  },
  importantNoteDark: {
    backgroundColor: '#2c2c2e',
    borderLeftColor: '#ff9500',
  },
  importantNoteText: {
    fontSize: 14,
    color: '#856404',
    fontWeight: '500',
  },
  contactInfo: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  contactText: {
    fontSize: 14,
    color: '#000000',
    lineHeight: 20,
    fontFamily: 'monospace',
  },
  bottomPadding: {
    height: 50,
  },
});

export default PrivacyPolicyModal;

{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "DURATION", "ActivityIndicator", "animating", "color", "indicatorColor", "hidesWhenStopped", "size", "indicatorSize", "style", "theme", "themeOverrides", "rest", "_theme$colors", "useInternalTheme", "current", "timer", "useRef", "Animated", "Value", "fade", "rotation", "undefined", "animation", "scale", "startRotation", "useCallback", "timing", "duration", "toValue", "isInteraction", "useNativeDriver", "start", "setValue", "loop", "stopRotation", "stop", "useEffect", "easing", "Easing", "linear", "Platform", "OS", "colors", "primary", "frames", "bezier", "containerStyle", "width", "height", "overflow", "createElement", "View", "styles", "container", "accessible", "accessibilityRole", "accessibilityState", "busy", "opacity", "collapsable", "map", "index", "inputRange", "Array", "from", "_", "frameIndex", "outputRange", "progress", "direction", "layerStyle", "transform", "rotate", "interpolate", "viewportStyle", "translateY", "offsetStyle", "top", "lineStyle", "borderColor", "borderWidth", "borderRadius", "key", "layer", "StyleSheet", "create", "justifyContent", "alignItems", "absoluteFillObject", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["components/ActivityIndicator.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,QAAA,GAAAF,OAAA;AAAmD,SAAAD,wBAAAI,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAN,uBAAA,YAAAA,CAAAI,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAkB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAjB,CAAA,aAAAJ,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAC,CAAA,GAAAqB,SAAA,CAAAtB,CAAA,YAAAG,CAAA,IAAAF,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAe,QAAA,CAAAK,KAAA,OAAAF,SAAA;AA2BnD,MAAMG,QAAQ,GAAG,IAAI;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,iBAAiB,GAAGA,CAAC;EACzBC,SAAS,GAAG,IAAI;EAChBC,KAAK,EAAEC,cAAc;EACrBC,gBAAgB,GAAG,IAAI;EACvBC,IAAI,EAAEC,aAAa,GAAG,OAAO;EAC7BC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMH,KAAK,GAAG,IAAAI,yBAAgB,EAACH,cAAc,CAAC;EAC9C,MAAM;IAAEI,OAAO,EAAEC;EAAM,CAAC,GAAG7C,KAAK,CAAC8C,MAAM,CACrC,IAAIC,qBAAQ,CAACC,KAAK,CAAC,CAAC,CACtB,CAAC;EACD,MAAM;IAAEJ,OAAO,EAAEK;EAAK,CAAC,GAAGjD,KAAK,CAAC8C,MAAM,CACpC,IAAIC,qBAAQ,CAACC,KAAK,CAAC,CAAChB,SAAS,IAAIG,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAC3D,CAAC;EAED,MAAMe,QAAQ,GAAGlD,KAAK,CAAC8C,MAAM,CAC3BK,SACF,CAAC;EAED,MAAM;IACJC,SAAS,EAAE;MAAEC;IAAM;EACrB,CAAC,GAAGd,KAAK;EAET,MAAMe,aAAa,GAAGtD,KAAK,CAACuD,WAAW,CAAC,MAAM;IAC5C;IACAR,qBAAQ,CAACS,MAAM,CAACP,IAAI,EAAE;MACpBQ,QAAQ,EAAE,GAAG,GAAGJ,KAAK;MACrBK,OAAO,EAAE,CAAC;MACVC,aAAa,EAAE,KAAK;MACpBC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,CAAC;;IAEV;IACA,IAAIX,QAAQ,CAACN,OAAO,EAAE;MACpBC,KAAK,CAACiB,QAAQ,CAAC,CAAC,CAAC;MACjB;MACAf,qBAAQ,CAACgB,IAAI,CAACb,QAAQ,CAACN,OAAO,CAAC,CAACiB,KAAK,CAAC,CAAC;IACzC;EACF,CAAC,EAAE,CAACR,KAAK,EAAEJ,IAAI,EAAEJ,KAAK,CAAC,CAAC;EAExB,MAAMmB,YAAY,GAAGA,CAAA,KAAM;IACzB,IAAId,QAAQ,CAACN,OAAO,EAAE;MACpBM,QAAQ,CAACN,OAAO,CAACqB,IAAI,CAAC,CAAC;IACzB;EACF,CAAC;EAEDjE,KAAK,CAACkE,SAAS,CAAC,MAAM;IACpB,IAAIhB,QAAQ,CAACN,OAAO,KAAKO,SAAS,EAAE;MAClC;MACAD,QAAQ,CAACN,OAAO,GAAGG,qBAAQ,CAACS,MAAM,CAACX,KAAK,EAAE;QACxCY,QAAQ,EAAE3B,QAAQ;QAClBqC,MAAM,EAAEC,mBAAM,CAACC,MAAM;QACrB;QACAT,eAAe,EAAEU,qBAAQ,CAACC,EAAE,KAAK,KAAK;QACtCb,OAAO,EAAE,CAAC;QACVC,aAAa,EAAE;MACjB,CAAC,CAAC;IACJ;IAEA,IAAI3B,SAAS,EAAE;MACbsB,aAAa,CAAC,CAAC;IACjB,CAAC,MAAM,IAAInB,gBAAgB,EAAE;MAC3B;MACAY,qBAAQ,CAACS,MAAM,CAACP,IAAI,EAAE;QACpBQ,QAAQ,EAAE,GAAG,GAAGJ,KAAK;QACrBK,OAAO,EAAE,CAAC;QACVE,eAAe,EAAE,IAAI;QACrBD,aAAa,EAAE;MACjB,CAAC,CAAC,CAACE,KAAK,CAACG,YAAY,CAAC;IACxB,CAAC,MAAM;MACLA,YAAY,CAAC,CAAC;IAChB;EACF,CAAC,EAAE,CAAChC,SAAS,EAAEiB,IAAI,EAAEd,gBAAgB,EAAEmB,aAAa,EAAED,KAAK,EAAER,KAAK,CAAC,CAAC;EAEpE,MAAMZ,KAAK,GAAGC,cAAc,MAAAQ,aAAA,GAAIH,KAAK,CAACiC,MAAM,cAAA9B,aAAA,uBAAZA,aAAA,CAAc+B,OAAO;EACrD,MAAMrC,IAAI,GACR,OAAOC,aAAa,KAAK,QAAQ,GAC7BA,aAAa,KAAK,OAAO,GACvB,EAAE,GACF,EAAE,GACJA,aAAa,GACbA,aAAa,GACb,EAAE;EAER,MAAMqC,MAAM,GAAI,EAAE,GAAG5C,QAAQ,GAAI,IAAI;EACrC,MAAMqC,MAAM,GAAGC,mBAAM,CAACO,MAAM,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC;EAChD,MAAMC,cAAc,GAAG;IACrBC,KAAK,EAAEzC,IAAI;IACX0C,MAAM,EAAE1C,IAAI,GAAG,CAAC;IAChB2C,QAAQ,EAAE;EACZ,CAAC;EAED,oBACE/E,KAAA,CAAAgF,aAAA,CAAC7E,YAAA,CAAA8E,IAAI,EAAAzD,QAAA;IACHc,KAAK,EAAE,CAAC4C,MAAM,CAACC,SAAS,EAAE7C,KAAK;EAAE,GAC7BG,IAAI;IACR2C,UAAU;IACVC,iBAAiB,EAAC,aAAa;IAC/BC,kBAAkB,EAAE;MAAEC,IAAI,EAAEvD;IAAU;EAAE,iBAExChC,KAAA,CAAAgF,aAAA,CAAC7E,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;IACZ3C,KAAK,EAAE,CAAC;MAAEuC,KAAK,EAAEzC,IAAI;MAAE0C,MAAM,EAAE1C,IAAI;MAAEoD,OAAO,EAAEvC;IAAK,CAAC,CAAE;IACtDwC,WAAW,EAAE;EAAM,GAElB,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,GAAG,CAAEC,KAAK,IAAK;IACrB;IACA,MAAMC,UAAU,GAAGC,KAAK,CAACC,IAAI,CAC3B,IAAID,KAAK,CAACnB,MAAM,CAAC,EACjB,CAACqB,CAAC,EAAEC,UAAU,KAAKA,UAAU,IAAItB,MAAM,GAAG,CAAC,CAC7C,CAAC;IACD,MAAMuB,WAAW,GAAGJ,KAAK,CAACC,IAAI,CAAC,IAAID,KAAK,CAACnB,MAAM,CAAC,EAAE,CAACqB,CAAC,EAAEC,UAAU,KAAK;MACnE,IAAIE,QAAQ,GAAI,CAAC,GAAGF,UAAU,IAAKtB,MAAM,GAAG,CAAC,CAAC;MAC9C,MAAMxB,QAAQ,GAAGyC,KAAK,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC,GAAG,EAAE,GAAG,GAAG,EAAE,CAAC;MAElD,IAAIO,QAAQ,GAAG,GAAG,EAAE;QAClBA,QAAQ,GAAG,GAAG,GAAGA,QAAQ;MAC3B;MAEA,MAAMC,SAAS,GAAGR,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;MAEjC,OAAO,GAAGQ,SAAS,IAAI,GAAG,GAAG,EAAE,CAAC,GAAGhC,MAAM,CAAC+B,QAAQ,CAAC,GAAGhD,QAAQ,KAAK;IACrE,CAAC,CAAC;IAEF,MAAMkD,UAAU,GAAG;MACjBvB,KAAK,EAAEzC,IAAI;MACX0C,MAAM,EAAE1C,IAAI;MACZiE,SAAS,EAAE,CACT;QACEC,MAAM,EAAEzD,KAAK,CAAC0D,WAAW,CAAC;UACxBX,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBK,WAAW,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,KAAK;QAC9D,CAAC;MACH,CAAC;IAEL,CAAC;IAED,MAAMO,aAAa,GAAG;MACpB3B,KAAK,EAAEzC,IAAI;MACX0C,MAAM,EAAE1C,IAAI;MACZiE,SAAS,EAAE,CACT;QACEI,UAAU,EAAEd,KAAK,GAAG,CAACvD,IAAI,GAAG,CAAC,GAAG;MAClC,CAAC,EACD;QACEkE,MAAM,EAAEzD,KAAK,CAAC0D,WAAW,CAAC;UAAEX,UAAU;UAAEK;QAAY,CAAC;MACvD,CAAC;IAEL,CAAC;IAED,MAAMS,WAAW,GAAGf,KAAK,GAAG;MAAEgB,GAAG,EAAEvE,IAAI,GAAG;IAAE,CAAC,GAAG,IAAI;IAEpD,MAAMwE,SAAS,GAAG;MAChB/B,KAAK,EAAEzC,IAAI;MACX0C,MAAM,EAAE1C,IAAI;MACZyE,WAAW,EAAE5E,KAAK;MAClB6E,WAAW,EAAE1E,IAAI,GAAG,EAAE;MACtB2E,YAAY,EAAE3E,IAAI,GAAG;IACvB,CAAC;IAED,oBACEpC,KAAA,CAAAgF,aAAA,CAAC7E,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;MAAC+B,GAAG,EAAErB,KAAM;MAACrD,KAAK,EAAE,CAAC4C,MAAM,CAAC+B,KAAK;IAAE,gBAC/CjH,KAAA,CAAAgF,aAAA,CAAC7E,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;MAAC3C,KAAK,EAAE8D;IAAW,gBAC/BpG,KAAA,CAAAgF,aAAA,CAAC7E,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;MACZ3C,KAAK,EAAE,CAACsC,cAAc,EAAE8B,WAAW,CAAE;MACrCjB,WAAW,EAAE;IAAM,gBAEnBzF,KAAA,CAAAgF,aAAA,CAAC7E,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;MAAC3C,KAAK,EAAEkE;IAAc,gBAClCxG,KAAA,CAAAgF,aAAA,CAAC7E,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;MAAC3C,KAAK,EAAEsC,cAAe;MAACa,WAAW,EAAE;IAAM,gBACvDzF,KAAA,CAAAgF,aAAA,CAAC7E,YAAA,CAAA4C,QAAQ,CAACkC,IAAI;MAAC3C,KAAK,EAAEsE;IAAU,CAAE,CACrB,CACF,CACF,CACF,CACF,CAAC;EAEpB,CAAC,CACY,CACX,CAAC;AAEX,CAAC;AAED,MAAM1B,MAAM,GAAGgC,uBAAU,CAACC,MAAM,CAAC;EAC/BhC,SAAS,EAAE;IACTiC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EAEDJ,KAAK,EAAE;IACL,GAAGC,uBAAU,CAACI,kBAAkB;IAEhCF,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAAC,IAAAE,QAAA,GAAAC,OAAA,CAAAzG,OAAA,GAEYgB,iBAAiB", "ignoreList": []}
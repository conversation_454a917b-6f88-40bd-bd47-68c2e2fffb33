{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_utils", "_PagerViewNativeComponent", "_interopRequireWildcard", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "_defineProperty", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "value", "enumerable", "configurable", "writable", "_toPrimitive", "Symbol", "toPrimitive", "TypeError", "String", "Number", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "Component", "constructor", "args", "props", "onPageScroll", "Platform", "OS", "keyboardDismissMode", "Keyboard", "dismiss", "onPageScrollStateChanged", "isScrolling", "nativeEvent", "pageScrollState", "onPageSelected", "selectedPage", "pager<PERSON>ie<PERSON>", "PagerViewNativeCommands", "setPage", "setPageWithoutAnimation", "scrollEnabled", "setScrollEnabledImperatively", "deducedLayoutDirection", "layoutDirection", "I18nManager", "isRTL", "render", "createElement", "ref", "style", "_onPageScroll", "_onPageScrollStateChanged", "_onPageSelected", "onMoveShouldSetResponderCapture", "_onMoveShouldSetResponderCapture", "children", "childrenWithOverriddenStyle", "exports"], "sources": ["PagerView.tsx"], "sourcesContent": ["import React from 'react';\nimport { Platform, Keyboard } from 'react-native';\nimport { I18nManager } from 'react-native';\nimport type * as ReactNative from 'react-native';\n\nimport {\n  childrenWithOverriddenStyle,\n} from './utils';\n\nimport PagerViewNativeComponent, {\n  Commands as PagerViewNativeCommands,\n  OnPageScrollEventData,\n  OnPageScrollStateChangedEventData,\n  OnPageSelectedEventData,\n  NativeProps,\n} from './PagerViewNativeComponent';\n\n\n/**\n * Container that allows to flip left and right between child views. Each\n * child view of the `PagerView` will be treated as a separate page\n * and will be stretched to fill the `PagerView`.\n *\n * It is important all children are `<View>`s and not composite components.\n * You can set style properties like `padding` or `backgroundColor` for each\n * child. It is also important that each child have a `key` prop.\n *\n * Example:\n *\n * ```\n * render: function() {\n *   return (\n *     <PagerView\n *       style={styles.PagerView}\n *       initialPage={0}>\n *       <View style={styles.pageStyle} key=\"1\">\n *         <Text>First page</Text>\n *       </View>\n *       <View style={styles.pageStyle} key=\"2\">\n *         <Text>Second page</Text>\n *       </View>\n *     </PagerView>\n *   );\n * }\n *\n * ...\n *\n * var styles = {\n *   ...\n *   PagerView: {\n *     flex: 1\n *   },\n *   pageStyle: {\n *     alignItems: 'center',\n *     padding: 20,\n *   }\n * }\n * ```\n */\n\nexport class PagerView extends React.Component<NativeProps> {\n  private isScrolling = false;\n  pagerView: React.ElementRef<typeof PagerViewNativeComponent> | null = null;\n\n\n  private get deducedLayoutDirection() {\n    if (\n      !this.props.layoutDirection ||\n      //@ts-ignore fix it\n      this.props.layoutDirection === 'locale'\n    ) {\n      return I18nManager.isRTL ? 'rtl' : 'ltr';\n    } else {\n      return this.props.layoutDirection;\n    }\n  }\n\n  private _onPageScroll = (\n    e: ReactNative.NativeSyntheticEvent<OnPageScrollEventData>\n  ) => {\n    if (this.props.onPageScroll) {\n      this.props.onPageScroll(e);\n    }\n\n    // Not implemented on iOS yet\n    if (Platform.OS === 'android') {\n      if (this.props.keyboardDismissMode === 'on-drag') {\n        Keyboard.dismiss();\n      }\n    }\n  };\n\n  private _onPageScrollStateChanged = (\n    e: ReactNative.NativeSyntheticEvent<OnPageScrollStateChangedEventData>\n  ) => {\n    if (this.props.onPageScrollStateChanged) {\n      this.props.onPageScrollStateChanged(e);\n    }\n    this.isScrolling = e.nativeEvent.pageScrollState === 'dragging';\n  };\n\n  private _onPageSelected = (\n    e: ReactNative.NativeSyntheticEvent<OnPageSelectedEventData>\n  ) => {\n    if (this.props.onPageSelected) {\n      this.props.onPageSelected(e);\n    }\n  };\n\n  private _onMoveShouldSetResponderCapture = () => {\n    return this.isScrolling;\n  };\n\n  /**\n   * A helper function to scroll to a specific page in the PagerView.\n   * The transition between pages will be animated.\n   */\n  public setPage = (selectedPage: number) => {\n    if (this.pagerView) {\n      PagerViewNativeCommands.setPage(this.pagerView, selectedPage);\n    }\n  };\n\n  /**\n   * A helper function to scroll to a specific page in the PagerView.\n   * The transition between pages will *not* be animated.\n   */\n  public setPageWithoutAnimation = (selectedPage: number) => {\n    if (this.pagerView) {\n      PagerViewNativeCommands.setPageWithoutAnimation(\n        this.pagerView,\n        selectedPage\n      );\n    }\n  };\n\n  /**\n   * A helper function to enable/disable scroll imperatively\n   * The recommended way is using the scrollEnabled prop, however, there might be a case where a\n   * imperative solution is more useful (e.g. for not blocking an animation)\n   */\n  public setScrollEnabled = (scrollEnabled: boolean) => {\n    if (this.pagerView) {\n      PagerViewNativeCommands.setScrollEnabledImperatively(\n        this.pagerView,\n        scrollEnabled\n      );\n    }\n  };\n\n  render() {\n      return (\n        <PagerViewNativeComponent\n          {...this.props}\n          ref={(ref) => {\n            this.pagerView = ref;\n          }}\n          style={this.props.style}\n          layoutDirection={this.deducedLayoutDirection}\n          onPageScroll={this._onPageScroll}\n          onPageScrollStateChanged={this._onPageScrollStateChanged}\n          onPageSelected={this._onPageSelected}\n          onMoveShouldSetResponderCapture={\n            this._onMoveShouldSetResponderCapture\n          }\n          children={childrenWithOverriddenStyle(this.props.children)}\n        />\n      );\n  }\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAIA,IAAAE,MAAA,GAAAF,OAAA;AAIA,IAAAG,yBAAA,GAAAC,uBAAA,CAAAJ,OAAA;AAMoC,SAAAK,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAF,wBAAAE,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAf,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAI,UAAA,GAAAJ,CAAA,KAAAK,OAAA,EAAAL,CAAA;AAAA,SAAAmB,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAR,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAE,CAAA,IAAAC,CAAA,OAAAY,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAD,CAAA,MAAAM,CAAA,CAAAN,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAM,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAAA,SAAAG,gBAAAzB,CAAA,EAAAE,CAAA,EAAAC,CAAA,YAAAD,CAAA,GAAAwB,cAAA,CAAAxB,CAAA,MAAAF,CAAA,GAAAW,MAAA,CAAAC,cAAA,CAAAZ,CAAA,EAAAE,CAAA,IAAAyB,KAAA,EAAAxB,CAAA,EAAAyB,UAAA,MAAAC,YAAA,MAAAC,QAAA,UAAA9B,CAAA,CAAAE,CAAA,IAAAC,CAAA,EAAAH,CAAA;AAAA,SAAA0B,eAAAvB,CAAA,QAAAc,CAAA,GAAAc,YAAA,CAAA5B,CAAA,uCAAAc,CAAA,GAAAA,CAAA,GAAAA,CAAA;AAAA,SAAAc,aAAA5B,CAAA,EAAAD,CAAA,2BAAAC,CAAA,KAAAA,CAAA,SAAAA,CAAA,MAAAH,CAAA,GAAAG,CAAA,CAAA6B,MAAA,CAAAC,WAAA,kBAAAjC,CAAA,QAAAiB,CAAA,GAAAjB,CAAA,CAAAgB,IAAA,CAAAb,CAAA,EAAAD,CAAA,uCAAAe,CAAA,SAAAA,CAAA,YAAAiB,SAAA,yEAAAhC,CAAA,GAAAiC,MAAA,GAAAC,MAAA,EAAAjC,CAAA;AAGpC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEO,MAAMkC,SAAS,SAASC,cAAK,CAACC,SAAS,CAAc;EAAAC,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAAhB,eAAA,sBACpC,KAAK;IAAAA,eAAA,oBAC2C,IAAI;IAAAA,eAAA,wBAgBxEzB,CAA0D,IACvD;MACH,IAAI,IAAI,CAAC0C,KAAK,CAACC,YAAY,EAAE;QAC3B,IAAI,CAACD,KAAK,CAACC,YAAY,CAAC3C,CAAC,CAAC;MAC5B;;MAEA;MACA,IAAI4C,qBAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;QAC7B,IAAI,IAAI,CAACH,KAAK,CAACI,mBAAmB,KAAK,SAAS,EAAE;UAChDC,qBAAQ,CAACC,OAAO,CAAC,CAAC;QACpB;MACF;IACF,CAAC;IAAAvB,eAAA,oCAGCzB,CAAsE,IACnE;MACH,IAAI,IAAI,CAAC0C,KAAK,CAACO,wBAAwB,EAAE;QACvC,IAAI,CAACP,KAAK,CAACO,wBAAwB,CAACjD,CAAC,CAAC;MACxC;MACA,IAAI,CAACkD,WAAW,GAAGlD,CAAC,CAACmD,WAAW,CAACC,eAAe,KAAK,UAAU;IACjE,CAAC;IAAA3B,eAAA,0BAGCzB,CAA4D,IACzD;MACH,IAAI,IAAI,CAAC0C,KAAK,CAACW,cAAc,EAAE;QAC7B,IAAI,CAACX,KAAK,CAACW,cAAc,CAACrD,CAAC,CAAC;MAC9B;IACF,CAAC;IAAAyB,eAAA,2CAE0C,MAAM;MAC/C,OAAO,IAAI,CAACyB,WAAW;IACzB,CAAC;IAED;AACF;AACA;AACA;IAHEzB,eAAA,kBAIkB6B,YAAoB,IAAK;MACzC,IAAI,IAAI,CAACC,SAAS,EAAE;QAClBC,kCAAuB,CAACC,OAAO,CAAC,IAAI,CAACF,SAAS,EAAED,YAAY,CAAC;MAC/D;IACF,CAAC;IAED;AACF;AACA;AACA;IAHE7B,eAAA,kCAIkC6B,YAAoB,IAAK;MACzD,IAAI,IAAI,CAACC,SAAS,EAAE;QAClBC,kCAAuB,CAACE,uBAAuB,CAC7C,IAAI,CAACH,SAAS,EACdD,YACF,CAAC;MACH;IACF,CAAC;IAED;AACF;AACA;AACA;AACA;IAJE7B,eAAA,2BAK2BkC,aAAsB,IAAK;MACpD,IAAI,IAAI,CAACJ,SAAS,EAAE;QAClBC,kCAAuB,CAACI,4BAA4B,CAClD,IAAI,CAACL,SAAS,EACdI,aACF,CAAC;MACH;IACF,CAAC;EAAA;EAnFD,IAAYE,sBAAsBA,CAAA,EAAG;IACnC,IACE,CAAC,IAAI,CAACnB,KAAK,CAACoB,eAAe;IAC3B;IACA,IAAI,CAACpB,KAAK,CAACoB,eAAe,KAAK,QAAQ,EACvC;MACA,OAAOC,wBAAW,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;IAC1C,CAAC,MAAM;MACL,OAAO,IAAI,CAACtB,KAAK,CAACoB,eAAe;IACnC;EACF;EA2EAG,MAAMA,CAAA,EAAG;IACL,oBACEzE,MAAA,CAAAa,OAAA,CAAA6D,aAAA,CAACrE,yBAAA,CAAAQ,OAAwB,EAAAc,QAAA,KACnB,IAAI,CAACuB,KAAK;MACdyB,GAAG,EAAGA,GAAG,IAAK;QACZ,IAAI,CAACZ,SAAS,GAAGY,GAAG;MACtB,CAAE;MACFC,KAAK,EAAE,IAAI,CAAC1B,KAAK,CAAC0B,KAAM;MACxBN,eAAe,EAAE,IAAI,CAACD,sBAAuB;MAC7ClB,YAAY,EAAE,IAAI,CAAC0B,aAAc;MACjCpB,wBAAwB,EAAE,IAAI,CAACqB,yBAA0B;MACzDjB,cAAc,EAAE,IAAI,CAACkB,eAAgB;MACrCC,+BAA+B,EAC7B,IAAI,CAACC,gCACN;MACDC,QAAQ,EAAE,IAAAC,kCAA2B,EAAC,IAAI,CAACjC,KAAK,CAACgC,QAAQ;IAAE,EAC5D,CAAC;EAER;AACF;AAACE,OAAA,CAAAvC,SAAA,GAAAA,SAAA", "ignoreList": []}
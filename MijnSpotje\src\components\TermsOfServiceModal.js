import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Modal,
  Alert,
} from 'react-native';
import { useTranslation } from '../i18n/useTranslation';

const TermsOfServiceModal = ({ visible, onClose, onAccept, isDark }) => {
  const { t } = useTranslation();
  const [hasScrolledToBottom, setHasScrolledToBottom] = useState(false);

  const handleScroll = ({ nativeEvent }) => {
    const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
    const isCloseToBottom = layoutMeasurement.height + contentOffset.y >= contentSize.height - 20;
    if (isCloseToBottom && !hasScrolledToBottom) {
      setHasScrolledToBottom(true);
    }
  };

  const handleAccept = () => {
    if (!hasScrolledToBottom) {
      Alert.alert(
        t('legal.terms.scrollRequired'),
        t('legal.terms.scrollRequiredMessage')
      );
      return;
    }
    onAccept();
    onClose();
  };

  const handleDecline = () => {
    Alert.alert(
      t('legal.terms.declineTitle'),
      t('legal.terms.declineMessage'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('legal.terms.decline'), style: 'destructive', onPress: onClose }
      ]
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.container, isDark && styles.containerDark]}>
        <View style={[styles.header, isDark && styles.headerDark]}>
          <TouchableOpacity onPress={handleDecline}>
            <Text style={[styles.button, styles.declineButton]}>{t('legal.terms.decline')}</Text>
          </TouchableOpacity>
          <Text style={[styles.title, isDark && styles.textDark]}>{t('legal.terms.title')}</Text>
          <TouchableOpacity 
            onPress={handleAccept}
            style={[styles.acceptButtonContainer, !hasScrolledToBottom && styles.acceptButtonDisabled]}
          >
            <Text style={[
              styles.button, 
              styles.acceptButton,
              !hasScrolledToBottom && styles.acceptButtonTextDisabled
            ]}>
              {t('legal.terms.accept')}
            </Text>
          </TouchableOpacity>
        </View>

        <ScrollView 
          style={styles.content} 
          showsVerticalScrollIndicator={true}
          onScroll={handleScroll}
          scrollEventThrottle={16}
        >
          <View style={styles.documentHeader}>
            <Text style={[styles.documentTitle, isDark && styles.textDark]}>
              {t('legal.terms.documentTitle')}
            </Text>
            <Text style={[styles.lastUpdated, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.lastUpdated')}: 15 december 2024
            </Text>
            <Text style={[styles.version, isDark && styles.textSecondaryDark]}>
              Versie 1.0
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              1. {t('legal.terms.userAgreement.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.userAgreement.content')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              2. {t('legal.terms.acceptableUse.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.acceptableUse.intro')}
            </Text>
            <View style={styles.bulletPoints}>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.terms.acceptableUse.point1')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.terms.acceptableUse.point2')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.terms.acceptableUse.point3')}
              </Text>
              <Text style={[styles.bulletPoint, isDark && styles.textSecondaryDark]}>
                • {t('legal.terms.acceptableUse.point4')}
              </Text>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              3. {t('legal.terms.contentGuidelines.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.contentGuidelines.content')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              4. {t('legal.terms.liability.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.liability.content')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              5. {t('legal.terms.termination.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.termination.content')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              6. {t('legal.terms.governingLaw.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.governingLaw.content')}
            </Text>
          </View>

          <View style={styles.section}>
            <Text style={[styles.sectionTitle, isDark && styles.textDark]}>
              7. {t('legal.terms.contact.title')}
            </Text>
            <Text style={[styles.sectionText, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.contact.content')}
            </Text>
            <Text style={[styles.contactInfo, isDark && styles.textDark]}>
              Mijn Spotje B.V.{'\n'}
              Herengracht 123{'\n'}
              1015 BG Amsterdam{'\n'}
              Nederland{'\n\n'}
              E-mail: <EMAIL>{'\n'}
              KvK: 12345678
            </Text>
          </View>

          <View style={styles.bottomPadding} />
        </ScrollView>

        {!hasScrolledToBottom && (
          <View style={[styles.scrollIndicator, isDark && styles.scrollIndicatorDark]}>
            <Text style={[styles.scrollText, isDark && styles.textSecondaryDark]}>
              {t('legal.terms.scrollToAccept')}
            </Text>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
  },
  textDark: {
    color: '#ffffff',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  button: {
    fontSize: 16,
    fontWeight: '600',
  },
  declineButton: {
    color: '#ff3b30',
  },
  acceptButtonContainer: {
    minWidth: 60,
  },
  acceptButton: {
    color: '#007AFF',
  },
  acceptButtonDisabled: {
    opacity: 0.5,
  },
  acceptButtonTextDisabled: {
    color: '#8e8e93',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  documentHeader: {
    marginBottom: 30,
    alignItems: 'center',
  },
  documentTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    textAlign: 'center',
    marginBottom: 8,
  },
  lastUpdated: {
    fontSize: 14,
    color: '#666666',
    marginBottom: 4,
  },
  version: {
    fontSize: 12,
    color: '#666666',
  },
  section: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 12,
  },
  sectionText: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 8,
  },
  bulletPoints: {
    marginLeft: 16,
  },
  bulletPoint: {
    fontSize: 14,
    color: '#666666',
    lineHeight: 20,
    marginBottom: 4,
  },
  contactInfo: {
    fontSize: 14,
    color: '#000000',
    lineHeight: 20,
    fontFamily: 'monospace',
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  bottomPadding: {
    height: 50,
  },
  scrollIndicator: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  scrollIndicatorDark: {
    backgroundColor: '#1c1c1e',
    borderTopColor: '#38383a',
  },
  scrollText: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'center',
  },
});

export default TermsOfServiceModal;

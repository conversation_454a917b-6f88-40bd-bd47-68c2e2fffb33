{"version": 3, "sources": ["State.ts"], "names": ["State", "UNDETERMINED", "FAILED", "BEGAN", "CANCELLED", "ACTIVE", "END"], "mappings": ";;;;;;AAAA;AAEO,MAAMA,KAAK,GAAG;AACnBC,EAAAA,YAAY,EAAE,CADK;AAEnBC,EAAAA,MAAM,EAAE,CAFW;AAGnBC,EAAAA,KAAK,EAAE,CAHY;AAInBC,EAAAA,SAAS,EAAE,CAJQ;AAKnBC,EAAAA,MAAM,EAAE,CALW;AAMnBC,EAAAA,GAAG,EAAE;AANc,CAAd,C,CASP", "sourcesContent": ["// TODO use State from RNModule\n\nexport const State = {\n  UNDETERMINED: 0,\n  FAILED: 1,\n  BEGAN: 2,\n  CANCELLED: 3,\n  ACTIVE: 4,\n  END: 5,\n} as const;\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\nexport type State = (typeof State)[keyof typeof State];\n"]}
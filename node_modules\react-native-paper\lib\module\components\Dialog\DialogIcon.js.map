{"version": 3, "names": ["React", "StyleSheet", "View", "useInternalTheme", "Icon", "DialogIcon", "size", "color", "icon", "theme", "themeOverrides", "isV3", "iconColor", "colors", "secondary", "createElement", "style", "styles", "wrapper", "source", "displayName", "create", "alignItems", "justifyContent", "paddingTop"], "sourceRoot": "../../../../src", "sources": ["components/Dialog/DialogIcon.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAI/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,OAAOC,IAAI,MAAsB,SAAS;AAqB1C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAGA,CAAC;EAClBC,IAAI,GAAG,EAAE;EACTC,KAAK;EACLC,IAAI;EACJC,KAAK,EAAEC;AACF,CAAC,KAAK;EACX,MAAMD,KAAK,GAAGN,gBAAgB,CAACO,cAAc,CAAC;EAE9C,IAAI,CAACD,KAAK,CAACE,IAAI,EAAE;IACf,OAAO,IAAI;EACb;;EAEA;EACA,MAAMC,SAAS,GAAGL,KAAK,IAAIE,KAAK,CAACI,MAAM,CAACC,SAAS;EAEjD,oBACEd,KAAA,CAAAe,aAAA,CAACb,IAAI;IAACc,KAAK,EAAEC,MAAM,CAACC;EAAQ,gBAC1BlB,KAAA,CAAAe,aAAA,CAACX,IAAI;IAACe,MAAM,EAAEX,IAAK;IAACD,KAAK,EAAEK,SAAU;IAACN,IAAI,EAAEA;EAAK,CAAE,CAC/C,CAAC;AAEX,CAAC;AAEDD,UAAU,CAACe,WAAW,GAAG,aAAa;AAEtC,MAAMH,MAAM,GAAGhB,UAAU,CAACoB,MAAM,CAAC;EAC/BH,OAAO,EAAE;IACPI,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd;AACF,CAAC,CAAC;AAEF,eAAenB,UAAU;;AAEzB;AACA,SAASA,UAAU", "ignoreList": []}
"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.v5 = void 0;
const v35_1 = require("./v35");
const sha1_1 = __importDefault(require("./sha1"));
exports.v5 = (0, v35_1.v35)('v5', 0x50, sha1_1.default);
//# sourceMappingURL=v5.js.map
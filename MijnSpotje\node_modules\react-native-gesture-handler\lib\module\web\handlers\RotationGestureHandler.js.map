{"version": 3, "sources": ["RotationGestureHandler.ts"], "names": ["State", "Gesture<PERSON>andler", "RotationGestureDetector", "ROTATION_RECOGNITION_THRESHOLD", "Math", "PI", "RotationGestureHandler", "onRotationBegin", "_detector", "onRotation", "detector", "previousRotation", "rotation", "delta", "<PERSON><PERSON><PERSON><PERSON>", "velocity", "abs", "state", "BEGAN", "activate", "onRotationEnd", "end", "rotationGestureListener", "init", "ref", "propsRef", "shouldCancelWhenOutside", "transformNativeEvent", "anchorX", "getAnchorX", "anchorY", "getAnchorY", "rotationGestureDetector", "cachedAnchorX", "cachedAnchorY", "onPointerDown", "event", "tracker", "addToTracker", "tryToSendTouchEvent", "onPointerAdd", "tryBegin", "onTouchEvent", "onPointerMove", "trackedPointersCount", "track", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "ACTIVE", "fail", "onPointerRemove", "UNDETERMINED", "begin", "onReset", "reset"], "mappings": ";;AAAA,SAASA,KAAT,QAAsB,aAAtB;AAGA,OAAOC,cAAP,MAA2B,kBAA3B;AACA,OAAOC,uBAAP,MAEO,sCAFP;AAIA,MAAMC,8BAA8B,GAAGC,IAAI,CAACC,EAAL,GAAU,EAAjD;AAEA,eAAe,MAAMC,sBAAN,SAAqCL,cAArC,CAAoD;AAAA;AAAA;;AAAA,sCAC9C,CAD8C;;AAAA,sCAE9C,CAF8C;;AAAA,2CAIzC,CAJyC;;AAAA,2CAKzC,CALyC;;AAAA,qDAON;AACzDM,MAAAA,eAAe,EAAGC,SAAD,IAAiD,IADT;AAEzDC,MAAAA,UAAU,EAAGC,QAAD,IAAgD;AAC1D,cAAMC,gBAAwB,GAAG,KAAKC,QAAtC;AACA,aAAKA,QAAL,IAAiBF,QAAQ,CAACE,QAA1B;AAEA,cAAMC,KAAK,GAAGH,QAAQ,CAACI,SAAvB;;AAEA,YAAID,KAAK,GAAG,CAAZ,EAAe;AACb,eAAKE,QAAL,GAAgB,CAAC,KAAKH,QAAL,GAAgBD,gBAAjB,IAAqCE,KAArD;AACD;;AAED,YACET,IAAI,CAACY,GAAL,CAAS,KAAKJ,QAAd,KAA2BT,8BAA3B,IACA,KAAKc,KAAL,KAAejB,KAAK,CAACkB,KAFvB,EAGE;AACA,eAAKC,QAAL;AACD;;AAED,eAAO,IAAP;AACD,OApBwD;AAqBzDC,MAAAA,aAAa,EAAGZ,SAAD,IAA8C;AAC3D,aAAKa,GAAL;AACD;AAvBwD,KAPM;;AAAA,qDAkC/D,IAAInB,uBAAJ,CAA4B,KAAKoB,uBAAjC,CAlC+D;AAAA;;AAoC1DC,EAAAA,IAAI,CAACC,GAAD,EAAcC,QAAd,EAAwD;AACjE,UAAMF,IAAN,CAAWC,GAAX,EAAgBC,QAAhB;AAEA,SAAKC,uBAAL,GAA+B,KAA/B;AACD;;AAESC,EAAAA,oBAAoB,GAAG;AAC/B,WAAO;AACLf,MAAAA,QAAQ,EAAE,KAAKA,QAAL,GAAgB,KAAKA,QAArB,GAAgC,CADrC;AAELgB,MAAAA,OAAO,EAAE,KAAKC,UAAL,EAFJ;AAGLC,MAAAA,OAAO,EAAE,KAAKC,UAAL,EAHJ;AAILhB,MAAAA,QAAQ,EAAE,KAAKA,QAAL,GAAgB,KAAKA,QAArB,GAAgC;AAJrC,KAAP;AAMD;;AAEMc,EAAAA,UAAU,GAAW;AAC1B,UAAMD,OAAO,GAAG,KAAKI,uBAAL,CAA6BJ,OAA7C;AAEA,WAAOA,OAAO,GAAGA,OAAH,GAAa,KAAKK,aAAhC;AACD;;AAEMF,EAAAA,UAAU,GAAW;AAC1B,UAAMD,OAAO,GAAG,KAAKE,uBAAL,CAA6BF,OAA7C;AAEA,WAAOA,OAAO,GAAGA,OAAH,GAAa,KAAKI,aAAhC;AACD;;AAESC,EAAAA,aAAa,CAACC,KAAD,EAA4B;AACjD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMD,aAAN,CAAoBC,KAApB;AAEA,SAAKG,mBAAL,CAAyBH,KAAzB;AACD;;AAESI,EAAAA,YAAY,CAACJ,KAAD,EAA4B;AAChD,SAAKC,OAAL,CAAaC,YAAb,CAA0BF,KAA1B;AACA,UAAMI,YAAN,CAAmBJ,KAAnB;AAEA,SAAKK,QAAL;AACA,SAAKT,uBAAL,CAA6BU,YAA7B,CAA0CN,KAA1C,EAAiD,KAAKC,OAAtD;AACD;;AAESM,EAAAA,aAAa,CAACP,KAAD,EAA4B;AACjD,QAAI,KAAKC,OAAL,CAAaO,oBAAb,GAAoC,CAAxC,EAA2C;AACzC;AACD;;AAED,QAAI,KAAKf,UAAL,EAAJ,EAAuB;AACrB,WAAKI,aAAL,GAAqB,KAAKJ,UAAL,EAArB;AACD;;AACD,QAAI,KAAKE,UAAL,EAAJ,EAAuB;AACrB,WAAKG,aAAL,GAAqB,KAAKH,UAAL,EAArB;AACD;;AAED,SAAKM,OAAL,CAAaQ,KAAb,CAAmBT,KAAnB;AAEA,SAAKJ,uBAAL,CAA6BU,YAA7B,CAA0CN,KAA1C,EAAiD,KAAKC,OAAtD;AAEA,UAAMM,aAAN,CAAoBP,KAApB;AACD;;AAESU,EAAAA,oBAAoB,CAACV,KAAD,EAA4B;AACxD,QAAI,KAAKC,OAAL,CAAaO,oBAAb,GAAoC,CAAxC,EAA2C;AACzC;AACD;;AAED,QAAI,KAAKf,UAAL,EAAJ,EAAuB;AACrB,WAAKI,aAAL,GAAqB,KAAKJ,UAAL,EAArB;AACD;;AACD,QAAI,KAAKE,UAAL,EAAJ,EAAuB;AACrB,WAAKG,aAAL,GAAqB,KAAKH,UAAL,EAArB;AACD;;AAED,SAAKM,OAAL,CAAaQ,KAAb,CAAmBT,KAAnB;AAEA,SAAKJ,uBAAL,CAA6BU,YAA7B,CAA0CN,KAA1C,EAAiD,KAAKC,OAAtD;AAEA,UAAMS,oBAAN,CAA2BV,KAA3B;AACD;;AAESW,EAAAA,WAAW,CAACX,KAAD,EAA4B;AAC/C,UAAMW,WAAN,CAAkBX,KAAlB;AACA,SAAKC,OAAL,CAAaW,iBAAb,CAA+BZ,KAAK,CAACa,SAArC;AACA,SAAKjB,uBAAL,CAA6BU,YAA7B,CAA0CN,KAA1C,EAAiD,KAAKC,OAAtD;;AAEA,QAAI,KAAKpB,KAAL,KAAejB,KAAK,CAACkD,MAAzB,EAAiC;AAC/B;AACD;;AAED,QAAI,KAAKjC,KAAL,KAAejB,KAAK,CAACkD,MAAzB,EAAiC;AAC/B,WAAK7B,GAAL;AACD,KAFD,MAEO;AACL,WAAK8B,IAAL;AACD;AACF;;AAESC,EAAAA,eAAe,CAAChB,KAAD,EAA4B;AACnD,UAAMgB,eAAN,CAAsBhB,KAAtB;AACA,SAAKJ,uBAAL,CAA6BU,YAA7B,CAA0CN,KAA1C,EAAiD,KAAKC,OAAtD;AACA,SAAKA,OAAL,CAAaW,iBAAb,CAA+BZ,KAAK,CAACa,SAArC;AACD;;AAESR,EAAAA,QAAQ,GAAS;AACzB,QAAI,KAAKxB,KAAL,KAAejB,KAAK,CAACqD,YAAzB,EAAuC;AACrC;AACD;;AAED,SAAKC,KAAL;AACD;;AAESC,EAAAA,OAAO,GAAS;AACxB,QAAI,KAAKtC,KAAL,KAAejB,KAAK,CAACkD,MAAzB,EAAiC;AAC/B;AACD;;AAED,SAAKtC,QAAL,GAAgB,CAAhB;AACA,SAAKG,QAAL,GAAgB,CAAhB;AACA,SAAKiB,uBAAL,CAA6BwB,KAA7B;AACD;;AA1JgE", "sourcesContent": ["import { State } from '../../State';\nimport { AdaptedEvent } from '../interfaces';\n\nimport GestureHandler from './GestureHandler';\nimport RotationGestureDetector, {\n  RotationGestureListener,\n} from '../detectors/RotationGestureDetector';\n\nconst ROTATION_RECOGNITION_THRESHOLD = Math.PI / 36;\n\nexport default class RotationGestureHandler extends GestureHandler {\n  private rotation = 0;\n  private velocity = 0;\n\n  private cachedAnchorX = 0;\n  private cachedAnchorY = 0;\n\n  private rotationGestureListener: RotationGestureListener = {\n    onRotationBegin: (_detector: RotationGestureDetector): boolean => true,\n    onRotation: (detector: RotationGestureDetector): boolean => {\n      const previousRotation: number = this.rotation;\n      this.rotation += detector.rotation;\n\n      const delta = detector.timeDelta;\n\n      if (delta > 0) {\n        this.velocity = (this.rotation - previousRotation) / delta;\n      }\n\n      if (\n        Math.abs(this.rotation) >= ROTATION_RECOGNITION_THRESHOLD &&\n        this.state === State.BEGAN\n      ) {\n        this.activate();\n      }\n\n      return true;\n    },\n    onRotationEnd: (_detector: RotationGestureDetector): void => {\n      this.end();\n    },\n  };\n\n  private rotationGestureDetector: RotationGestureDetector =\n    new RotationGestureDetector(this.rotationGestureListener);\n\n  public init(ref: number, propsRef: React.RefObject<unknown>): void {\n    super.init(ref, propsRef);\n\n    this.shouldCancelWhenOutside = false;\n  }\n\n  protected transformNativeEvent() {\n    return {\n      rotation: this.rotation ? this.rotation : 0,\n      anchorX: this.getAnchorX(),\n      anchorY: this.getAnchorY(),\n      velocity: this.velocity ? this.velocity : 0,\n    };\n  }\n\n  public getAnchorX(): number {\n    const anchorX = this.rotationGestureDetector.anchorX;\n\n    return anchorX ? anchorX : this.cachedAnchorX;\n  }\n\n  public getAnchorY(): number {\n    const anchorY = this.rotationGestureDetector.anchorY;\n\n    return anchorY ? anchorY : this.cachedAnchorY;\n  }\n\n  protected onPointerDown(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerDown(event);\n\n    this.tryToSendTouchEvent(event);\n  }\n\n  protected onPointerAdd(event: AdaptedEvent): void {\n    this.tracker.addToTracker(event);\n    super.onPointerAdd(event);\n\n    this.tryBegin();\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    if (this.tracker.trackedPointersCount < 2) {\n      return;\n    }\n\n    if (this.getAnchorX()) {\n      this.cachedAnchorX = this.getAnchorX();\n    }\n    if (this.getAnchorY()) {\n      this.cachedAnchorY = this.getAnchorY();\n    }\n\n    this.tracker.track(event);\n\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerOutOfBounds(event: AdaptedEvent): void {\n    if (this.tracker.trackedPointersCount < 2) {\n      return;\n    }\n\n    if (this.getAnchorX()) {\n      this.cachedAnchorX = this.getAnchorX();\n    }\n    if (this.getAnchorY()) {\n      this.cachedAnchorY = this.getAnchorY();\n    }\n\n    this.tracker.track(event);\n\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n\n    super.onPointerOutOfBounds(event);\n  }\n\n  protected onPointerUp(event: AdaptedEvent): void {\n    super.onPointerUp(event);\n    this.tracker.removeFromTracker(event.pointerId);\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n\n    if (this.state !== State.ACTIVE) {\n      return;\n    }\n\n    if (this.state === State.ACTIVE) {\n      this.end();\n    } else {\n      this.fail();\n    }\n  }\n\n  protected onPointerRemove(event: AdaptedEvent): void {\n    super.onPointerRemove(event);\n    this.rotationGestureDetector.onTouchEvent(event, this.tracker);\n    this.tracker.removeFromTracker(event.pointerId);\n  }\n\n  protected tryBegin(): void {\n    if (this.state !== State.UNDETERMINED) {\n      return;\n    }\n\n    this.begin();\n  }\n\n  protected onReset(): void {\n    if (this.state === State.ACTIVE) {\n      return;\n    }\n\n    this.rotation = 0;\n    this.velocity = 0;\n    this.rotationGestureDetector.reset();\n  }\n}\n"]}
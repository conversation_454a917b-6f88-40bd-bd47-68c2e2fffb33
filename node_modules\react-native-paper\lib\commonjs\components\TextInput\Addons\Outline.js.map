{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "Outline", "isV3", "label", "activeColor", "backgroundColor", "hasActiveOutline", "focused", "outlineColor", "roundness", "style", "createElement", "View", "testID", "pointerEvents", "styles", "outline", "noLabelOutline", "borderRadius", "borderWidth", "borderColor", "exports", "StyleSheet", "create", "position", "left", "right", "top", "bottom"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Addons/Outline.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAMsB,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAgBf,MAAMkB,OAAO,GAAGA,CAAC;EACtBC,IAAI;EACJC,KAAK;EACLC,WAAW;EACXC,eAAe;EACfC,gBAAgB;EAChBC,OAAO;EACPC,YAAY;EACZC,SAAS;EACTC;AACY,CAAC,kBACbhC,KAAA,CAAAiC,aAAA,CAAC9B,YAAA,CAAA+B,IAAI;EACHC,MAAM,EAAC,oBAAoB;EAC3BC,aAAa,EAAC,MAAM;EACpBJ,KAAK,EAAE,CACLK,MAAM,CAACC,OAAO,EACd,CAACb,KAAK,IAAIY,MAAM,CAACE,cAAc;EAC/B;EACA;IACEZ,eAAe;IACfa,YAAY,EAAET,SAAS;IACvBU,WAAW,EAAE,CAACjB,IAAI,GAAGI,gBAAgB,GAAGC,OAAO,IAAI,CAAC,GAAG,CAAC;IACxDa,WAAW,EAAEd,gBAAgB,GAAGF,WAAW,GAAGI;EAChD,CAAC,EACDE,KAAK;AACL,CACH,CACF;AAACW,OAAA,CAAApB,OAAA,GAAAA,OAAA;AAEF,MAAMc,MAAM,GAAGO,uBAAU,CAACC,MAAM,CAAC;EAC/BP,OAAO,EAAE;IACPQ,QAAQ,EAAE,UAAU;IACpBC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE,CAAC;IACNC,MAAM,EAAE;EACV,CAAC;EACDX,cAAc,EAAE;IACdU,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}
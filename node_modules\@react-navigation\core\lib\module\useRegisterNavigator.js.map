{"version": 3, "names": ["nanoid", "React", "SingleNavigatorContext", "useRegisterNavigator", "key", "useState", "container", "useContext", "undefined", "Error", "useEffect", "register", "unregister"], "sourceRoot": "../../src", "sources": ["useRegisterNavigator.tsx"], "mappings": ";;AAAA,SAASA,MAAM,QAAQ,mBAAmB;AAC1C,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,SAASC,sBAAsB,QAAQ,4BAAyB;;AAEhE;AACA;AACA;AACA;AACA,OAAO,SAASC,oBAAoBA,CAAA,EAAG;EACrC,MAAM,CAACC,GAAG,CAAC,GAAGH,KAAK,CAACI,QAAQ,CAAC,MAAML,MAAM,CAAC,CAAC,CAAC;EAC5C,MAAMM,SAAS,GAAGL,KAAK,CAACM,UAAU,CAACL,sBAAsB,CAAC;EAE1D,IAAII,SAAS,KAAKE,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,wLACF,CAAC;EACH;EAEAR,KAAK,CAACS,SAAS,CAAC,MAAM;IACpB,MAAM;MAAEC,QAAQ;MAAEC;IAAW,CAAC,GAAGN,SAAS;IAE1CK,QAAQ,CAACP,GAAG,CAAC;IAEb,OAAO,MAAMQ,UAAU,CAACR,GAAG,CAAC;EAC9B,CAAC,EAAE,CAACE,SAAS,EAAEF,GAAG,CAAC,CAAC;EAEpB,OAAOA,GAAG;AACZ", "ignoreList": []}
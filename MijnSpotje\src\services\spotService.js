import { supabase, TABLES, BUCKETS } from '../config/supabase';
import * as FileSystem from 'expo-file-system';

export const spotService = {
  // Create a new spot
  async createSpot(spotData, images = []) {
    try {
      const { data: spot, error: spotError } = await supabase
        .from(TABLES.SPOTS)
        .insert([spotData])
        .select()
        .single();

      if (spotError) throw spotError;

      // Upload images if provided
      if (images.length > 0) {
        const imagePromises = images.map((image, index) =>
          this.uploadSpotImage(spot.id, image, index)
        );
        
        const imageResults = await Promise.all(imagePromises);
        const successfulUploads = imageResults.filter(result => !result.error);
        
        if (successfulUploads.length > 0) {
          const { error: imagesError } = await supabase
            .from(TABLES.SPOT_IMAGES)
            .insert(
              successfulUploads.map(result => ({
                spot_id: spot.id,
                image_url: result.publicUrl,
                order_index: result.index,
              }))
            );

          if (imagesError) console.warn('Error saving image records:', imagesError);
        }
      }

      return { data: spot, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Upload spot image to storage
  async uploadSpotImage(spotId, imageUri, index = 0) {
    try {
      const fileExtension = imageUri.split('.').pop();
      const fileName = `${spotId}_${index}_${Date.now()}.${fileExtension}`;
      const filePath = `${spotId}/${fileName}`;

      // Read file as base64
      const base64 = await FileSystem.readAsStringAsync(imageUri, {
        encoding: FileSystem.EncodingType.Base64,
      });

      // Convert base64 to blob
      const byteCharacters = atob(base64);
      const byteNumbers = new Array(byteCharacters.length);
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
      }
      const byteArray = new Uint8Array(byteNumbers);
      const blob = new Blob([byteArray], { type: `image/${fileExtension}` });

      const { data, error } = await supabase.storage
        .from(BUCKETS.SPOT_IMAGES)
        .upload(filePath, blob);

      if (error) throw error;

      const { data: { publicUrl } } = supabase.storage
        .from(BUCKETS.SPOT_IMAGES)
        .getPublicUrl(filePath);

      return { publicUrl, index, error: null };
    } catch (error) {
      return { publicUrl: null, index, error };
    }
  },

  // Get spots with filters
  async getSpots(filters = {}) {
    try {
      let query = supabase
        .from(TABLES.SPOTS)
        .select(`
          *,
          spot_images (
            image_url,
            order_index
          ),
          users (
            username,
            avatar_url
          ),
          ratings (
            rating
          )
        `);

      // Apply filters
      if (filters.groupId) {
        query = query.eq('group_id', filters.groupId);
      }

      if (filters.userId) {
        query = query.eq('user_id', filters.userId);
      }

      if (filters.bounds) {
        const { north, south, east, west } = filters.bounds;
        query = query
          .gte('latitude', south)
          .lte('latitude', north)
          .gte('longitude', west)
          .lte('longitude', east);
      }

      if (filters.category) {
        query = query.eq('category', filters.category);
      }

      // Order by creation date (newest first)
      query = query.order('created_at', { ascending: false });

      // Apply limit
      if (filters.limit) {
        query = query.limit(filters.limit);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Calculate average ratings
      const spotsWithRatings = data.map(spot => ({
        ...spot,
        averageRating: spot.ratings.length > 0
          ? spot.ratings.reduce((sum, r) => sum + r.rating, 0) / spot.ratings.length
          : 0,
        ratingCount: spot.ratings.length,
        images: spot.spot_images.sort((a, b) => a.order_index - b.order_index),
      }));

      return { data: spotsWithRatings, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get single spot by ID
  async getSpotById(spotId) {
    try {
      const { data, error } = await supabase
        .from(TABLES.SPOTS)
        .select(`
          *,
          spot_images (
            image_url,
            order_index
          ),
          users (
            username,
            avatar_url,
            level
          ),
          ratings (
            rating,
            user_id,
            users (
              username,
              avatar_url
            )
          ),
          comments (
            id,
            comment,
            created_at,
            user_id,
            users (
              username,
              avatar_url
            )
          )
        `)
        .eq('id', spotId)
        .single();

      if (error) throw error;

      // Process the data
      const spot = {
        ...data,
        averageRating: data.ratings.length > 0
          ? data.ratings.reduce((sum, r) => sum + r.rating, 0) / data.ratings.length
          : 0,
        ratingCount: data.ratings.length,
        images: data.spot_images.sort((a, b) => a.order_index - b.order_index),
        comments: data.comments.sort((a, b) => new Date(b.created_at) - new Date(a.created_at)),
      };

      return { data: spot, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Rate a spot
  async rateSpot(spotId, userId, rating) {
    try {
      const { data, error } = await supabase
        .from(TABLES.RATINGS)
        .upsert([
          {
            spot_id: spotId,
            user_id: userId,
            rating: rating,
            created_at: new Date().toISOString(),
          }
        ], {
          onConflict: 'spot_id,user_id'
        });

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Add comment to spot
  async addComment(spotId, userId, comment) {
    try {
      const { data, error } = await supabase
        .from(TABLES.COMMENTS)
        .insert([
          {
            spot_id: spotId,
            user_id: userId,
            comment: comment,
            created_at: new Date().toISOString(),
          }
        ])
        .select(`
          *,
          users (
            username,
            avatar_url
          )
        `)
        .single();

      if (error) throw error;
      return { data, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },

  // Toggle favorite
  async toggleFavorite(spotId, userId) {
    try {
      // Check if already favorited
      const { data: existing } = await supabase
        .from(TABLES.FAVORITES)
        .select('id')
        .eq('spot_id', spotId)
        .eq('user_id', userId)
        .single();

      if (existing) {
        // Remove favorite
        const { error } = await supabase
          .from(TABLES.FAVORITES)
          .delete()
          .eq('spot_id', spotId)
          .eq('user_id', userId);

        if (error) throw error;
        return { data: { isFavorited: false }, error: null };
      } else {
        // Add favorite
        const { data, error } = await supabase
          .from(TABLES.FAVORITES)
          .insert([
            {
              spot_id: spotId,
              user_id: userId,
              created_at: new Date().toISOString(),
            }
          ]);

        if (error) throw error;
        return { data: { isFavorited: true }, error: null };
      }
    } catch (error) {
      return { data: null, error };
    }
  },

  // Get user's favorite spots
  async getFavoriteSpots(userId) {
    try {
      const { data, error } = await supabase
        .from(TABLES.FAVORITES)
        .select(`
          spots (
            *,
            spot_images (
              image_url,
              order_index
            ),
            users (
              username,
              avatar_url
            ),
            ratings (
              rating
            )
          )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const favoriteSpots = data.map(fav => ({
        ...fav.spots,
        averageRating: fav.spots.ratings.length > 0
          ? fav.spots.ratings.reduce((sum, r) => sum + r.rating, 0) / fav.spots.ratings.length
          : 0,
        ratingCount: fav.spots.ratings.length,
        images: fav.spots.spot_images.sort((a, b) => a.order_index - b.order_index),
      }));

      return { data: favoriteSpots, error: null };
    } catch (error) {
      return { data: null, error };
    }
  },
};

{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "useLayout", "layout", "setLayout", "useState", "height", "width", "measured", "onLayout", "useCallback", "nativeEvent"], "sourceRoot": "../../../src", "sources": ["utils/useLayout.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AAA+B,SAAAD,wBAAAE,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAJ,uBAAA,YAAAA,CAAAE,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAGhB,SAASkB,SAASA,CAAA,EAAG;EAClC,MAAM,CAACC,MAAM,EAAEC,SAAS,CAAC,GAAGxB,KAAK,CAACyB,QAAQ,CAIvC;IAAEC,MAAM,EAAE,CAAC;IAAEC,KAAK,EAAE,CAAC;IAAEC,QAAQ,EAAE;EAAM,CAAC,CAAC;EAE5C,MAAMC,QAAQ,GAAG7B,KAAK,CAAC8B,WAAW,CAC/B3B,CAAoB,IAAK;IACxB,MAAM;MAAEuB,MAAM;MAAEC;IAAM,CAAC,GAAGxB,CAAC,CAAC4B,WAAW,CAACR,MAAM;IAE9C,IAAIG,MAAM,KAAKH,MAAM,CAACG,MAAM,IAAIC,KAAK,KAAKJ,MAAM,CAACI,KAAK,EAAE;MACtD;IACF;IAEAH,SAAS,CAAC;MACRE,MAAM;MACNC,KAAK;MACLC,QAAQ,EAAE;IACZ,CAAC,CAAC;EACJ,CAAC,EACD,CAACL,MAAM,CAACG,MAAM,EAAEH,MAAM,CAACI,KAAK,CAC9B,CAAC;EAED,OAAO,CAACJ,MAAM,EAAEM,QAAQ,CAAC;AAC3B", "ignoreList": []}
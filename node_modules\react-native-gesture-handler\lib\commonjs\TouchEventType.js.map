{"version": 3, "sources": ["TouchEventType.ts"], "names": ["TouchEventType", "UNDETERMINED", "TOUCHES_DOWN", "TOUCHES_MOVE", "TOUCHES_UP", "TOUCHES_CANCELLED"], "mappings": ";;;;;;AAAO,MAAMA,cAAc,GAAG;AAC5BC,EAAAA,YAAY,EAAE,CADc;AAE5BC,EAAAA,YAAY,EAAE,CAFc;AAG5BC,EAAAA,YAAY,EAAE,CAHc;AAI5BC,EAAAA,UAAU,EAAE,CAJgB;AAK5BC,EAAAA,iBAAiB,EAAE;AALS,CAAvB,C,CAQP", "sourcesContent": ["export const TouchEventType = {\n  UNDETERMINED: 0,\n  TOUCHES_DOWN: 1,\n  TOUCHES_MOVE: 2,\n  TOUCHES_UP: 3,\n  TOUCHES_CANCELLED: 4,\n} as const;\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\nexport type TouchEventType =\n  (typeof TouchEventType)[keyof typeof TouchEventType];\n"]}
{"name": "@react-navigation/elements", "description": "UI Components for React Navigation", "version": "2.4.3", "keywords": ["react-native", "react-navigation", "ios", "android"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/elements"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org", "source": "./src/index.tsx", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "publishConfig": {"access": "public"}, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"color": "^4.2.3"}, "devDependencies": {"@jest/globals": "^29.7.0", "@react-native-masked-view/masked-view": "0.3.2", "@react-navigation/native": "^7.1.10", "@testing-library/react-native": "^13.2.0", "@types/react": "~19.0.10", "del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.2", "react-native-builder-bob": "^0.40.9", "react-test-renderer": "19.0.0", "typescript": "^5.8.3"}, "peerDependencies": {"@react-native-masked-view/masked-view": ">= 0.2.0", "@react-navigation/native": "^7.1.10", "react": ">= 18.2.0", "react-native": "*", "react-native-safe-area-context": ">= 4.0.0"}, "peerDependenciesMeta": {"@react-native-masked-view/masked-view": {"optional": true}}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "52e5ec64745b399655adf7bd9773e31372d51e62"}
// App Testing Utility for Development
// This utility helps test all app functionality programmatically

import StorageService from '../services/StorageService';
import GroupsService from '../services/GroupsService';
import DeepLinkService from '../services/DeepLinkService';

class AppTester {
  constructor() {
    this.testResults = [];
  }

  // Log test result
  logTest(testName, success, details = '') {
    const result = {
      test: testName,
      success,
      details,
      timestamp: new Date().toISOString(),
    };
    
    this.testResults.push(result);
    
    if (success) {
      console.log(`✅ ${testName}: PASSED ${details}`);
    } else {
      console.error(`❌ ${testName}: FAILED ${details}`);
    }
    
    return success;
  }

  // Test storage functionality
  async testStorage() {
    try {
      // Test spot storage
      const testSpot = {
        id: 'test_spot_' + Date.now(),
        title: 'Test Spot',
        description: 'Test Description',
        category: 'restaurant',
        rating: 4.5,
        location: 'Test Location',
        createdAt: new Date().toISOString(),
      };

      // Add spot
      const savedSpot = await StorageService.addSpot(testSpot);
      if (!savedSpot) {
        return this.logTest('Storage - Add Spot', false, 'Failed to save spot');
      }

      // Get spots
      const spots = await StorageService.getSpots();
      const foundSpot = spots.find(s => s.id === testSpot.id);
      if (!foundSpot) {
        return this.logTest('Storage - Get Spots', false, 'Spot not found after saving');
      }

      // Delete spot
      const deleted = await StorageService.deleteSpot(testSpot.id);
      if (!deleted) {
        return this.logTest('Storage - Delete Spot', false, 'Failed to delete spot');
      }

      return this.logTest('Storage', true, 'All storage operations successful');
    } catch (error) {
      return this.logTest('Storage', false, error.message);
    }
  }

  // Test groups functionality
  async testGroups() {
    try {
      // Create test group
      const testGroupData = {
        name: 'Test Group',
        description: 'Test group for functionality testing',
      };

      const group = await GroupsService.createGroup(testGroupData);
      if (!group || !group.id) {
        return this.logTest('Groups - Create', false, 'Failed to create group');
      }

      // Get groups
      const groups = await GroupsService.getUserGroups();
      const foundGroup = groups.find(g => g.id === group.id);
      if (!foundGroup) {
        return this.logTest('Groups - Get Groups', false, 'Group not found after creation');
      }

      // Test group spots
      const testSpot = {
        id: 'test_group_spot_' + Date.now(),
        title: 'Test Group Spot',
        description: 'Test spot for group',
        category: 'cafe',
        rating: 4.0,
      };

      const groupSpot = await GroupsService.addSpotToGroup(group.id, testSpot);
      if (!groupSpot) {
        return this.logTest('Groups - Add Spot', false, 'Failed to add spot to group');
      }

      // Get group spots
      const groupSpots = await GroupsService.getGroupSpots(group.id);
      if (!Array.isArray(groupSpots) || groupSpots.length === 0) {
        return this.logTest('Groups - Get Spots', false, 'Failed to retrieve group spots');
      }

      // Clean up - delete group
      await GroupsService.deleteGroup(group.id);

      return this.logTest('Groups', true, 'All group operations successful');
    } catch (error) {
      return this.logTest('Groups', false, error.message);
    }
  }

  // Test deep linking functionality
  async testDeepLinking() {
    try {
      // Test URL generation
      const testSpot = { id: 'test_spot_123' };
      const spotLink = DeepLinkService.generateSpotLink(testSpot);
      
      if (!spotLink || !spotLink.deepLink || !spotLink.deepLink.includes('mijnspotje://')) {
        return this.logTest('Deep Linking - Generate Spot Link', false, 'Invalid spot link generated');
      }

      // Test URL parsing
      const testUrl = 'mijnspotje://spot/test_spot_123';
      const parsed = DeepLinkService.parseDeepLink(testUrl);
      
      if (!parsed || parsed.type !== 'spot' || parsed.spotId !== 'test_spot_123') {
        return this.logTest('Deep Linking - Parse URL', false, 'Failed to parse deep link correctly');
      }

      // Test group invite link
      const inviteLink = DeepLinkService.generateInviteLink('ABC123');
      if (!inviteLink || !inviteLink.deepLink || !inviteLink.deepLink.includes('invite/ABC123')) {
        return this.logTest('Deep Linking - Generate Invite Link', false, 'Invalid invite link generated');
      }

      return this.logTest('Deep Linking', true, 'All deep linking operations successful');
    } catch (error) {
      return this.logTest('Deep Linking', false, error.message);
    }
  }

  // Test app initialization
  async testAppInitialization() {
    try {
      // Test deep linking initialization
      const subscription = await DeepLinkService.initialize();
      
      // Test if scheme is correct
      const scheme = DeepLinkService.getScheme();
      if (scheme !== 'mijnspotje') {
        return this.logTest('App Init - Deep Link Scheme', false, `Wrong scheme: ${scheme}`);
      }

      return this.logTest('App Initialization', true, 'App initialized successfully');
    } catch (error) {
      return this.logTest('App Initialization', false, error.message);
    }
  }

  // Run all tests
  async runAllTests() {
    console.log('🧪 Starting comprehensive app testing...');
    
    const tests = [
      () => this.testAppInitialization(),
      () => this.testStorage(),
      () => this.testGroups(),
      () => this.testDeepLinking(),
    ];

    let passedTests = 0;
    let totalTests = tests.length;

    for (const test of tests) {
      try {
        const result = await test();
        if (result) passedTests++;
      } catch (error) {
        console.error('❌ Test execution error:', error);
      }
    }

    // Summary
    console.log('\n📊 Test Summary:');
    console.log(`✅ Passed: ${passedTests}/${totalTests}`);
    console.log(`❌ Failed: ${totalTests - passedTests}/${totalTests}`);
    
    if (passedTests === totalTests) {
      console.log('🎉 All tests passed! App is ready for use.');
    } else {
      console.log('⚠️ Some tests failed. Check the logs above for details.');
    }

    return {
      passed: passedTests,
      total: totalTests,
      success: passedTests === totalTests,
      results: this.testResults,
    };
  }

  // Get test results
  getResults() {
    return this.testResults;
  }

  // Clear test results
  clearResults() {
    this.testResults = [];
  }

  // Test specific functionality (for debugging)
  async testSpecific(testName) {
    switch (testName.toLowerCase()) {
      case 'storage':
        return await this.testStorage();
      case 'groups':
        return await this.testGroups();
      case 'deeplinking':
        return await this.testDeepLinking();
      case 'init':
        return await this.testAppInitialization();
      default:
        console.warn(`⚠️ Unknown test: ${testName}`);
        return false;
    }
  }
}

// Create singleton instance
const appTester = new AppTester();

// Development helper - run tests automatically in dev mode
if (__DEV__) {
  // Auto-run tests after a delay to ensure app is loaded
  setTimeout(() => {
    console.log('🔧 Development mode detected - running app tests...');
    appTester.runAllTests().catch(error => {
      console.error('❌ Auto-test failed:', error);
    });
  }, 3000); // 3 second delay
}

// Export singleton instance
export default appTester;

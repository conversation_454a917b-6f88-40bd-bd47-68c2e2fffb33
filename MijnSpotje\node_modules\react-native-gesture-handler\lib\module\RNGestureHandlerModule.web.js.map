{"version": 3, "sources": ["RNGestureHandlerModule.web.ts"], "names": ["React", "isNewWebImplementationEnabled", "Gestures", "HammerGestures", "InteractionManager", "NodeManager", "HammerNodeManager", "GestureHandlerWebDelegate", "shouldPreventDrop", "handleSetJSResponder", "tag", "blockNativeResponder", "console", "warn", "handleClearJSResponder", "createGestureHandler", "handler<PERSON>ame", "handlerTag", "config", "Error", "GestureClass", "instance", "configureInteractions", "<PERSON><PERSON><PERSON><PERSON>", "updateGestureHandler", "attachGestureHandler", "newView", "_actionType", "propsRef", "Element", "Component", "handler", "constructor", "name", "init", "<PERSON><PERSON><PERSON><PERSON>", "newConfig", "updateGestureConfig", "getGestureHandlerNode", "dropGestureHandler", "flushOperations"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAGA,SAASC,6BAAT,QAA8C,8BAA9C;AACA,SAASC,QAAT,EAAmBC,cAAnB,QAAyC,gBAAzC;AAEA,OAAOC,kBAAP,MAA+B,gCAA/B;AACA,OAAOC,WAAP,MAAwB,yBAAxB;AACA,OAAO,KAAKC,iBAAZ,MAAmC,0BAAnC;AACA,SAASC,yBAAT,QAA0C,uCAA1C,C,CAEA;AACA;AACA;AACA;AACA;;AACA,IAAIC,iBAAiB,GAAG,KAAxB;AAEA,eAAe;AACbC,EAAAA,oBAAoB,CAACC,GAAD,EAAcC,oBAAd,EAA6C;AAC/DC,IAAAA,OAAO,CAACC,IAAR,CAAa,wBAAb,EAAuCH,GAAvC,EAA4CC,oBAA5C;AACD,GAHY;;AAIbG,EAAAA,sBAAsB,GAAG;AACvBF,IAAAA,OAAO,CAACC,IAAR,CAAa,0BAAb;AACD,GANY;;AAObE,EAAAA,oBAAoB,CAClBC,WADkB,EAElBC,UAFkB,EAGlBC,MAHkB,EAIlB;AACA,QAAIjB,6BAA6B,EAAjC,EAAqC;AACnC,UAAI,EAAEe,WAAW,IAAId,QAAjB,CAAJ,EAAgC;AAC9B,cAAM,IAAIiB,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD;;AAED,YAAMI,YAAY,GAAGlB,QAAQ,CAACc,WAAD,CAA7B;AACAX,MAAAA,WAAW,CAACU,oBAAZ,CACEE,UADF,EAEE,IAAIG,YAAJ,CAAiB,IAAIb,yBAAJ,EAAjB,CAFF;AAIAH,MAAAA,kBAAkB,CAACiB,QAAnB,CAA4BC,qBAA5B,CACEjB,WAAW,CAACkB,UAAZ,CAAuBN,UAAvB,CADF,EAEEC,MAFF;AAID,KAhBD,MAgBO;AACL,UAAI,EAAEF,WAAW,IAAIb,cAAjB,CAAJ,EAAsC;AACpC,cAAM,IAAIgB,KAAJ,CACH,iCAAgCH,WAAY,2BADzC,CAAN;AAGD,OALI,CAOL;AACA;;;AACA,YAAMI,YAAY,GAAGjB,cAAc,CAACa,WAAD,CAAnC,CATK,CAUL;;AACAV,MAAAA,iBAAiB,CAACS,oBAAlB,CAAuCE,UAAvC,EAAmD,IAAIG,YAAJ,EAAnD;AACD;;AAED,SAAKI,oBAAL,CAA0BP,UAA1B,EAAsCC,MAAtC;AACD,GA3CY;;AA4CbO,EAAAA,oBAAoB,CAClBR,UADkB,EAElB;AACAS,EAAAA,OAHkB,EAIlBC,WAJkB,EAKlBC,QALkB,EAMlB;AACA,QAAI,EAAEF,OAAO,YAAYG,OAAnB,IAA8BH,OAAO,YAAY1B,KAAK,CAAC8B,SAAzD,CAAJ,EAAyE;AACvEtB,MAAAA,iBAAiB,GAAG,IAApB;AAEA,YAAMuB,OAAO,GAAG9B,6BAA6B,KACzCI,WAAW,CAACkB,UAAZ,CAAuBN,UAAvB,CADyC,GAEzCX,iBAAiB,CAACiB,UAAlB,CAA6BN,UAA7B,CAFJ;AAIA,YAAMD,WAAW,GAAGe,OAAO,CAACC,WAAR,CAAoBC,IAAxC;AAEA,YAAM,IAAId,KAAJ,CACH,GAAEH,WAAY,aAAYC,UAAW,iDADlC,CAAN;AAGD;;AAED,QAAIhB,6BAA6B,EAAjC,EAAqC;AACnC;AACAI,MAAAA,WAAW,CAACkB,UAAZ,CAAuBN,UAAvB,EAAmCiB,IAAnC,CAAwCR,OAAxC,EAAiDE,QAAjD;AACD,KAHD,MAGO;AACL;AACAtB,MAAAA,iBAAiB,CAACiB,UAAlB,CAA6BN,UAA7B,EAAyCkB,OAAzC,CAAiDT,OAAjD,EAA0DE,QAA1D;AACD;AACF,GAxEY;;AAyEbJ,EAAAA,oBAAoB,CAACP,UAAD,EAAqBmB,SAArB,EAAwC;AAC1D,QAAInC,6BAA6B,EAAjC,EAAqC;AACnCI,MAAAA,WAAW,CAACkB,UAAZ,CAAuBN,UAAvB,EAAmCoB,mBAAnC,CAAuDD,SAAvD;AAEAhC,MAAAA,kBAAkB,CAACiB,QAAnB,CAA4BC,qBAA5B,CACEjB,WAAW,CAACkB,UAAZ,CAAuBN,UAAvB,CADF,EAEEmB,SAFF;AAID,KAPD,MAOO;AACL9B,MAAAA,iBAAiB,CAACiB,UAAlB,CAA6BN,UAA7B,EAAyCoB,mBAAzC,CAA6DD,SAA7D;AACD;AACF,GApFY;;AAqFbE,EAAAA,qBAAqB,CAACrB,UAAD,EAAqB;AACxC,QAAIhB,6BAA6B,EAAjC,EAAqC;AACnC,aAAOI,WAAW,CAACkB,UAAZ,CAAuBN,UAAvB,CAAP;AACD,KAFD,MAEO;AACL,aAAOX,iBAAiB,CAACiB,UAAlB,CAA6BN,UAA7B,CAAP;AACD;AACF,GA3FY;;AA4FbsB,EAAAA,kBAAkB,CAACtB,UAAD,EAAqB;AACrC,QAAIT,iBAAJ,EAAuB;AACrB;AACD;;AAED,QAAIP,6BAA6B,EAAjC,EAAqC;AACnCI,MAAAA,WAAW,CAACkC,kBAAZ,CAA+BtB,UAA/B;AACD,KAFD,MAEO;AACLX,MAAAA,iBAAiB,CAACiC,kBAAlB,CAAqCtB,UAArC;AACD;AACF,GAtGY;;AAuGb;AACAuB,EAAAA,eAAe,GAAG,CAAE;;AAxGP,CAAf", "sourcesContent": ["import React from 'react';\n\nimport type { ActionType } from './ActionType';\nimport { isNewWebImplementationEnabled } from './EnableNewWebImplementation';\nimport { Gestures, HammerGestures } from './web/Gestures';\nimport type { Config } from './web/interfaces';\nimport InteractionManager from './web/tools/InteractionManager';\nimport NodeManager from './web/tools/NodeManager';\nimport * as HammerNodeManager from './web_hammer/NodeManager';\nimport { GestureHandlerWebDelegate } from './web/tools/GestureHandlerWebDelegate';\n\n// init method is called inside attachGestureHandler function. However, this function may\n// fail when received view is not valid HTML element. On the other hand, dropGestureHandler\n// will be called even if attach failed, which will result in crash.\n//\n// We use this flag to check whether or not dropGestureHandler should be called.\nlet shouldPreventDrop = false;\n\nexport default {\n  handleSetJSResponder(tag: number, blockNativeResponder: boolean) {\n    console.warn('handleSetJSResponder: ', tag, blockNativeResponder);\n  },\n  handleClearJSResponder() {\n    console.warn('handleClearJSResponder: ');\n  },\n  createGestureHandler<T>(\n    handlerName: keyof typeof Gestures,\n    handlerTag: number,\n    config: T\n  ) {\n    if (isNewWebImplementationEnabled()) {\n      if (!(handlerName in Gestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      const GestureClass = Gestures[handlerName];\n      NodeManager.createGestureHandler(\n        handlerTag,\n        new GestureClass(new GestureHandlerWebDelegate())\n      );\n      InteractionManager.instance.configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        config as unknown as Config\n      );\n    } else {\n      if (!(handlerName in HammerGestures)) {\n        throw new Error(\n          `react-native-gesture-handler: ${handlerName} is not supported on web.`\n        );\n      }\n\n      // @ts-ignore If it doesn't exist, the error is thrown\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      const GestureClass = HammerGestures[handlerName];\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-call\n      HammerNodeManager.createGestureHandler(handlerTag, new GestureClass());\n    }\n\n    this.updateGestureHandler(handlerTag, config as unknown as Config);\n  },\n  attachGestureHandler(\n    handlerTag: number,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    newView: any,\n    _actionType: ActionType,\n    propsRef: React.RefObject<unknown>\n  ) {\n    if (!(newView instanceof Element || newView instanceof React.Component)) {\n      shouldPreventDrop = true;\n\n      const handler = isNewWebImplementationEnabled()\n        ? NodeManager.getHandler(handlerTag)\n        : HammerNodeManager.getHandler(handlerTag);\n\n      const handlerName = handler.constructor.name;\n\n      throw new Error(\n        `${handlerName} with tag ${handlerTag} received child that is not valid HTML element.`\n      );\n    }\n\n    if (isNewWebImplementationEnabled()) {\n      // @ts-ignore Types should be HTMLElement or React.Component\n      NodeManager.getHandler(handlerTag).init(newView, propsRef);\n    } else {\n      // @ts-ignore Types should be HTMLElement or React.Component\n      HammerNodeManager.getHandler(handlerTag).setView(newView, propsRef);\n    }\n  },\n  updateGestureHandler(handlerTag: number, newConfig: Config) {\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n\n      InteractionManager.instance.configureInteractions(\n        NodeManager.getHandler(handlerTag),\n        newConfig\n      );\n    } else {\n      HammerNodeManager.getHandler(handlerTag).updateGestureConfig(newConfig);\n    }\n  },\n  getGestureHandlerNode(handlerTag: number) {\n    if (isNewWebImplementationEnabled()) {\n      return NodeManager.getHandler(handlerTag);\n    } else {\n      return HammerNodeManager.getHandler(handlerTag);\n    }\n  },\n  dropGestureHandler(handlerTag: number) {\n    if (shouldPreventDrop) {\n      return;\n    }\n\n    if (isNewWebImplementationEnabled()) {\n      NodeManager.dropGestureHandler(handlerTag);\n    } else {\n      HammerNodeManager.dropGestureHandler(handlerTag);\n    }\n  },\n  // eslint-disable-next-line @typescript-eslint/no-empty-function\n  flushOperations() {},\n};\n"]}
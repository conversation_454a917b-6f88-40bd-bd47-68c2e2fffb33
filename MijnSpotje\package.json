{"name": "mijnspotje", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.9", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "@expo/vector-icons": "^14.1.0", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "expo-constants": "^17.1.6", "expo-location": "~18.1.5", "react-native-gesture-handler": "~2.24.0", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "@react-native-async-storage/async-storage": "2.1.2", "expo-image-picker": "~16.1.4", "react-native-maps": "1.20.1", "expo-web-browser": "~14.1.6"}, "devDependencies": {"@babel/core": "^7.20.0"}, "private": true}
{"version": 3, "names": ["_<PERSON>r<PERSON><PERSON><PERSON>", "require", "_reactNative", "_react", "AnimatedPagerView", "Animated", "createAnimatedComponent", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "usePagerView", "pagesAmount", "ref", "useRef", "pages", "setPages", "useState", "Array", "fill", "map", "_v", "index", "activePage", "setActivePage", "isAnimated", "setIsAnimated", "overdragEnabled", "setOverdragEnabled", "scrollEnabled", "setScrollEnabled", "scrollState", "setScrollState", "progress", "setProgress", "position", "offset", "onPageScrollOffset", "Value", "current", "onPageScrollPosition", "onPageSelectedPosition", "setPage", "useCallback", "page", "_ref$current", "_ref$current2", "setPageWithoutAnimation", "addPage", "prevPages", "length", "removePage", "slice", "toggleAnimation", "animated", "toggleScroll", "enabled", "toggle<PERSON>verdrag", "onPageScroll", "useMemo", "event", "nativeEvent", "listener", "useNativeDriver", "onPageSelected", "onPageScrollStateChanged", "e", "pageScrollState"], "sources": ["usePagerView.ts"], "sourcesContent": ["import type * as ReactNative from 'react-native';\nimport type {\n  OnPageScrollEventData as PagerViewOnPageScrollEventData,\n  OnPageSelectedEventData as PagerViewOnPageSelectedEventData,\n  OnPageScrollStateChangedEventData as PageScrollStateChangedNativeEventData,\n} from './PagerViewNativeComponent';\n\ntype PageScrollStateChangedNativeEvent =\n  ReactNative.NativeSyntheticEvent<PageScrollStateChangedNativeEventData>;\n\nimport { PagerView } from './PagerView';\n\nimport { Animated } from 'react-native';\nimport { useCallback, useMemo, useRef, useState } from 'react';\n\nexport type UsePagerViewProps = ReturnType<typeof usePagerView>;\n\nconst AnimatedPagerView = Animated.createAnimatedComponent(PagerView);\n\ntype UsePagerViewParams = {\n  pagesAmount: number;\n};\n\nexport function usePagerView(\n  { pagesAmount }: UsePagerViewParams = { pagesAmount: 0 }\n) {\n  const ref = useRef<PagerView>(null);\n  const [pages, setPages] = useState<number[]>(\n    new Array(pagesAmount).fill('').map((_v, index) => index)\n  );\n  const [activePage, setActivePage] = useState(0);\n  const [isAnimated, setIsAnimated] = useState(true);\n  const [overdragEnabled, setOverdragEnabled] = useState(false);\n  const [scrollEnabled, setScrollEnabled] = useState(true);\n  const [scrollState, setScrollState] = useState('idle');\n  const [progress, setProgress] = useState({ position: 0, offset: 0 });\n  const onPageScrollOffset = useRef(new Animated.Value(0)).current;\n  const onPageScrollPosition = useRef(new Animated.Value(0)).current;\n  const onPageSelectedPosition = useRef(new Animated.Value(0)).current;\n\n  const setPage = useCallback(\n    (page: number) =>\n      isAnimated\n        ? ref.current?.setPage(page)\n        : ref.current?.setPageWithoutAnimation(page),\n    [isAnimated]\n  );\n\n  const addPage = useCallback(() => {\n    setPages((prevPages) => {\n      return [...prevPages, prevPages.length];\n    });\n  }, []);\n\n  const removePage = useCallback(() => {\n    setPages((prevPages) => {\n      if (prevPages.length === 1) {\n        return prevPages;\n      }\n      return prevPages.slice(0, prevPages.length - 1);\n    });\n  }, []);\n\n  const toggleAnimation = useCallback(\n    () => setIsAnimated((animated) => !animated),\n    []\n  );\n\n  const toggleScroll = useCallback(\n    () => setScrollEnabled((enabled) => !enabled),\n    []\n  );\n\n  const toggleOverdrag = useCallback(\n    () => setOverdragEnabled((enabled) => !enabled),\n    []\n  );\n\n  const onPageScroll = useMemo(\n    () =>\n      Animated.event<PagerViewOnPageScrollEventData>(\n        [\n          {\n            nativeEvent: {\n              offset: onPageScrollOffset,\n              position: onPageScrollPosition,\n            },\n          },\n        ],\n        {\n          listener: ({ nativeEvent: { offset, position } }) => {\n            setProgress({\n              position,\n              offset,\n            });\n          },\n          useNativeDriver: true,\n        }\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    []\n  );\n\n  const onPageSelected = useMemo(\n    () =>\n      Animated.event<PagerViewOnPageSelectedEventData>(\n        [{ nativeEvent: { position: onPageSelectedPosition } }],\n        {\n          listener: ({ nativeEvent: { position } }) => {\n            setActivePage(position);\n          },\n          useNativeDriver: true,\n        }\n      ),\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    []\n  );\n\n  const onPageScrollStateChanged = useCallback(\n    (e: PageScrollStateChangedNativeEvent) => {\n      setScrollState(e.nativeEvent.pageScrollState);\n    },\n    []\n  );\n\n  return {\n    ref,\n    activePage,\n    isAnimated,\n    pages,\n    scrollState,\n    scrollEnabled,\n    progress,\n    overdragEnabled,\n    setPage,\n    addPage,\n    removePage,\n    toggleScroll,\n    toggleAnimation,\n    setProgress,\n    onPageScroll,\n    onPageSelected,\n    onPageScrollStateChanged,\n    toggleOverdrag,\n    AnimatedPagerView,\n    PagerView,\n  };\n}\n"], "mappings": ";;;;;;AAUA,IAAAA,UAAA,GAAAC,OAAA;AAEA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AAIA,MAAMG,iBAAiB,GAAGC,qBAAQ,CAACC,uBAAuB,CAACC,oBAAS,CAAC;AAM9D,SAASC,YAAYA,CAC1B;EAAEC;AAAgC,CAAC,GAAG;EAAEA,WAAW,EAAE;AAAE,CAAC,EACxD;EACA,MAAMC,GAAG,GAAG,IAAAC,aAAM,EAAY,IAAI,CAAC;EACnC,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAG,IAAAC,eAAQ,EAChC,IAAIC,KAAK,CAACN,WAAW,CAAC,CAACO,IAAI,CAAC,EAAE,CAAC,CAACC,GAAG,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAKA,KAAK,CAC1D,CAAC;EACD,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAG,IAAAP,eAAQ,EAAC,CAAC,CAAC;EAC/C,MAAM,CAACQ,UAAU,EAAEC,aAAa,CAAC,GAAG,IAAAT,eAAQ,EAAC,IAAI,CAAC;EAClD,MAAM,CAACU,eAAe,EAAEC,kBAAkB,CAAC,GAAG,IAAAX,eAAQ,EAAC,KAAK,CAAC;EAC7D,MAAM,CAACY,aAAa,EAAEC,gBAAgB,CAAC,GAAG,IAAAb,eAAQ,EAAC,IAAI,CAAC;EACxD,MAAM,CAACc,WAAW,EAAEC,cAAc,CAAC,GAAG,IAAAf,eAAQ,EAAC,MAAM,CAAC;EACtD,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAG,IAAAjB,eAAQ,EAAC;IAAEkB,QAAQ,EAAE,CAAC;IAAEC,MAAM,EAAE;EAAE,CAAC,CAAC;EACpE,MAAMC,kBAAkB,GAAG,IAAAvB,aAAM,EAAC,IAAIN,qBAAQ,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAChE,MAAMC,oBAAoB,GAAG,IAAA1B,aAAM,EAAC,IAAIN,qBAAQ,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAClE,MAAME,sBAAsB,GAAG,IAAA3B,aAAM,EAAC,IAAIN,qBAAQ,CAAC8B,KAAK,CAAC,CAAC,CAAC,CAAC,CAACC,OAAO;EAEpE,MAAMG,OAAO,GAAG,IAAAC,kBAAW,EACxBC,IAAY;IAAA,IAAAC,YAAA,EAAAC,aAAA;IAAA,OACXrB,UAAU,IAAAoB,YAAA,GACNhC,GAAG,CAAC0B,OAAO,cAAAM,YAAA,uBAAXA,YAAA,CAAaH,OAAO,CAACE,IAAI,CAAC,IAAAE,aAAA,GAC1BjC,GAAG,CAAC0B,OAAO,cAAAO,aAAA,uBAAXA,aAAA,CAAaC,uBAAuB,CAACH,IAAI,CAAC;EAAA,GAChD,CAACnB,UAAU,CACb,CAAC;EAED,MAAMuB,OAAO,GAAG,IAAAL,kBAAW,EAAC,MAAM;IAChC3B,QAAQ,CAAEiC,SAAS,IAAK;MACtB,OAAO,CAAC,GAAGA,SAAS,EAAEA,SAAS,CAACC,MAAM,CAAC;IACzC,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMC,UAAU,GAAG,IAAAR,kBAAW,EAAC,MAAM;IACnC3B,QAAQ,CAAEiC,SAAS,IAAK;MACtB,IAAIA,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAOD,SAAS;MAClB;MACA,OAAOA,SAAS,CAACG,KAAK,CAAC,CAAC,EAAEH,SAAS,CAACC,MAAM,GAAG,CAAC,CAAC;IACjD,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,eAAe,GAAG,IAAAV,kBAAW,EACjC,MAAMjB,aAAa,CAAE4B,QAAQ,IAAK,CAACA,QAAQ,CAAC,EAC5C,EACF,CAAC;EAED,MAAMC,YAAY,GAAG,IAAAZ,kBAAW,EAC9B,MAAMb,gBAAgB,CAAE0B,OAAO,IAAK,CAACA,OAAO,CAAC,EAC7C,EACF,CAAC;EAED,MAAMC,cAAc,GAAG,IAAAd,kBAAW,EAChC,MAAMf,kBAAkB,CAAE4B,OAAO,IAAK,CAACA,OAAO,CAAC,EAC/C,EACF,CAAC;EAED,MAAME,YAAY,GAAG,IAAAC,cAAO,EAC1B,MACEnD,qBAAQ,CAACoD,KAAK,CACZ,CACE;IACEC,WAAW,EAAE;MACXzB,MAAM,EAAEC,kBAAkB;MAC1BF,QAAQ,EAAEK;IACZ;EACF,CAAC,CACF,EACD;IACEsB,QAAQ,EAAEA,CAAC;MAAED,WAAW,EAAE;QAAEzB,MAAM;QAAED;MAAS;IAAE,CAAC,KAAK;MACnDD,WAAW,CAAC;QACVC,QAAQ;QACRC;MACF,CAAC,CAAC;IACJ,CAAC;IACD2B,eAAe,EAAE;EACnB,CACF,CAAC;EACH;EACA,EACF,CAAC;EAED,MAAMC,cAAc,GAAG,IAAAL,cAAO,EAC5B,MACEnD,qBAAQ,CAACoD,KAAK,CACZ,CAAC;IAAEC,WAAW,EAAE;MAAE1B,QAAQ,EAAEM;IAAuB;EAAE,CAAC,CAAC,EACvD;IACEqB,QAAQ,EAAEA,CAAC;MAAED,WAAW,EAAE;QAAE1B;MAAS;IAAE,CAAC,KAAK;MAC3CX,aAAa,CAACW,QAAQ,CAAC;IACzB,CAAC;IACD4B,eAAe,EAAE;EACnB,CACF,CAAC;EACH;EACA,EACF,CAAC;EAED,MAAME,wBAAwB,GAAG,IAAAtB,kBAAW,EACzCuB,CAAoC,IAAK;IACxClC,cAAc,CAACkC,CAAC,CAACL,WAAW,CAACM,eAAe,CAAC;EAC/C,CAAC,EACD,EACF,CAAC;EAED,OAAO;IACLtD,GAAG;IACHU,UAAU;IACVE,UAAU;IACVV,KAAK;IACLgB,WAAW;IACXF,aAAa;IACbI,QAAQ;IACRN,eAAe;IACfe,OAAO;IACPM,OAAO;IACPG,UAAU;IACVI,YAAY;IACZF,eAAe;IACfnB,WAAW;IACXwB,YAAY;IACZM,cAAc;IACdC,wBAAwB;IACxBR,cAAc;IACdlD,iBAAiB;IACjBG,SAAS,EAATA;EACF,CAAC;AACH", "ignoreList": []}
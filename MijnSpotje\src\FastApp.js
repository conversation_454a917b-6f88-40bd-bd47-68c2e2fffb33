import React, { useState, useCallback, useMemo, useEffect, memo } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  useColorScheme,
  FlatList,
  Alert,
  Modal,
  TextInput,
  Switch,
  Linking,
  InteractionManager,
} from 'react-native';
import { StatusBar } from 'expo-status-bar';
// Expo packages removed for Expo Go compatibility
// import * as Notifications from 'expo-notifications';
// import * as Location from 'expo-location';
import { I18nProvider, useTranslation } from './i18n/useTranslation';
import ProfileEditModal from './components/ProfileEditModal';
import PasswordChangeModal from './components/PasswordChangeModal';
import TermsOfServiceModal from './components/TermsOfServiceModal';
import PrivacyPolicyModal from './components/PrivacyPolicyModal';
import ContactSupportModal from './components/ContactSupportModal';
import LocationService from './services/LocationService';
import StorageService from './services/StorageService';
import CustomMapView from './components/MapView';
import ImageManager from './components/ImageManager';
import EnhancedCreateSpotModal from './components/EnhancedCreateSpotModal';

// Theme management
const THEME_MODES = {
  SYSTEM: 'system',
  LIGHT: 'light',
  DARK: 'dark',
};

const CATEGORIES = [
  'restaurant', 'café', 'park', 'museum', 'bar', 'winkel', 'sport', 'natuur', 'cultuur', 'uitgaan'
];

// No initial demo spots - users start with empty list
// All spots will be user-created through the app

// Create Spot Modal Component
const CreateSpotModal = React.memo(({ visible, onClose, onSave, isDark, t }) => {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    category: 'restaurant',
    location: '',
  });
  const [errors, setErrors] = useState({});

  const validateForm = useCallback(() => {
    const newErrors = {};
    if (!formData.title.trim()) {
      newErrors.title = t('createSpot.titleRequired');
    } else if (formData.title.length > 50) {
      newErrors.title = t('createSpot.titleMaxLength');
    }
    if (formData.description.length > 200) {
      newErrors.description = t('createSpot.descriptionMaxLength');
    }
    if (!formData.location.trim()) {
      newErrors.location = t('createSpot.locationRequired');
    }
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  }, [formData, t]);

  const handleSave = useCallback(() => {
    if (validateForm()) {
      onSave({
        ...formData,
        id: Date.now(),
        rating: 0,
        createdAt: new Date().toISOString(),
      });
      setFormData({ title: '', description: '', category: 'restaurant', location: '' });
      setErrors({});
      onClose();
    }
  }, [formData, validateForm, onSave, onClose]);

  const handleClose = useCallback(() => {
    setFormData({ title: '', description: '', category: 'restaurant', location: '' });
    setErrors({});
    onClose();
  }, [onClose]);

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={[styles.modalContainer, isDark && styles.modalContainerDark]}>
        <View style={[styles.modalHeader, isDark && styles.modalHeaderDark]}>
          <TouchableOpacity onPress={handleClose}>
            <Text style={[styles.modalButton, styles.cancelButton]}>{t('common.cancel')}</Text>
          </TouchableOpacity>
          <Text style={[styles.modalTitle, isDark && styles.textDark]}>{t('createSpot.title')}</Text>
          <TouchableOpacity onPress={handleSave}>
            <Text style={[styles.modalButton, styles.saveButton]}>{t('common.save')}</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.modalContent} showsVerticalScrollIndicator={false}>
          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Titel *</Text>
            <TextInput
              style={[styles.input, isDark && styles.inputDark, errors.title && styles.inputError]}
              value={formData.title}
              onChangeText={(text) => setFormData(prev => ({ ...prev, title: text }))}
              placeholder="Naam van de spot"
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              maxLength={50}
            />
            {errors.title && <Text style={styles.errorText}>{errors.title}</Text>}
            <Text style={[styles.charCount, isDark && styles.charCountDark]}>
              {formData.title.length}/50
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Beschrijving</Text>
            <TextInput
              style={[styles.textArea, isDark && styles.inputDark, errors.description && styles.inputError]}
              value={formData.description}
              onChangeText={(text) => setFormData(prev => ({ ...prev, description: text }))}
              placeholder="Beschrijf deze spot..."
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
              multiline
              numberOfLines={4}
              maxLength={200}
            />
            {errors.description && <Text style={styles.errorText}>{errors.description}</Text>}
            <Text style={[styles.charCount, isDark && styles.charCountDark]}>
              {formData.description.length}/200
            </Text>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Categorie</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.categoryScroll}>
              {CATEGORIES.map((category) => (
                <TouchableOpacity
                  key={category}
                  style={[
                    styles.categoryChip,
                    formData.category === category && styles.categoryChipSelected,
                    isDark && styles.categoryChipDark,
                    isDark && formData.category === category && styles.categoryChipSelectedDark,
                  ]}
                  onPress={() => setFormData(prev => ({ ...prev, category }))}
                >
                  <Text style={[
                    styles.categoryChipText,
                    formData.category === category && styles.categoryChipTextSelected,
                    isDark && styles.categoryChipTextDark,
                    isDark && formData.category === category && styles.categoryChipTextSelectedDark,
                  ]}>
                    {category}
                  </Text>
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Locatie *</Text>
            <TextInput
              style={[styles.input, isDark && styles.inputDark, errors.location && styles.inputError]}
              value={formData.location}
              onChangeText={(text) => setFormData(prev => ({ ...prev, location: text }))}
              placeholder="Adres of beschrijving van locatie"
              placeholderTextColor={isDark ? '#8e8e93' : '#666666'}
            />
            {errors.location && <Text style={styles.errorText}>{errors.location}</Text>}
          </View>

          <View style={styles.formGroup}>
            <Text style={[styles.label, isDark && styles.labelDark]}>Foto</Text>
            <TouchableOpacity style={[styles.photoPlaceholder, isDark && styles.photoPlaceholderDark]}>
              <Text style={styles.photoIcon}>📷</Text>
              <Text style={[styles.photoText, isDark && styles.photoTextDark]}>Foto toevoegen</Text>
              <Text style={[styles.comingSoon, isDark && styles.comingSoonDark]}>Binnenkort beschikbaar</Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </View>
    </Modal>
  );
});

// Optimized components for performance
const SpotCard = memo(({ spot, isDark, onPress }) => {
  const handlePress = useCallback(() => {
    onPress(spot);
  }, [spot, onPress]);

  return (
    <TouchableOpacity
      style={[styles.spotCard, isDark && styles.spotCardDark]}
      onPress={handlePress}
      activeOpacity={0.7}
    >
      <View style={styles.spotHeader}>
        <Text style={[styles.spotTitle, isDark && styles.textDark]}>{spot.title}</Text>
        <View style={styles.rating}>
          <Text style={styles.star}>⭐</Text>
          <Text style={[styles.ratingText, isDark && styles.textDark]}>{spot.rating}</Text>
        </View>
      </View>
      <Text style={[styles.category, isDark && styles.categoryDark]}>{spot.category}</Text>
      {spot.description && (
        <Text style={[styles.spotDescription, isDark && styles.textSecondaryDark]} numberOfLines={2}>
          {spot.description}
        </Text>
      )}
      <Text style={[styles.location, isDark && styles.textSecondaryDark]}>📍 {spot.location}</Text>
    </TouchableOpacity>
  );
});

SpotCard.displayName = 'SpotCard';

// Settings Screen Component
const SettingsScreen = React.memo(({
  onBack,
  isDark,
  themeMode,
  onThemeChange,
  settings,
  onSettingsChange,
  onDataUsageChange,
  onLanguageChange,
  onProfileEdit,
  onPasswordChange,
  onTermsPress,
  onPrivacyPress,
  onSupportPress,
  currentLanguage,
  availableLanguages,
  t
}) => {
  const handleLogout = useCallback(() => {
    Alert.alert(
      t('settings.logout'),
      t('settings.logoutConfirm'),
      [
        { text: t('common.cancel'), style: 'cancel' },
        { text: t('settings.logout'), style: 'destructive', onPress: () => {
          Alert.alert(t('common.success'), t('settings.logoutSuccess'));
        }}
      ]
    );
  }, [t]);

  const SettingRow = useCallback(({ title, subtitle, onPress, rightElement, showArrow = true }) => (
    <TouchableOpacity
      style={[styles.settingRow, isDark && styles.settingRowDark]}
      onPress={onPress}
      activeOpacity={0.7}
    >
      <View style={styles.settingContent}>
        <Text style={[styles.settingTitle, isDark && styles.textDark]}>{title}</Text>
        {subtitle && <Text style={[styles.settingSubtitle, isDark && styles.textSecondaryDark]}>{subtitle}</Text>}
      </View>
      <View style={styles.settingRight}>
        {rightElement}
        {showArrow && <Text style={[styles.settingArrow, isDark && styles.textSecondaryDark]}>›</Text>}
      </View>
    </TouchableOpacity>
  ), [isDark]);

  const SectionHeader = useCallback(({ title }) => (
    <Text style={[styles.sectionHeader, isDark && styles.sectionHeaderDark]}>{title}</Text>
  ), [isDark]);

  return (
    <View style={[styles.container, isDark && styles.containerDark]}>
      <View style={[styles.header, isDark && styles.headerDark]}>
        <TouchableOpacity onPress={onBack} style={styles.backButton}>
          <Text style={styles.backIcon}>←</Text>
        </TouchableOpacity>
        <Text style={[styles.headerTitle, isDark && styles.textDark]}>{t('settings.title')}</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <SectionHeader title={t('settings.sections.appearance')} />
        <View style={[styles.settingsSection, isDark && styles.settingsSectionDark]}>
          <SettingRow
            title={t('settings.appearance.theme')}
            subtitle={themeMode === THEME_MODES.SYSTEM ? t('settings.appearance.themeSystem') : themeMode === THEME_MODES.LIGHT ? t('settings.appearance.themeLight') : t('settings.appearance.themeDark')}
            onPress={() => {
              Alert.alert(
                t('alerts.themeSelect.title'),
                t('alerts.themeSelect.message'),
                [
                  { text: t('settings.appearance.themeSystem'), onPress: () => onThemeChange(THEME_MODES.SYSTEM) },
                  { text: t('settings.appearance.themeLight'), onPress: () => onThemeChange(THEME_MODES.LIGHT) },
                  { text: t('settings.appearance.themeDark'), onPress: () => onThemeChange(THEME_MODES.DARK) },
                  { text: t('common.cancel'), style: 'cancel' }
                ]
              );
            }}
          />
          <SettingRow
            title={t('settings.appearance.language')}
            subtitle={availableLanguages.find(lang => lang.code === currentLanguage)?.name || currentLanguage}
            onPress={() => {
              Alert.alert(
                t('alerts.languageSelect.title'),
                t('alerts.languageSelect.message'),
                [
                  ...availableLanguages.map(lang => ({
                    text: lang.name,
                    onPress: () => onLanguageChange(lang.code)
                  })),
                  { text: t('common.cancel'), style: 'cancel' }
                ]
              );
            }}
          />
        </View>

        <SectionHeader title={t('settings.sections.account')} />
        <View style={[styles.settingsSection, isDark && styles.settingsSectionDark]}>
          <SettingRow
            title={t('settings.account.editProfile')}
            subtitle={t('settings.account.editProfileSubtitle')}
            onPress={onProfileEdit}
          />
          <SettingRow
            title={t('settings.account.changePassword')}
            subtitle={t('settings.account.changePasswordSubtitle')}
            onPress={onPasswordChange}
          />
        </View>

        <SectionHeader title={t('settings.sections.notifications')} />
        <View style={[styles.settingsSection, isDark && styles.settingsSectionDark]}>
          <SettingRow
            title={t('settings.notifications.push')}
            subtitle={t('settings.notifications.pushSubtitle')}
            rightElement={
              <Switch
                value={settings.pushNotifications}
                onValueChange={(value) => onSettingsChange('pushNotifications', value)}
                trackColor={{ false: '#767577', true: '#007AFF' }}
                thumbColor={settings.pushNotifications ? '#ffffff' : '#f4f3f4'}
              />
            }
            showArrow={false}
          />
          <SettingRow
            title={t('settings.notifications.email')}
            subtitle={t('settings.notifications.emailSubtitle')}
            rightElement={
              <Switch
                value={settings.emailNotifications}
                onValueChange={(value) => onSettingsChange('emailNotifications', value)}
                trackColor={{ false: '#767577', true: '#007AFF' }}
                thumbColor={settings.emailNotifications ? '#ffffff' : '#f4f3f4'}
              />
            }
            showArrow={false}
          />
        </View>

        <SectionHeader title={t('settings.sections.privacy')} />
        <View style={[styles.settingsSection, isDark && styles.settingsSectionDark]}>
          <SettingRow
            title={t('settings.privacy.locationSharing')}
            subtitle={t('settings.privacy.locationSharingSubtitle')}
            rightElement={
              <Switch
                value={settings.locationSharing}
                onValueChange={(value) => onSettingsChange('locationSharing', value)}
                trackColor={{ false: '#767577', true: '#007AFF' }}
                thumbColor={settings.locationSharing ? '#ffffff' : '#f4f3f4'}
              />
            }
            showArrow={false}
          />
          <SettingRow
            title={t('settings.privacy.dataUsage')}
            subtitle={t('settings.privacy.dataUsageSubtitle')}
            onPress={() => {
              Alert.alert(
                'Data Usage Settings',
                'Configure your data preferences',
                [
                  {
                    text: 'Auto-load Images',
                    onPress: () => onDataUsageChange('autoLoadImages', !settings.dataUsage.autoLoadImages)
                  },
                  {
                    text: 'Background Sync',
                    onPress: () => onDataUsageChange('backgroundSync', !settings.dataUsage.backgroundSync)
                  },
                  {
                    text: 'High Quality Images',
                    onPress: () => onDataUsageChange('highQualityImages', !settings.dataUsage.highQualityImages)
                  },
                  { text: t('common.cancel'), style: 'cancel' }
                ]
              );
            }}
          />
        </View>

        <SectionHeader title={t('settings.sections.about')} />
        <View style={[styles.settingsSection, isDark && styles.settingsSectionDark]}>
          <SettingRow
            title={t('settings.about.version')}
            subtitle="1.0.0"
            showArrow={false}
            rightElement={null}
          />
          <SettingRow
            title={t('settings.about.terms')}
            onPress={onTermsPress}
          />
          <SettingRow
            title={t('settings.about.privacy')}
            onPress={onPrivacyPress}
          />
          <SettingRow
            title={t('settings.about.support')}
            onPress={onSupportPress}
          />
        </View>

        <View style={[styles.settingsSection, isDark && styles.settingsSectionDark, { marginTop: 20 }]}>
          <TouchableOpacity
            style={[styles.logoutButton, isDark && styles.logoutButtonDark]}
            onPress={handleLogout}
            activeOpacity={0.7}
          >
            <Text style={styles.logoutText}>{t('settings.logout')}</Text>
          </TouchableOpacity>
        </View>

        <View style={{ height: 50 }} />
      </ScrollView>
    </View>
  );
});

const TabButton = React.memo(({ title, active, onPress, icon, isDark }) => (
  <TouchableOpacity
    style={[styles.tabButton, active && styles.tabButtonActive, isDark && active && styles.tabButtonActiveDark]}
    onPress={onPress}
    activeOpacity={0.7}
  >
    <Text style={styles.tabIcon}>{icon}</Text>
    <Text style={[
      styles.tabText,
      active && styles.tabTextActive,
      isDark && styles.tabTextDark,
      isDark && active && styles.tabTextActiveDark
    ]}>
      {title}
    </Text>
  </TouchableOpacity>
));

// Main App Component (wrapped with I18nProvider)
const FastAppContent = () => {
  const { t, currentLanguage, changeLanguage, getAvailableLanguages } = useTranslation();
  const [activeTab, setActiveTab] = useState('spots');
  const [selectedSpot, setSelectedSpot] = useState(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const [showProfileEdit, setShowProfileEdit] = useState(false);
  const [showPasswordChange, setShowPasswordChange] = useState(false);
  const [showTermsOfService, setShowTermsOfService] = useState(false);
  const [showPrivacyPolicy, setShowPrivacyPolicy] = useState(false);
  const [showContactSupport, setShowContactSupport] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [spots, setSpots] = useState([]); // Start with empty spots list
  const [themeMode, setThemeMode] = useState(THEME_MODES.SYSTEM);
  const [profile, setProfile] = useState({
    name: 'Demo User',
    email: '<EMAIL>',
    bio: 'Exploring amazing spots around the world!',
  });
  const [settings, setSettings] = useState({
    pushNotifications: true,
    emailNotifications: false,
    locationSharing: true,
    dataUsage: {
      autoLoadImages: true,
      backgroundSync: false,
      highQualityImages: false,
    },
    permissions: {
      notifications: false,
      location: false,
    },
  });

  const colorScheme = useColorScheme();
  const isDark = themeMode === THEME_MODES.SYSTEM
    ? colorScheme === 'dark'
    : themeMode === THEME_MODES.DARK;

  // Optimized data loading with InteractionManager
  useEffect(() => {
    const loadAppData = async () => {
      try {
        setIsLoading(true);

        // Wait for interactions to complete for smoother startup
        await new Promise(resolve => {
          InteractionManager.runAfterInteractions(() => {
            resolve();
          });
        });

        // Clear any demo data and load only user-created spots
        const userSpots = await StorageService.clearDemoData();
        setSpots(userSpots); // Will be empty array for new users
        console.log(`📍 Loaded ${userSpots.length} user-created spots (demo data cleared)`);

        // Load other data in parallel for better performance
        const [savedSettings, savedTheme, savedProfile] = await Promise.all([
          StorageService.getUserSettings(),
          StorageService.getThemeMode(),
          StorageService.getUserProfile(),
        ]);

        setSettings(prev => ({ ...prev, ...savedSettings }));
        setThemeMode(savedTheme);
        setProfile(savedProfile);

        console.log('App data loaded from storage');
      } catch (error) {
        console.error('Error loading app data:', error);
        // Fallback to empty spots list
        setSpots([]);
      } finally {
        setIsLoading(false);
      }
    };

    loadAppData();
  }, []);

  const handleTabPress = useCallback((tab) => {
    setActiveTab(tab);
    setSelectedSpot(null);
    setShowSettings(false);
  }, []);

  const handleSpotPress = useCallback((spot) => {
    setSelectedSpot(spot);
  }, []);

  const handleBack = useCallback(() => {
    setSelectedSpot(null);
  }, []);

  const handleCreateSpot = useCallback(() => {
    setShowCreateModal(true);
  }, []);

  const handleSaveSpot = useCallback(async (newSpot) => {
    try {
      // Add location data if available
      const currentLocation = await LocationService.getCurrentLocation();
      const spotWithLocation = {
        ...newSpot,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        latitude: currentLocation?.latitude || null,
        longitude: currentLocation?.longitude || null,
      };

      // Save to storage
      const savedSpot = await StorageService.addSpot(spotWithLocation);
      if (savedSpot) {
        setSpots(prev => [savedSpot, ...prev]);
        Alert.alert('Succes!', 'Je spot is toegevoegd', [{ text: 'OK' }]);
      } else {
        Alert.alert('Fout', 'Kon spot niet opslaan', [{ text: 'OK' }]);
      }
    } catch (error) {
      console.error('Error saving spot:', error);
      Alert.alert('Fout', 'Kon spot niet opslaan', [{ text: 'OK' }]);
    }
  }, []);

  const handleThemeChange = useCallback(async (newTheme) => {
    setThemeMode(newTheme);
    await StorageService.saveThemeMode(newTheme);
    console.log('Theme changed to:', newTheme);
  }, []);

  // Functional settings handlers (Expo Go compatible)
  const handleNotificationToggle = useCallback(async (value) => {
    if (value) {
      // Simulate permission request for Expo Go compatibility
      Alert.alert(
        t('alerts.permissionRequest.title'),
        t('alerts.permissionRequest.notifications'),
        [
          {
            text: t('common.yes'),
            onPress: () => {
              setSettings(prev => ({
                ...prev,
                pushNotifications: true,
                permissions: { ...prev.permissions, notifications: true }
              }));
              Alert.alert(t('common.success'), 'Push notifications enabled');
            }
          },
          { text: t('common.cancel'), style: 'cancel' }
        ]
      );
    } else {
      setSettings(prev => ({ ...prev, pushNotifications: false }));
      Alert.alert(t('common.success'), 'Push notifications disabled');
    }
  }, [t]);

  const handleLocationToggle = useCallback(async (value) => {
    if (value) {
      // Simulate permission request for Expo Go compatibility
      Alert.alert(
        t('alerts.permissionRequest.title'),
        t('alerts.permissionRequest.location'),
        [
          {
            text: t('common.yes'),
            onPress: () => {
              setSettings(prev => ({
                ...prev,
                locationSharing: true,
                permissions: { ...prev.permissions, location: true }
              }));
              Alert.alert(t('common.success'), 'Location sharing enabled');
            }
          },
          { text: t('common.cancel'), style: 'cancel' }
        ]
      );
    } else {
      setSettings(prev => ({ ...prev, locationSharing: false }));
      Alert.alert(t('common.success'), 'Location sharing disabled');
    }
  }, [t]);

  const handleSettingsChange = useCallback(async (key, value) => {
    if (key === 'pushNotifications') {
      handleNotificationToggle(value);
    } else if (key === 'locationSharing') {
      handleLocationToggle(value);
    } else {
      const newSettings = { ...settings, [key]: value };
      setSettings(newSettings);
      await StorageService.saveUserSettings(newSettings);
      console.log('Setting changed:', key, value);
    }
  }, [settings, handleNotificationToggle, handleLocationToggle]);

  const handleDataUsageChange = useCallback((key, value) => {
    setSettings(prev => ({
      ...prev,
      dataUsage: { ...prev.dataUsage, [key]: value }
    }));
    console.log('Data usage setting changed:', key, value);
  }, []);

  const handleLanguageChange = useCallback((newLanguage) => {
    changeLanguage(newLanguage);
    Alert.alert(t('common.success'), `Language changed to ${newLanguage}`);
  }, [changeLanguage, t]);

  const handleShowSettings = useCallback(() => {
    setShowSettings(true);
  }, []);

  const handleBackFromSettings = useCallback(() => {
    setShowSettings(false);
  }, []);

  const tabs = useMemo(() => [
    { id: 'spots', title: t('navigation.spots'), icon: '📍' },
    { id: 'map', title: t('navigation.map'), icon: '🗺️' },
    { id: 'groups', title: t('navigation.groups'), icon: '👥' },
    { id: 'profile', title: t('navigation.profile'), icon: '👤' },
  ], [t]);

  // Optimized FlatList rendering
  const renderSpot = useCallback(({ item }) => (
    <SpotCard spot={item} isDark={isDark} onPress={handleSpotPress} />
  ), [isDark, handleSpotPress]);

  const keyExtractor = useCallback((item) => item.id.toString(), []);

  const getItemLayout = useCallback((data, index) => ({
    length: 120, // Approximate height of SpotCard
    offset: 120 * index,
    index,
  }), []);

  // Show loading screen while data is loading
  if (isLoading) {
    return (
      <View style={[styles.container, isDark && styles.containerDark, styles.loadingContainer]}>
        <StatusBar style={isDark ? "light" : "dark"} />
        <Text style={[styles.loadingIcon, isDark && styles.textDark]}>🗺️</Text>
        <Text style={[styles.loadingText, isDark && styles.textDark]}>Mijn Spotje</Text>
        <Text style={[styles.loadingSubtext, isDark && styles.textSecondaryDark]}>
          Laden van je spots...
        </Text>
        <View style={styles.loadingDots}>
          <View style={[styles.dot, isDark && styles.dotDark]} />
          <View style={[styles.dot, isDark && styles.dotDark]} />
          <View style={[styles.dot, isDark && styles.dotDark]} />
        </View>
      </View>
    );
  }

  // Show settings screen
  if (showSettings) {
    return (
      <SettingsScreen
        onBack={handleBackFromSettings}
        isDark={isDark}
        themeMode={themeMode}
        onThemeChange={handleThemeChange}
        settings={settings}
        onSettingsChange={handleSettingsChange}
        onDataUsageChange={handleDataUsageChange}
        onLanguageChange={handleLanguageChange}
        onProfileEdit={() => setShowProfileEdit(true)}
        onPasswordChange={() => setShowPasswordChange(true)}
        onTermsPress={() => setShowTermsOfService(true)}
        onPrivacyPress={() => setShowPrivacyPolicy(true)}
        onSupportPress={() => setShowContactSupport(true)}
        currentLanguage={currentLanguage}
        availableLanguages={getAvailableLanguages()}
        t={t}
      />
    );
  }

  if (selectedSpot) {
    return (
      <View style={[styles.container, isDark && styles.containerDark]}>
        <StatusBar style={isDark ? "light" : "dark"} />
        <View style={[styles.header, isDark && styles.headerDark]}>
          <TouchableOpacity onPress={handleBack} style={styles.backButton}>
            <Text style={styles.backIcon}>←</Text>
          </TouchableOpacity>
          <Text style={[styles.headerTitle, isDark && styles.textDark]}>Spot Details</Text>
        </View>
        
        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={[styles.detailCard, isDark && styles.detailCardDark]}>
            <Text style={[styles.detailTitle, isDark && styles.textDark]}>{selectedSpot.title}</Text>
            <View style={styles.detailRating}>
              <Text style={styles.star}>⭐</Text>
              <Text style={[styles.ratingText, isDark && styles.textDark]}>{selectedSpot.rating}</Text>
              <Text style={[styles.ratingCount, isDark && styles.textSecondaryDark]}>(23 reviews)</Text>
            </View>
            <Text style={[styles.detailCategory, isDark && styles.categoryDark]}>{selectedSpot.category}</Text>
            <Text style={[styles.description, isDark && styles.textSecondaryDark]}>
              Een geweldige plek om te bezoeken in Amsterdam. Perfect voor een dagje uit met vrienden of familie.
            </Text>
            
            <View style={styles.actions}>
              <TouchableOpacity style={[styles.actionButton, styles.favoriteButton]}>
                <Text style={styles.actionText}>❤️ Favoriet</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.actionButton, styles.shareButton]}>
                <Text style={styles.actionText}>📤 Delen</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'spots':
        return (
          <>
            <FlatList
              data={spots}
              renderItem={renderSpot}
              keyExtractor={keyExtractor}
              getItemLayout={getItemLayout}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.listContent}
              removeClippedSubviews={true}
              maxToRenderPerBatch={10}
              updateCellsBatchingPeriod={50}
              initialNumToRender={10}
              windowSize={10}
              legacyImplementation={false}
            />
            <TouchableOpacity
              style={[styles.fab, isDark && styles.fabDark]}
              onPress={handleCreateSpot}
              activeOpacity={0.8}
            >
              <Text style={styles.fabIcon}>+</Text>
            </TouchableOpacity>
          </>
        );
      case 'map':
        return (
          <>
            <CustomMapView
              spots={spots}
              isDark={isDark}
              onSpotPress={handleSpotPress}
              onCreateSpot={(coordinate) => {
                // Pre-fill location when creating spot from map
                setShowCreateModal(true);
              }}
              t={t}
            />
            <TouchableOpacity
              style={[styles.fab, isDark && styles.fabDark]}
              onPress={handleCreateSpot}
              activeOpacity={0.8}
            >
              <Text style={styles.fabIcon}>+</Text>
            </TouchableOpacity>
          </>
        );
      case 'groups':
        return (
          <View style={styles.placeholder}>
            <Text style={styles.placeholderIcon}>👥</Text>
            <Text style={[styles.placeholderText, isDark && styles.textDark]}>Groepen</Text>
            <Text style={[styles.placeholderSubtext, isDark && styles.textSecondaryDark]}>
              Deel spots met vrienden
            </Text>
          </View>
        );
      case 'profile':
        return (
          <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
            <View style={[styles.profileHeader, isDark && styles.profileHeaderDark]}>
              <View style={[styles.avatar, isDark && styles.avatarDark]}>
                <Text style={styles.avatarText}>👤</Text>
              </View>
              <Text style={[styles.profileName, isDark && styles.textDark]}>Demo Gebruiker</Text>
              <Text style={[styles.profileEmail, isDark && styles.textSecondaryDark]}><EMAIL></Text>
            </View>

            <View style={[styles.statsContainer, isDark && styles.statsContainerDark]}>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, isDark && styles.textDark]}>{spots.length}</Text>
                <Text style={[styles.statLabel, isDark && styles.textSecondaryDark]}>Spots</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, isDark && styles.textDark]}>12</Text>
                <Text style={[styles.statLabel, isDark && styles.textSecondaryDark]}>Reviews</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={[styles.statNumber, isDark && styles.textDark]}>3</Text>
                <Text style={[styles.statLabel, isDark && styles.textSecondaryDark]}>Groepen</Text>
              </View>
            </View>

            <TouchableOpacity
              style={[styles.settingsButton, isDark && styles.settingsButtonDark]}
              onPress={handleShowSettings}
              activeOpacity={0.7}
            >
              <Text style={styles.settingsIcon}>⚙️</Text>
              <Text style={[styles.settingsText, isDark && styles.textDark]}>Instellingen</Text>
              <Text style={[styles.settingsArrow, isDark && styles.textSecondaryDark]}>›</Text>
            </TouchableOpacity>

            <View style={styles.profileActions}>
              <TouchableOpacity style={[styles.profileActionButton, isDark && styles.profileActionButtonDark]}>
                <Text style={styles.profileActionIcon}>❤️</Text>
                <Text style={[styles.profileActionText, isDark && styles.textDark]}>Favorieten</Text>
              </TouchableOpacity>
              <TouchableOpacity style={[styles.profileActionButton, isDark && styles.profileActionButtonDark]}>
                <Text style={styles.profileActionIcon}>🏆</Text>
                <Text style={[styles.profileActionText, isDark && styles.textDark]}>Prestaties</Text>
              </TouchableOpacity>
            </View>
          </ScrollView>
        );
      default:
        return null;
    }
  };

  return (
    <View style={[styles.container, isDark && styles.containerDark]}>
      <StatusBar style={isDark ? "light" : "dark"} />

      <View style={[styles.header, isDark && styles.headerDark]}>
        <Text style={[styles.headerTitle, isDark && styles.textDark]}>🗺️ Mijn Spotje</Text>
        <TouchableOpacity
          style={styles.themeToggle}
          onPress={() => {
            const nextTheme = themeMode === THEME_MODES.SYSTEM
              ? THEME_MODES.LIGHT
              : themeMode === THEME_MODES.LIGHT
                ? THEME_MODES.DARK
                : THEME_MODES.SYSTEM;
            handleThemeChange(nextTheme);
          }}
        >
          <Text style={styles.themeIcon}>
            {themeMode === THEME_MODES.SYSTEM ? '🌓' : themeMode === THEME_MODES.LIGHT ? '☀️' : '🌙'}
          </Text>
        </TouchableOpacity>
      </View>

      <View style={styles.content}>
        {renderContent()}
      </View>

      <View style={[styles.tabBar, isDark && styles.tabBarDark]}>
        {tabs.map((tab) => (
          <TabButton
            key={tab.id}
            title={tab.title}
            icon={tab.icon}
            active={activeTab === tab.id}
            onPress={() => handleTabPress(tab.id)}
            isDark={isDark}
          />
        ))}
      </View>

      <EnhancedCreateSpotModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        onSave={handleSaveSpot}
        isDark={isDark}
        t={t}
      />

      <ProfileEditModal
        visible={showProfileEdit}
        onClose={() => setShowProfileEdit(false)}
        onSave={setProfile}
        isDark={isDark}
        currentProfile={profile}
      />

      <PasswordChangeModal
        visible={showPasswordChange}
        onClose={() => setShowPasswordChange(false)}
        isDark={isDark}
      />

      <TermsOfServiceModal
        visible={showTermsOfService}
        onClose={() => setShowTermsOfService(false)}
        onAccept={() => {
          console.log('Terms of service accepted');
          // In a real app, this would save the acceptance
        }}
        isDark={isDark}
      />

      <PrivacyPolicyModal
        visible={showPrivacyPolicy}
        onClose={() => setShowPrivacyPolicy(false)}
        isDark={isDark}
      />

      <ContactSupportModal
        visible={showContactSupport}
        onClose={() => setShowContactSupport(false)}
        isDark={isDark}
      />
    </View>
  );
};

// Main FastApp component (I18nProvider now in App.js)
export default function FastApp() {
  return <FastAppContent />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  containerDark: {
    backgroundColor: '#000000',
  },
  loadingContainer: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingIcon: {
    fontSize: 60,
    marginBottom: 16,
  },
  loadingText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  loadingSubtext: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 32,
  },
  loadingDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    gap: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#007AFF',
    opacity: 0.7,
  },
  dotDark: {
    backgroundColor: '#ffffff',
  },
  header: {
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    flex: 1,
    textAlign: 'center',
  },
  themeToggle: {
    padding: 8,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.05)',
  },
  themeIcon: {
    fontSize: 20,
  },
  backButton: {
    position: 'absolute',
    left: 20,
    top: 50,
    padding: 8,
  },
  backIcon: {
    fontSize: 24,
    color: '#007AFF',
  },
  content: {
    flex: 1,
  },
  listContent: {
    padding: 16,
  },
  spotCard: {
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
    marginBottom: 12,
  },
  spotCardDark: {
    backgroundColor: '#1c1c1e',
  },
  spotHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  spotTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    flex: 1,
  },
  rating: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  star: {
    fontSize: 14,
    marginRight: 4,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },
  category: {
    fontSize: 12,
    color: '#666666',
    textTransform: 'uppercase',
    fontWeight: '500',
  },
  categoryDark: {
    color: '#8e8e93',
  },
  textDark: {
    color: '#ffffff',
  },
  textSecondaryDark: {
    color: '#8e8e93',
  },
  tabBar: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    paddingBottom: 20,
  },
  tabBarDark: {
    backgroundColor: '#1c1c1e',
    borderTopColor: '#38383a',
  },
  tabButton: {
    flex: 1,
    alignItems: 'center',
    paddingVertical: 8,
  },
  tabButtonActive: {
    backgroundColor: '#f0f8ff',
  },
  tabButtonActiveDark: {
    backgroundColor: '#2c2c2e',
  },
  tabIcon: {
    fontSize: 20,
    marginBottom: 4,
  },
  tabText: {
    fontSize: 10,
    color: '#666666',
    fontWeight: '500',
  },
  tabTextActive: {
    color: '#007AFF',
  },
  tabTextDark: {
    color: '#8e8e93',
  },
  tabTextActiveDark: {
    color: '#007AFF',
  },
  placeholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  placeholderIcon: {
    fontSize: 60,
    marginBottom: 16,
  },
  placeholderText: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 8,
  },
  placeholderSubtext: {
    fontSize: 14,
    color: '#666666',
    textAlign: 'center',
  },
  detailCard: {
    backgroundColor: '#ffffff',
    margin: 16,
    borderRadius: 16,
    padding: 20,
  },
  detailCardDark: {
    backgroundColor: '#1c1c1e',
  },
  detailTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 12,
  },
  detailRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  ratingCount: {
    fontSize: 14,
    color: '#666666',
    marginLeft: 8,
  },
  detailCategory: {
    fontSize: 14,
    color: '#666666',
    textTransform: 'uppercase',
    fontWeight: '600',
    marginBottom: 16,
  },
  description: {
    fontSize: 16,
    color: '#666666',
    lineHeight: 24,
    marginBottom: 24,
  },
  actions: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  favoriteButton: {
    backgroundColor: '#ff3b30',
  },
  shareButton: {
    backgroundColor: '#007AFF',
  },
  actionText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },

  // Enhanced spot card styles
  spotDescription: {
    fontSize: 12,
    color: '#666666',
    marginTop: 4,
    lineHeight: 16,
  },
  location: {
    fontSize: 11,
    color: '#999999',
    marginTop: 4,
  },

  // FAB styles
  fab: {
    position: 'absolute',
    bottom: 90,
    right: 20,
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: '#007AFF',
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  fabDark: {
    backgroundColor: '#0A84FF',
  },
  fabIcon: {
    fontSize: 24,
    color: '#ffffff',
    fontWeight: 'bold',
  },

  // Map spots list styles
  mapSpotsList: {
    marginTop: 20,
    width: '100%',
    paddingHorizontal: 20,
  },
  mapSpotsTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
    color: '#000000',
  },
  mapSpotItem: {
    backgroundColor: '#f8f9fa',
    padding: 12,
    borderRadius: 8,
    marginBottom: 8,
  },
  mapSpotItemDark: {
    backgroundColor: '#1c1c1e',
  },
  mapSpotTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#000000',
  },
  mapSpotCategory: {
    fontSize: 12,
    color: '#666666',
    marginTop: 2,
    textTransform: 'capitalize',
  },

  // Profile styles
  profileHeader: {
    alignItems: 'center',
    padding: 20,
    backgroundColor: '#ffffff',
    marginBottom: 20,
  },
  profileHeaderDark: {
    backgroundColor: '#1c1c1e',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  avatarDark: {
    backgroundColor: '#2c2c2e',
  },
  avatarText: {
    fontSize: 32,
  },
  profileName: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  profileEmail: {
    fontSize: 14,
    color: '#666666',
  },
  statsContainer: {
    flexDirection: 'row',
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    borderRadius: 12,
    padding: 20,
    marginBottom: 20,
  },
  statsContainerDark: {
    backgroundColor: '#1c1c1e',
  },
  statItem: {
    flex: 1,
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#000000',
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: '#666666',
    textTransform: 'uppercase',
  },
  settingsButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ffffff',
    marginHorizontal: 20,
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
  },
  settingsButtonDark: {
    backgroundColor: '#1c1c1e',
  },
  settingsIcon: {
    fontSize: 20,
    marginRight: 12,
  },
  settingsText: {
    flex: 1,
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
  },
  settingsArrow: {
    fontSize: 16,
    color: '#666666',
  },
  profileActions: {
    flexDirection: 'row',
    marginHorizontal: 20,
    gap: 12,
  },
  profileActionButton: {
    flex: 1,
    backgroundColor: '#ffffff',
    padding: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
  profileActionButtonDark: {
    backgroundColor: '#1c1c1e',
  },
  profileActionIcon: {
    fontSize: 24,
    marginBottom: 8,
  },
  profileActionText: {
    fontSize: 14,
    fontWeight: '500',
    color: '#000000',
  },

  // Create Spot Modal styles
  modalContainer: {
    flex: 1,
    backgroundColor: '#ffffff',
  },
  modalContainerDark: {
    backgroundColor: '#000000',
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: 50,
    paddingBottom: 16,
    paddingHorizontal: 20,
    backgroundColor: '#ffffff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  modalHeaderDark: {
    backgroundColor: '#1c1c1e',
    borderBottomColor: '#38383a',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000000',
  },
  modalButton: {
    fontSize: 16,
    fontWeight: '600',
  },
  cancelButton: {
    color: '#ff3b30',
  },
  saveButton: {
    color: '#007AFF',
  },
  modalContent: {
    flex: 1,
    padding: 20,
  },
  formGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#000000',
    marginBottom: 8,
  },
  labelDark: {
    color: '#ffffff',
  },
  input: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
  },
  inputDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
    color: '#ffffff',
  },
  inputError: {
    borderColor: '#ff3b30',
  },
  textArea: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    padding: 12,
    fontSize: 16,
    backgroundColor: '#ffffff',
    color: '#000000',
    height: 80,
    textAlignVertical: 'top',
  },
  errorText: {
    color: '#ff3b30',
    fontSize: 12,
    marginTop: 4,
  },
  charCount: {
    fontSize: 12,
    color: '#666666',
    textAlign: 'right',
    marginTop: 4,
  },
  charCountDark: {
    color: '#8e8e93',
  },
  categoryScroll: {
    marginTop: 8,
  },
  categoryChip: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  categoryChipDark: {
    backgroundColor: '#2c2c2e',
  },
  categoryChipSelected: {
    backgroundColor: '#007AFF',
  },
  categoryChipSelectedDark: {
    backgroundColor: '#0A84FF',
  },
  categoryChipText: {
    fontSize: 14,
    color: '#000000',
  },
  categoryChipTextDark: {
    color: '#ffffff',
  },
  categoryChipTextSelected: {
    color: '#ffffff',
  },
  categoryChipTextSelectedDark: {
    color: '#ffffff',
  },
  photoPlaceholder: {
    backgroundColor: '#f8f9fa',
    borderRadius: 8,
    padding: 20,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderStyle: 'dashed',
  },
  photoPlaceholderDark: {
    backgroundColor: '#1c1c1e',
    borderColor: '#38383a',
  },
  photoIcon: {
    fontSize: 32,
    marginBottom: 8,
  },
  photoText: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 4,
  },
  photoTextDark: {
    color: '#ffffff',
  },
  comingSoon: {
    fontSize: 12,
    color: '#666666',
    fontStyle: 'italic',
  },
  comingSoonDark: {
    color: '#8e8e93',
  },

  // Settings Screen styles
  settingsSection: {
    backgroundColor: '#ffffff',
    borderRadius: 12,
    marginHorizontal: 20,
    marginBottom: 20,
    overflow: 'hidden',
  },
  settingsSectionDark: {
    backgroundColor: '#1c1c1e',
  },
  sectionHeader: {
    fontSize: 13,
    fontWeight: '600',
    color: '#666666',
    textTransform: 'uppercase',
    marginLeft: 20,
    marginTop: 20,
    marginBottom: 8,
  },
  sectionHeaderDark: {
    color: '#8e8e93',
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingRowDark: {
    borderBottomColor: '#2c2c2e',
  },
  settingContent: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '500',
    color: '#000000',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666666',
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingArrow: {
    fontSize: 18,
    color: '#666666',
    marginLeft: 8,
  },
  logoutButton: {
    backgroundColor: '#ff3b30',
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
  },
  logoutButtonDark: {
    backgroundColor: '#ff453a',
  },
  logoutText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
});

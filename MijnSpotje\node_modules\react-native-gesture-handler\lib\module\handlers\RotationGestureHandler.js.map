{"version": 3, "sources": ["RotationGestureHandler.ts"], "names": ["createHandler", "baseGestureHandlerProps", "rotationHandlerName", "RotationGestureHandler", "name", "allowedProps", "config"], "mappings": "AACA,OAAOA,aAAP,MAA0B,iBAA1B;AACA,SAEEC,uBAFF,QAGO,wBAHP;AAKA;AACA;AACA;;AAIA,OAAO,MAAMC,mBAAmB,GAAG,wBAA5B;AAEP;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,sBAAsB,GAAGH,aAAa,CAGjD;AACAI,EAAAA,IAAI,EAAEF,mBADN;AAEAG,EAAAA,YAAY,EAAEJ,uBAFd;AAGAK,EAAAA,MAAM,EAAE;AAHR,CAHiD,CAA5C", "sourcesContent": ["import { RotationGestureHandlerEventPayload } from './GestureHandlerEventPayload';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\n/**\n * @deprecated RotationGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Rotation()` instead.\n */\nexport interface RotationGestureHandlerProps\n  extends BaseGestureHandlerProps<RotationGestureHandlerEventPayload> {}\n\nexport const rotationHandlerName = 'RotationGestureHandler';\n\n/**\n * @deprecated RotationGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Rotation()` instead.\n */\nexport type RotationGestureHandler = typeof RotationGestureHandler;\n\n/**\n * @deprecated RotationGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Rotation()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const RotationGestureHandler = createHandler<\n  RotationGestureHandlerProps,\n  RotationGestureHandlerEventPayload\n>({\n  name: rotationHandlerName,\n  allowedProps: baseGestureHandlerProps,\n  config: {},\n});\n"]}
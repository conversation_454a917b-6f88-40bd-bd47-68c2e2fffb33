{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_color", "_interopRequireDefault", "_Pressable", "_utils", "_settings", "_theming", "_forwardRef", "_hasTouchHandler", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "TouchableRipple", "style", "background", "_background", "borderless", "disabled", "disabledProp", "rippleColor", "underlayColor", "_underlayColor", "children", "theme", "themeOverrides", "rest", "ref", "useInternalTheme", "calculatedRippleColor", "getTouchableRippleColors", "hoverColor", "color", "fade", "rgb", "string", "rippleEffectEnabled", "useContext", "SettingsContext", "onPress", "onLongPress", "onPressIn", "onPressOut", "handlePressIn", "useCallback", "centered", "button", "currentTarget", "window", "getComputedStyle", "dimensions", "getBoundingClientRect", "touchX", "touchY", "changedTouches", "touches", "nativeEvent", "touch", "width", "height", "locationX", "pageX", "locationY", "pageY", "size", "Math", "min", "max", "container", "document", "createElement", "setAttribute", "position", "pointerEvents", "top", "left", "right", "bottom", "borderTopLeftRadius", "borderTopRightRadius", "borderBottomRightRadius", "borderBottomLeftRadius", "overflow", "ripple", "backgroundColor", "borderRadius", "transitionProperty", "transitionDuration", "transitionTimingFunction", "transform<PERSON><PERSON>in", "transform", "opacity", "append<PERSON><PERSON><PERSON>", "requestAnimationFrame", "handlePressOut", "containers", "querySelectorAll", "for<PERSON>ach", "<PERSON><PERSON><PERSON><PERSON>", "setTimeout", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "Pressable", "state", "styles", "touchable", "hovered", "Children", "only", "supported", "StyleSheet", "create", "Platform", "OS", "cursor", "transition", "Component", "forwardRef", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/TouchableRipple/TouchableRipple.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAUA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAGA,IAAAI,UAAA,GAAAJ,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,SAAA,GAAAN,OAAA;AACA,IAAAO,QAAA,GAAAP,OAAA;AAEA,IAAAQ,WAAA,GAAAR,OAAA;AACA,IAAAS,gBAAA,GAAAN,sBAAA,CAAAH,OAAA;AAA0D,SAAAG,uBAAAO,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAX,wBAAAW,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAf,uBAAA,YAAAA,CAAAW,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AA4D1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,eAAe,GAAGA,CACtB;EACEC,KAAK;EACLC,UAAU,EAAEC,WAAW;EACvBC,UAAU,GAAG,KAAK;EAClBC,QAAQ,EAAEC,YAAY;EACtBC,WAAW;EACXC,aAAa,EAAEC,cAAc;EAC7BC,QAAQ;EACRC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,EACRC,GAA6B,KAC1B;EACH,MAAMH,KAAK,GAAG,IAAAI,yBAAgB,EAACH,cAAc,CAAC;EAC9C,MAAM;IAAEI;EAAsB,CAAC,GAAG,IAAAC,+BAAwB,EAAC;IACzDN,KAAK;IACLJ;EACF,CAAC,CAAC;EACF,MAAMW,UAAU,GAAG,IAAAC,cAAK,EAACH,qBAAqB,CAAC,CAACI,IAAI,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACxE,MAAM;IAAEC;EAAoB,CAAC,GAAG5D,KAAK,CAAC6D,UAAU,CAAWC,yBAAe,CAAC;EAE3E,MAAM;IAAEC,OAAO;IAAEC,WAAW;IAAEC,SAAS;IAAEC;EAAW,CAAC,GAAGhB,IAAI;EAE5D,MAAMiB,aAAa,GAAGnE,KAAK,CAACoE,WAAW,CACpCxD,CAAM,IAAK;IACVqD,SAAS,aAATA,SAAS,eAATA,SAAS,CAAGrD,CAAC,CAAC;IAEd,IAAIgD,mBAAmB,EAAE;MACvB,MAAM;QAAES;MAAS,CAAC,GAAGnB,IAAI;MAEzB,MAAMoB,MAAM,GAAG1D,CAAC,CAAC2D,aAAa;MAC9B,MAAMjC,KAAK,GAAGkC,MAAM,CAACC,gBAAgB,CAACH,MAAM,CAAC;MAC7C,MAAMI,UAAU,GAAGJ,MAAM,CAACK,qBAAqB,CAAC,CAAC;MAEjD,IAAIC,MAAM;MACV,IAAIC,MAAM;MAEV,MAAM;QAAEC,cAAc;QAAEC;MAAQ,CAAC,GAAGnE,CAAC,CAACoE,WAAW;MACjD,MAAMC,KAAK,GAAG,CAAAF,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAG,CAAC,CAAC,MAAID,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAG,CAAC,CAAC;;MAEjD;MACA,IAAIT,QAAQ,IAAI,CAACY,KAAK,EAAE;QACtBL,MAAM,GAAGF,UAAU,CAACQ,KAAK,GAAG,CAAC;QAC7BL,MAAM,GAAGH,UAAU,CAACS,MAAM,GAAG,CAAC;MAChC,CAAC,MAAM;QACLP,MAAM,GAAGK,KAAK,CAACG,SAAS,IAAIxE,CAAC,CAACyE,KAAK;QACnCR,MAAM,GAAGI,KAAK,CAACK,SAAS,IAAI1E,CAAC,CAAC2E,KAAK;MACrC;;MAEA;MACA,MAAMC,IAAI,GAAGnB,QAAQ;MACjB;MACAoB,IAAI,CAACC,GAAG,CAAChB,UAAU,CAACQ,KAAK,EAAER,UAAU,CAACS,MAAM,CAAC,GAAG,GAAG;MACnD;MACAM,IAAI,CAACE,GAAG,CAACjB,UAAU,CAACQ,KAAK,EAAER,UAAU,CAACS,MAAM,CAAC,GAAG,CAAC;;MAErD;MACA,MAAMS,SAAS,GAAGC,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAEhDF,SAAS,CAACG,YAAY,CAAC,mBAAmB,EAAE,EAAE,CAAC;MAE/CnE,MAAM,CAACI,MAAM,CAAC4D,SAAS,CAACtD,KAAK,EAAE;QAC7B0D,QAAQ,EAAE,UAAU;QACpBC,aAAa,EAAE,MAAM;QACrBC,GAAG,EAAE,GAAG;QACRC,IAAI,EAAE,GAAG;QACTC,KAAK,EAAE,GAAG;QACVC,MAAM,EAAE,GAAG;QACXC,mBAAmB,EAAEhE,KAAK,CAACgE,mBAAmB;QAC9CC,oBAAoB,EAAEjE,KAAK,CAACiE,oBAAoB;QAChDC,uBAAuB,EAAElE,KAAK,CAACkE,uBAAuB;QACtDC,sBAAsB,EAAEnE,KAAK,CAACmE,sBAAsB;QACpDC,QAAQ,EAAErC,QAAQ,GAAG,SAAS,GAAG;MACnC,CAAC,CAAC;;MAEF;MACA,MAAMsC,MAAM,GAAGd,QAAQ,CAACC,aAAa,CAAC,MAAM,CAAC;MAE7ClE,MAAM,CAACI,MAAM,CAAC2E,MAAM,CAACrE,KAAK,EAAE;QAC1B0D,QAAQ,EAAE,UAAU;QACpBC,aAAa,EAAE,MAAM;QACrBW,eAAe,EAAEvD,qBAAqB;QACtCwD,YAAY,EAAE,KAAK;QAEnB;QACAC,kBAAkB,EAAE,mBAAmB;QACvCC,kBAAkB,EAAE,GAAGtB,IAAI,CAACC,GAAG,CAACF,IAAI,GAAG,GAAG,EAAE,GAAG,CAAC,IAAI;QACpDwB,wBAAwB,EAAE,QAAQ;QAClCC,eAAe,EAAE,QAAQ;QAEzB;QACAC,SAAS,EAAE,mDAAmD;QAC9DC,OAAO,EAAE,KAAK;QAEd;QACAhB,IAAI,EAAE,GAAGvB,MAAM,IAAI;QACnBsB,GAAG,EAAE,GAAGrB,MAAM,IAAI;QAClBK,KAAK,EAAE,GAAGM,IAAI,IAAI;QAClBL,MAAM,EAAE,GAAGK,IAAI;MACjB,CAAC,CAAC;;MAEF;MACAI,SAAS,CAACwB,WAAW,CAACT,MAAM,CAAC;MAC7BrC,MAAM,CAAC8C,WAAW,CAACxB,SAAS,CAAC;;MAE7B;MACA;MACA;MACAyB,qBAAqB,CAAC,MAAM;QAC1BA,qBAAqB,CAAC,MAAM;UAC1BzF,MAAM,CAACI,MAAM,CAAC2E,MAAM,CAACrE,KAAK,EAAE;YAC1B4E,SAAS,EAAE,6CAA6C;YACxDC,OAAO,EAAE;UACX,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAAClD,SAAS,EAAEf,IAAI,EAAEU,mBAAmB,EAAEP,qBAAqB,CAC9D,CAAC;EAED,MAAMiE,cAAc,GAAGtH,KAAK,CAACoE,WAAW,CACrCxD,CAAM,IAAK;IACVsD,UAAU,aAAVA,UAAU,eAAVA,UAAU,CAAGtD,CAAC,CAAC;IAEf,IAAIgD,mBAAmB,EAAE;MACvB,MAAM2D,UAAU,GAAG3G,CAAC,CAAC2D,aAAa,CAACiD,gBAAgB,CACjD,qBACF,CAAkB;MAElBH,qBAAqB,CAAC,MAAM;QAC1BA,qBAAqB,CAAC,MAAM;UAC1BE,UAAU,CAACE,OAAO,CAAE7B,SAAS,IAAK;YAChC,MAAMe,MAAM,GAAGf,SAAS,CAAC8B,UAA6B;YAEtD9F,MAAM,CAACI,MAAM,CAAC2E,MAAM,CAACrE,KAAK,EAAE;cAC1ByE,kBAAkB,EAAE,OAAO;cAC3BI,OAAO,EAAE;YACX,CAAC,CAAC;;YAEF;YACAQ,UAAU,CAAC,MAAM;cACf,MAAM;gBAAEC;cAAW,CAAC,GAAGhC,SAAS;cAEhC,IAAIgC,UAAU,EAAE;gBACdA,UAAU,CAACC,WAAW,CAACjC,SAAS,CAAC;cACnC;YACF,CAAC,EAAE,GAAG,CAAC;UACT,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAAC1B,UAAU,EAAEN,mBAAmB,CAClC,CAAC;EAED,MAAMkE,qBAAqB,GAAG,IAAAC,wBAAe,EAAC;IAC5ChE,OAAO;IACPC,WAAW;IACXC,SAAS;IACTC;EACF,CAAC,CAAC;EAEF,MAAMxB,QAAQ,GAAGC,YAAY,IAAI,CAACmF,qBAAqB;EAEvD,oBACE9H,KAAA,CAAA8F,aAAA,CAACxF,UAAA,CAAA0H,SAAS,EAAAjG,QAAA,KACJmB,IAAI;IACRC,GAAG,EAAEA,GAAI;IACTc,SAAS,EAAEE,aAAc;IACzBD,UAAU,EAAEoD,cAAe;IAC3B5E,QAAQ,EAAEA,QAAS;IACnBJ,KAAK,EAAG2F,KAAK,IAAK,CAChBC,MAAM,CAACC,SAAS,EAChB1F,UAAU,IAAIyF,MAAM,CAACzF,UAAU;IAC/B;IACA;IACAwF,KAAK,CAACG,OAAO,IAAI;MAAExB,eAAe,EAAErD;IAAW,CAAC,EAChDb,QAAQ,IAAIwF,MAAM,CAACxF,QAAQ,EAC3B,OAAOJ,KAAK,KAAK,UAAU,GAAGA,KAAK,CAAC2F,KAAK,CAAC,GAAG3F,KAAK;EAClD,IAEA2F,KAAK,IACLjI,KAAK,CAACqI,QAAQ,CAACC,IAAI,CACjB,OAAOvF,QAAQ,KAAK,UAAU,GAAGA,QAAQ,CAACkF,KAAK,CAAC,GAAGlF,QACrD,CAEO,CAAC;AAEhB,CAAC;;AAED;AACA;AACA;AACAV,eAAe,CAACkG,SAAS,GAAG,IAAI;AAEhC,MAAML,MAAM,GAAGM,uBAAU,CAACC,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTnC,QAAQ,EAAE,UAAU;IACpB,IAAI0C,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI;MAC3BC,MAAM,EAAE,SAAS;MACjBC,UAAU,EAAE;IACd,CAAC;EACH,CAAC;EACDnG,QAAQ,EAAE;IACR,IAAIgG,qBAAQ,CAACC,EAAE,KAAK,KAAK,IAAI;MAC3BC,MAAM,EAAE;IACV,CAAC;EACH,CAAC;EACDnG,UAAU,EAAE;IACViE,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC;AAEF,MAAMoC,SAAS,GAAG,IAAAC,sBAAU,EAAC1G,eAAe,CAAC;AAAC,IAAA2G,QAAA,GAAAC,OAAA,CAAAnI,OAAA,GAE/BgI,SAAS", "ignoreList": []}